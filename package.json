{"version": "2.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "tserve": "TAILWIND_MODE=watch vue-cli-service serve", "build:test:concurrently": "node bin/build-concurrently.js --mode test", "build:prod:concurrently": "node bin/build-concurrently.js --mode prod", "build:test:include": "node bin/build-include.js --mode test", "build:prod:include": "node bin/build-include.js --mode prod", "build:prod": "vue-cli-service build --mode prod", "build:test": "vue-cli-service build --mode test", "build:babel": "vue-cli-service build --mode babel"}, "dependencies": {"@antv/f6": "^0.0.15", "@chenfengyuan/vue-qrcode": "^2.0.0", "@vant/area-data": "^1.5.1", "@vant/compat": "^1.0.0", "@videojs-player/vue": "^1.0.0", "@videojs/http-streaming": "^3.10.0", "@vueuse/core": "^11.2.0", "add": "^2.0.6", "animate.css": "^4.1.1", "autoprefixer": "^9.8.8", "axios": "^0.21.1", "clipboard": "^2.0.8", "compressorjs": "^1.2.1", "core-js": "^3.6.5", "cropperjs": "^1.5.12", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "dom-to-image": "^2.6.0", "eruda": "^3.4.0", "gsap": "^3.13.0", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "iamor-lottery": "^1.0.2", "iamor-lottery-vue": "^1.1.3", "inobounce": "^0.2.1", "lodash": "^4.17.21", "macy": "^2.5.1", "postcss": "^7.0.39", "qrcode": "^1.5.4", "qs": "^6.11.2", "swiper": "^8.4.4", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "underscore": "^1.13.2", "vant": "^4.6.8", "videojs-contrib-hls": "^5.15.0", "vue": "^3.2.11", "vue-advanced-cropper": "^2.8.9", "vue-cal": "^4.8.1", "vue-router": "^4.0.0", "vue3-carousel": "^0.3.1", "vue3-danmaku": "^1.6.0", "vue3-perfect-scrollbar": "^2.0.0", "vue3-video-play": "^1.3.2", "vuex": "^4.1.0"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/lodash": "^4.14.184", "@types/node": "^20.8.10", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.9", "@vant/auto-import-resolver": "^1.0.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-e2e-cypress": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.2.47", "archiver": "^7.0.1", "babel-plugin-import": "^1.13.3", "cli-color": "^2.0.4", "lint-staged": "^9.5.0", "postcss-html": "^1.8.0", "postcss-px2rem-exclude": "^0.0.6", "sass": "1.26.5", "sass-loader": "^8.0.2", "single-line-log": "^1.1.2", "speed-measure-webpack-plugin": "^1.5.0", "str-format": "^1.0.1", "ts-import-plugin": "^1.5.5", "typescript": "^4.1.5", "unplugin-vue-components": "^0.25.2", "url-loader": "^4.1.1", "webpack-copy-on-plugin": "^1.0.1"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["git add"]}}