export interface Task {
  id: number;
  taskType: number;
  optWay: number;
  buyType: number;
  perOperateCount: number;
  perLotteryCount: number;
  lotteryCount: number;
  taskFinishCount: number;
  limit: number;
}
export interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}
export interface CardType {
  cardDesc: string;
  cardNumber: string;
  cardPassword: string;
  id: number;
  prizeName: string;
  showImg: string;
}
// sku 接口类型
export interface Skus {
  name?: string
}

// res 接口类型
export interface Res {
  data?: any,
  result?: boolean,
  errorMessage?: string
  msg?: string
  code?: number;
  message?: string;
}
// applyToken: string,
// api 接口类型
export interface ApisOpts {
  // 获取规则
  getRuleInfo: string,
  // 获取活动信息
  activityContent: string,
  // 发送验证码
  sendCode: string,
  // 获取用户资格
  getUserQualification: string,
  // 用户手机是否绑定过出生证号,用户是否领取过新客礼2
  checkNewGift2: string,
  // 上传和绑定出生证
  uploadAndBindBorn: string,
  // 获取新客礼2奖品信息
  getNewGift2PrizeInfo: string,
  // 校验奖品库存
  checkPrizeStock: string,
  // 领取新客奖品2
  sendNewGift2Prize: string,
  // 获取领奖记录
  getUserReceiveRecord: string,
  [propname: string]: any,
}
