<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
                <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>

        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            <div>{{ btn.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <vueDanmaku v-if="activityGiftRecords.length !== 0" ref="danmaku" v-model:danmus="activityGiftRecords" useSlot loop :channels="1" :speeds="150" class="danmaku">
      <template v-slot:dm="{ danmu }">
        <div class="winner">
          <span>恭喜{{ danmu.nickName }}抽中了{{ danmu.giftName }}</span>
        </div>
      </template>
    </vueDanmaku> -->
    <div class="select-hover" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
      <div class="wheel">
        <lz-lucky-wheel ref="myLucky" width="85vw" height="85vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
        <!-- <img :src="wheelImg" alt="" class="wheel-img" /> -->
      </div>
      <div class="figure">
        <div class="all-join-num">已有<span>0</span>人参与</div>
        <div class="draws-num" :style="furnishStyles.drawsNum.value">当前还有 <span>0</span> 次抽奖机会</div>
      </div>
    </div>
    <div class="sku select-hover" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
      <div class="sku-list-box" :style="furnishStyles.winnersBg.value" v-if="showSelect || isExposure === 1">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in skuList" :key="index">
            <img :src="item.skuMainPicture" alt="" />
            <div class="sku-text">{{ item.skuName }}</div>
            <div class="sku-price">￥{{ item.jdPrice }}</div>
          </div>
        </div>
      </div>
      <div class="load-more" @click="handleLoadMore" v-show="skuList.length>0">加载更多</div>
    </div>
    <div class="bottom-div">我也是有底线的哦~</div>
  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--    我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
    </VanPopup>
    <!--抽奖记录弹窗-->
    <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
      <DrawRecordPopup @close="showDrawRecord = false" ></DrawRecordPopup>
    </VanPopup>
    <!--    活动商品弹窗-->
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup :data="orderSkuList" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { inject, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import { showToast } from 'vant';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';

Swiper.use([Autoplay]);

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);

const showRule = ref(false);
const ruleTest = ref('');

const showMyPrize = ref(false);
const showGoods = ref(false);
const showOrderRecord = ref(false);
const showDrawRecord = ref(false);
// 订单状态
const orderRestrainStatus = ref(0);

const times = ref(0);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const isExposure = ref(1);
const skuList = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    event: () => {
      showRule.value = true;
    },
  },
  {
    name: '我的奖品',
    event: () => {
      showMyPrize.value = true;
    },
  },
  {
    name: '活动商品',
    event: () => {
      showGoods.value = true;
    },
  },
  {
    name: '我的订单',
    event: () => {
      showOrderRecord.value = true;
    },
  },
  {
    name: '抽奖记录',
    event: () => {
      showDrawRecord.value = true;
    },
  },
];

const activityGiftRecords = reactive([
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
]);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const myLucky = ref();
const startCallback = async () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    const _award = prizeInfo[index];
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  showAward.value = true;
};

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showGoods.value = false;
  showAward.value = false;
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    if (data.prizeList.length) {
      prizeInfo.splice(0);
      prizeInfo.push(...data.prizeList);
    }
    console.log('c', data);

    isExposure.value = data.isExposure;
    skuList.value = data.skuList;
    orderSkuList.value = data.orderSkuList;
    ruleTest.value = data.rules;
    orderRestrainStatus.value = data.orderRestrainStatus;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 加载更多
const handleLoadMore = () => {
  showToast('活动预览，仅供查看');
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeInfo.splice(0);
    prizeInfo.push(...activityData.prizeList);
    isExposure.value = activityData.isExposure;
    orderSkuList.value = activityData.orderSkuList;
    skuList.value.splice(0);
    skuList.value.push(...activityData.skuList);
    ruleTest.value = activityData.rules;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  if (activityGiftRecords.length > 4) {
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        direction: 'vertical',
        loop: true,
        slidesPerView: 5,
        loopedSlides: 8,
        centeredSlides: true,
      });
    });
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  height: 3.3rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }
  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.2rem;
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 0 0.2rem;
    height: 0.51rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.danmaku {
  width: 6.2rem;
  height: 1rem;
  margin: 0 auto;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  height: 0.65rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/168276/30/41149/4329/65683557F493dbec4/b7608a4a44724145.png') no-repeat;
  background-size: 100%;
  width: 5.2rem;
  padding-left: 0.15rem;
  padding-right: 0.4rem;
  color: #333;
  font-size: 0.24rem;
  margin: 0.1rem 0;
  span {
    margin-left: 0.8rem;
    width: 5.2rem;
    overflow: hidden;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}

.wheel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .wheel-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    object-fit: contain;
  }
}

.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  position: relative;
  .sku-list-box {
    background-size: 100%;
    background-repeat: no-repeat;
    width: 6.86rem;
    height: 10.15rem;
    margin: 0.5rem auto 0;
    padding-top: 0.8rem;
    .sku-list {
      width: 6.6rem;
      height: 8.2rem;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      display: flex;
      margin: 0.2rem auto 0.1rem auto;
      overflow: hidden;
      overflow-y: scroll;
      .sku-item {
        width: 3.22rem;
        margin-bottom: 0.1rem;
        background: rgb(255, 255, 255);
        border-radius: 0.2rem;
        overflow: hidden;
        img {
          display: block;
          width: 3.22rem;
          height: 3.22rem;
        }
        .sku-text {
          width: 3.4rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          font-size: 0.3rem;
          color: #262626;
          line-height: 0.4rem;
          height: 0.8rem;
          padding: 0 0.2rem;
          margin: 0.2rem 0 0.2rem;
          box-sizing: border-box;
        }
        .sku-price {
          font-size: 0.3rem;
          color: #ff5f00;
          padding: 0 0.2rem;
          margin-bottom: 0.2rem;
        }
      }
    }
  }
  .load-more {
    width: 3rem;
    height: 0.6rem;
    line-height: 0.6rem;
    text-align: center;
    background: #c36be6;
    border-radius: 0.2rem;
    color: white;
    position: absolute;
    bottom: 0.3rem;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
  }
}
.all-join-num {
  text-align: center;
  font-size: 0.24rem;
  margin-top: 1.5rem;
  margin-bottom: 0.2rem;
  color: #000;
}

.draws-num {
  display: flex;
  background: rgb(195, 107, 230);
  border: 0.02rem solid rgb(255, 255, 255);
  border-radius: 0.44rem;
  margin: 0.24rem 0.8rem 0px;
  align-items: center;
  justify-content: center;
  padding: 0.06rem 0px;
  font-size: 0.24rem;
  span {
    background: rgb(133, 59, 163);
    border-radius: 0.06rem;
    color: rgb(242, 214, 56);
    padding: 0.08rem;
    margin: 0 0.08rem;
  }
}

.draw-btn {
  width: 4rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
