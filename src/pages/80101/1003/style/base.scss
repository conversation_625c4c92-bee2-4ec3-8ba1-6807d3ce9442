.lz-multi-ellipsis--l2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.chat:before {
  content: '';
  @apply absolute h-3 w-3 top-1/2 -left-0.5 bg-white rotate-45 -translate-y-1/2;
}
.process {
  @apply absolute top-[0.48rem] left-[0.1rem];

  > li {
    @apply w-0.5 h-0.5 bg-red-500 rounded-full mb-[0.12rem];
  }
}
.circle {
  @apply rounded-full inline-block h-2 w-2 bg-red-500
}
.count-down {
  @apply bg-gradient-to-r from-[#E64D61FF] to-[#E85568FF] text-white font-light text-xs absolute rounded-2xl top-2 right-10 px-3.5 py-1;
}
.receive-btn {
  @apply bg-gradient-to-r from-[#f2270c] to-[#ff6420] text-white font-light px-3 py-1 rounded-2xl w-[1.5rem] text-center;
}
