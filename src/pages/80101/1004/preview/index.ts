import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decodata = {
  actBg: '',
  pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236651/6/7045/265113/658a8068Fb8339d04/6a4957ddeaa9d132.png',
  actBgColor: '#c82315',
  shopNameColor: '#fff',
  btnColor: '#fff9d7',
  btnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/232274/30/8844/4427/658a33f6Fa6daae84/9ef7cfe053d6408b.png',
  btnBorderColor: '',
  wheelBg: '//img10.360buyimg.com/imgzone/jfs/t1/235405/9/10299/133401/658a86fbF4c14b85a/30df18f1d730a323.png',
  drawBtn: '',
  wheelTextColor: '#333333',
  drawsNum: '#fef2e4',
  drawsNumBg: '//img10.360buyimg.com/imgzone/jfs/t1/238619/14/1222/6322/658a33f6Fb2734429/a7ed3989ea59702f.png',
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/224574/23/10871/47092/658a33f6F9445bf7e/ea3f829c139027fe.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/227162/37/9472/97293/658a33f6F98644521/e6dab1cb09b31b82.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/241750/22/986/10665/658a33f7F4ea6ce0e/6f2c04ffbf24af98.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/239774/3/1039/98022/658a33f7Fbbff5b5b/5fe767e0d0ba43cb.png',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '下单抽奖';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', _decodata);
  app.provide('isPreview', true);
  app.mount('#app');
});
