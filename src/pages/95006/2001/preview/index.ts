import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/72562/18/27395/57198/669dbbeaF87696e3c/686bd002ad96a242.png',
  actBgColor: '#0a862f',
  btnColor: '#0e822f',
  btnBg: '#ffffff',
  btnBorderColor: '#ffffff',
  cutDownBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/137012/1/42465/6275/65e80562F66c606e9/6ab10639ccdebab2.png',
  cutDownColor: '#e2231a',
  signButtonBg: '//img10.360buyimg.com/imgzone/jfs/t1/226731/40/20550/605/668654b0Fd467ee7b/e729a44288f2d8ba.png',
  signButtonColor: '#cc1729',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/42367/21/21621/47011/6698fcd8F098f4697/d5cf90562ab781e3.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/243469/7/14618/43849/6698fcf3F31996969/ea844fc9c0cb394a.png',
  ruleBtnBg: 'https: //img10.360buyimg.com/imgzone/jfs/t1/235481/36/20858/1427/66752ca9F28e9b68d/9bbe47e8d232e488.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/247902/35/15254/6718/6698fceaF5b57703f/5dfd6c79533db3e9.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '消费时间排名有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
