<template>
  <!-- 领取成功弹窗 -->
  <div class='box'>
    <div class='close-btn' @click.stop='closeDialog()'></div>
    <div class='handle-btn' @click='getOpenCardStatus()'></div>
  </div>
</template>

<script lang='ts' setup>
import { defineEmits, defineProps, ref, inject } from 'vue';
import { closeDialog } from '../ts/dialog';
import { BaseInfo } from '@/types/BaseInfo';
import { getOpenCardStatus } from '../ts/opencard';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

</script>

<style lang='scss' scoped>
.box {
  width: 7rem;
  height: 7rem;
  //background-image: url("../assets/dialog/no-member.png");

  .handle-btn {
    width: 4rem;
    height: 1.2rem;
    position: absolute;
    bottom: 0;
    left: 46%;
    transform: translateX(-50%);
  }
}
</style>
