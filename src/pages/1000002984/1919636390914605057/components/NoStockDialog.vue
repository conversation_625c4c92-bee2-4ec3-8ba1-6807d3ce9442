<template>
  <!-- 没有库存弹窗 -->
  <div class='box'>
    <div class='handle-btn' @click='gotoShopPage(baseInfo.shopId)'></div>
  </div>
</template>

<script lang='ts' setup>
import { inject, PropType } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

</script>

<style lang='scss' scoped>
.box {
  background-image: url("../assets/dialog/no-stock.png");

  .handle-btn {
    width: 5rem;
    height: 1.3rem;
    position: absolute;
    bottom: 2.7rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
