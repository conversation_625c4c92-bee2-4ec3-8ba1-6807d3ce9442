<template>
  <!-- 领取成功弹窗 -->
  <div class='box' :class="{'hasReceive-success':dialogName==='hesRecieveSuccessDialog'}">
    <div class='gift-view'>
      <img style='width: 1rem;max-height: 2.5rem' :src='giftInfo.rightsImg1' alt=''>
      <div class='gift-name'>{{ giftInfo.rightsName }}</div>
    </div>
    <div class='handle-group'>
      <div style='width: 2.3rem;height: 100%' @click='drawGift()'></div>
      <div style='width: 2.3rem;height: 100%' @click='closeDialog()'></div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { defineEmits, defineProps, ref, inject, PropType } from 'vue';
import { closeDialog, dialogName } from '../ts/dialog';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({ giftInfo: Object as PropType<any> });
const emits = defineEmits(['drawGift']);

const drawGift = () => {
  emits('drawGift', props.giftInfo);
};
</script>

<style lang='scss' scoped>

.hasReceive-success {
  padding-top: 2.5rem;
  background-image: url("../assets/dialog/hasReceive-success.png");

  .handle-group {
    bottom: .7rem;
  }
}

.box {
  padding-top: 2rem;
  background-image: url("../assets/dialog/success.png");

  .gift-view {
    width: 5.86rem;
    height: 3.58rem;
    margin: .15rem auto;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;
    background: {
      image: url("../assets/dialog/success-gift-bg.png");
      repeat: no-repeat;
      size: contain;
    };

    .gift-name {
      font-size: .24rem;
      color: #402825;
    }
  }

  .handle-group {
    width: 5.3rem;
    height: .8rem;
    position: absolute;
    bottom: .95rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
