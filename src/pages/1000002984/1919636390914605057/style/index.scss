@font-face {
  font-family: 'STZHONGS';
  src: url('https://lzcdn.dianpusoft.cn/fonts/STZHONGS/STZHONGS.woff');
}

* {
  box-sizing: border-box;
}

/*超过一行显示省略号*/
.one-line-omit {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

html {
  max-width: 750px;
  margin: 0 auto;
  background: #fff3e7;

  .background {
    min-height: 100vh;
    position: relative;
    padding-top: 7.3rem;
    padding-bottom: .5rem;
    background: #fff3e7 url("../assets/img/background.jpg") no-repeat;
    background-size: contain;

    .stock-view {
      position: absolute;
      top: 4.125rem;
      right: .7rem;
      width: 1.5rem;
      text-align: left;
      color: #402825;
      font-size: 0.28rem;
    }

    .side-btn {
      width: 1.2rem;
      height: .38rem;
      position: absolute;
      right: 0;
    }

    .home-handle-btn {
      width: 2.53rem;
      height: .65rem;
      line-height: .58rem;
      text-align: center;
      font-size: .28rem;
      margin: .1rem auto;
      color: #fffcf8;
      font-family: STZHONGS;
      background: {
        image: url("../assets/img/home-btn-bg.png");
        repeat: no-repeat;
        size: contain;
      };
    }

    .sku-view {
      width: 6.77rem;
      min-height: 5.35rem;
      margin: .3rem auto;
      padding: 1rem .2rem .2rem;
      border-radius: .2rem;
      position: relative;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: space-evenly;
      background: {
        color: #fffcf8;
        repeat: no-repeat;
        size: contain;
      };


      .sku-view-title {
        width: 1.2rem;
        position: absolute;
        left: 50%;
        top: .4rem;
        transform: translateX(-50%);
      }

      .sku-item {
        width: 3rem;
        height: 4rem;
        background-color: #ffefdf;
        border-radius: .15rem;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        flex-direction: column;
        margin-bottom: .15rem;

        .sku-img {
          width: 2.5rem;
        }

        .sku-name {
          font-size: .2rem;
          font-family: STZHONGS;
          font-weight: bold;
          color: #402825;
          width: 2.8rem;
          text-align: center;
        }

        .sku-handle-btn {
          width: 1.53rem;
          height: .37rem;
          line-height: .38rem;
          text-align: center;
          font-size: .2rem;
          margin: .1rem auto;
          color: #fffcf8;
          font-family: STZHONGS;
          background: {
            image: url("../assets/img/home-btn-bg.png");
            repeat: no-repeat;
            size: contain;
          };
        }
      }
    }
  }
}

.box {
  width: 6.5rem;
  height: 8rem;
  position: relative;
  background: {
    repeat: no-repeat;
    size: contain;
  };

  .close-btn {
    position: absolute;
    bottom: .2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.3rem;
    height: 0.7rem;
  }
}

.none-data-tip {
  text-align: center;
  font-size: .28rem;
  color: #222222;
  line-height: 4.2rem;
}
