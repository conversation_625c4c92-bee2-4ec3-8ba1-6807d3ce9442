import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
// const a = {
//   actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/74619/19/21614/37237/63049960Ea1d0dc87/64b56a3f863b9f4c.png',
//   pageBg: '',
//   actBgColor: '#df4226',
//   shopNameColor: '#000000',
//   btnColor: '#df4226',
//   btnBg: '#ffffff',
//   btnBorderColor: '#df4226',
//   ruleBg: '',
//   myPrizeBg: '',
//   myOrderBg: '',
//   cutDownBg: '',
//   cutDownColor: '#ffffff',
//   prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/55347/25/20723/5197/6306ed6cEe437d8e5/85039b02495827ad.png',
//   prizeNameColor: '#000000',
//   getPrizeBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/45668/31/21377/3750/63072ce9E149122d1/cafaa96f82228c46.png',
//   showSkuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/24074/17/18783/12133/63073c46E24642353/913ac5243642d81e.png',
//   shortRuleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/155748/38/29783/10145/63089888E70a9d848/5d28a55ab2a519ce.png',
//   btnToShop: 'https://img10.360buyimg.com/imgzone/jfs/t1/192572/12/27705/4069/6303376fE584f4c53/c1196beb18066383.png',
//   mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/192319/27/28174/37339/6305d73cE32f4373c/5813e6d12c9cfc1c.jpg',
//   cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/192319/27/28174/37339/6305d73cE32f4373c/5813e6d12c9cfc1c.jpg',
//   h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/192319/27/28174/37339/6305d73cE32f4373c/5813e6d12c9cfc1c.jpg',
// };
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
