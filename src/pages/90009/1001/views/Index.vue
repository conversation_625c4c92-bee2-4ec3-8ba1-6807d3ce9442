<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <div class="header-content" :style="limitOrder === 0 ? {marginBottom:'6.62rem'} : {}">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div class="header-btn" v-click-track="btn.clickCode" :style="furnishStyles.headerBtn.value" v-for="(btn, index) in btnList" :key="index" @click="btn.event">
            {{ btn.name }}
          </div>
          <div class="header-btn" v-if="limitOrder === 1" v-click-track="'hdsp'" :style="furnishStyles.headerBtn.value" @click="showGoodsPopup">活动商品</div>
        </div>
      </div>
    </div>
    <div class="hotZoneBox">
      <img class="hotZone" :src="furnish.hotZoneBg" alt="" />
      <div class="hotBtn" v-for="(item, index) in showData" :key="index" v-click-track="`btn${index+1}`" :style="item.style" @click="toLink(item)"></div>
      <div class="remainBox">奖品剩余{{prizeRemains}}份</div>
    </div>
    <div class="bottomBox">
      <div class="bottomRule" :style="furnishStyles.shortRuleBg.value"></div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showLimit">
    <Threshold @close="showLimit = false"></Threshold>
  </VanPopup>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showMyPrize">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum"></MyPrize>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup @close="showAward = false"></AwardPopup>
  </VanPopup>
  <!-- 活动商品弹窗-->
  <VanPopup teleport="body" v-model:show="showGoods">
    <GoodsPopup :data="orderSkuList" @close="showGoods = false" :orderSkuisExposure="orderSkuisExposure"></GoodsPopup>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import Threshold from '../components/Threshold.vue';
import AwardPopup from '../components/AwardPopup.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import CopyCard from '../components/CopyCard.vue';
import dayjs from 'dayjs';
import MyPrize from '../components/MyPrize.vue';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

// 店铺名称
const shopName = ref(baseInfo.shopName);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品弹窗
const showMyPrize = ref(false);
// 中奖弹窗
const showAward = ref(false);
// 曝光商品
const showGoods = ref(false);
const showData = ref([]);
// 活动商品   是否开启订单限制 0 不开启 1 开启
const limitOrder = ref(0);
// 奖品剩余份数
const prizeRemains = ref(0);

const showGoodsPopup = () => {
  showGoods.value = true;
};

// 奖品列表
const prizeList = ref({
  prizeId: 0,
  prizeImg: '',
  prizeName: '',
  prizeType: 0,
  remainCount: 0,
  sendTotalCount: 0,
  // 奖品状态 -1 不满足条件 0 未领取 1 领取成功 2 取消报名 3 发放奖奖品 4剩余份数不足
  status: 0,
  receivePrizeId: 0,
});
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};

const orderSkuList = ref<Sku[]>([]);
const pageNum = ref(1);
const pagesAll = ref(0);

// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  prizeId: '',
  userReceiveRecordId: '',
});
// 展示门槛显示弹框
const showLimit = ref(false);

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};
// 按钮列表
const btnList: {
  name: string;
  clickCode: string;
  event?: () => void;
}[] = [
  {
    name: '活动规则',
    clickCode: 'hdgz',
    event: () => {
      showRulePopup();
    },
  },
  {
    name: '我的奖品',
    clickCode: 'wdjp',
    event: () => {
      showMyPrize.value = true;
    },
  },
];

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  prizeImg: '',
});
// 展示卡密
const showCardNum = (result: any) => {
  console.log(result, 'result');
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showMyPrize.value = false;
  copyCardPopup.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
  userImg: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

const getStatePrizeList = async () => {
  try {
    const { data } = await httpRequest.post('/90009/activity');
    prizeList.value = data.prize;
    prizeList.value.receivePrizeId = data.receivePrizeId;
    orderSkuisExposure.value = data.orderSkuisExposure;
    limitOrder.value = data.limitOrder;
    prizeRemains.value = data.prize.remainCount;
  } catch (error: any) {
    console.error(error);
  }
};

// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    showData.value = furnish.hotZoneList;
    showData.value.forEach((item: any) => {
      const style: any = {};
      style.width = `${((item.width * 2) / 100) * 0.9186}rem`;
      style.height = `${((item.height * 2) / 100) * 0.9186}rem`;
      style.position = 'absolute';
      style.top = `${((item.top * 2) / 100) * 0.9186}rem`;
      style.left = `${((item.left * 2) / 100) * 0.9186}rem`;
      item.style = style;
    });
    console.log(showData.value, 'hotZoneList');
    console.log(showData.value[0].style);
    await Promise.all([getStatePrizeList()]);
    closeToast();
    if (baseInfo.thresholdResponseList.length) {
      if (baseInfo.thresholdResponseList.find((item) => item.thresholdCode === 1409 || item.thresholdCode === 1401)) {
        showLimit.value = true;
      }
    }
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};
// 领取奖品
const saveReceivePrize = async (prizeId: number, receivePrizeId: number) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90009/receivePrize', { prizeId, receivePrizeId });
    closeToast();
    award.value = data;
    showAward.value = true;
    await init();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
    console.log(error);
  }
};
// 报名领取
const receivePrize = async () => {
  if (baseInfo.thresholdResponseList.length) {
    if (baseInfo.thresholdResponseList.find((item) => item.thresholdCode === 1409) && baseInfo.memberLevel === 0) {
      showToast('请先加入会员后参与活动');
      return;
    }
    if (baseInfo.thresholdResponseList.find((item) => (item.thresholdCode >= 1402 && item.thresholdCode <= 1408) || item.thresholdCode === 1419)) {
      showToast('暂无满足条件的订单，快去下单吧');
      return;
    }
    if (baseInfo.thresholdResponseList.find((item) => (item.thresholdCode === 1))) {
      showToast('抱歉，活动暂未开始');
      return;
    }
    if (baseInfo.thresholdResponseList.find((item) => (item.thresholdCode === 2))) {
      showToast('抱歉，活动已结束');
      return;
    }
    showLimit.value = true;
    return;
  }
  if (prizeList.value.status === 1) {
    showToast('您已领取过该奖品~');
    return;
  }
  if (prizeList.value.status === 4) {
    showToast('手慢了，奖品已领光~');
    return;
  }
  await saveReceivePrize(prizeList.value.prizeId, prizeList.value.receivePrizeId);
};

const toLink = (item) => {
  if (item.radio === 1) {
    if (item.url.includes('shopmember.m.jd.com/shopcard')) {
      window.location.href = `${item.url}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
    } else {
      window.location.href = `${item.url}&returnUrl=${encodeURIComponent(`${window.location.href}`)}`;
    }
  } else {
    receivePrize();
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0;
    margin-bottom: 6rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.36rem;
    height: 0.52rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-size: 100%;
  }
}
.hotZoneBox {
  width: 6.89rem;
  margin: 0 auto;
  position: relative;
}
.hotZone {
  width: 100%;
}

//.hotBtn {
//  background-color: #000;
//}

.remainBox {
  position: absolute;
  bottom: 0.14rem;
  left: 4.4rem;
  font-size: 0.18rem;
}

.bottomBox {
  padding: 0.1rem 0 0.5rem;
}

.bottomRule {
  width: 7.09rem;
  height: 9.08rem;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
