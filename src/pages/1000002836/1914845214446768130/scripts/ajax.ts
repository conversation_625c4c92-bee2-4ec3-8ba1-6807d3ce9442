import { httpRequest } from '@/utils/service';
import { showLoadingToast, closeToast } from 'vant';

export const getUserInfo = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/dz/1914845214446768130/user');
    closeToast();
    return data;
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
  return '';
};
export const orderTraceability = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/dz/1914845214446768130/orderTraceability');
    closeToast();
    return data;
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
  return '';
};
