import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/236011/37/15861/115311/661f6d26Ffaebc373/3875f333d09b8b93.png',
  actBg: '',
  actBgColor: '',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/108757/40/44301/3168/661d1f29F4ed1fa7a/c5fa2c61f2071010.png',
  successPageBg: '//img10.360buyimg.com/imgzone/jfs/t1/150994/19/37632/100011/661f6d2fFf2fde3d1/a88b5492187c20d7.png',
  noPrizePop: '//img10.360buyimg.com/imgzone/jfs/t1/167657/21/44183/22311/661e1b13F55f2d81d/c5adbf65814f5225.png',
  noPrizePopLink: '',
  canNotPop: '//img10.360buyimg.com/imgzone/jfs/t1/235808/17/14264/44567/661e1b12F11493583/e309c4fd27935088.png',
  canNotPopLink: '',
  mainPageText: '',
  successPageText: '',
  rulePop: '//img10.360buyimg.com/imgzone/jfs/t1/219881/28/40071/16222/661e1737F82f17ede/8e17fcca2e4df743.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/248939/20/7540/20778/661f6d27F1ce8052d/a065ac74c049a5d1.jpg',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/110045/33/48933/7044/661f6d27F1d976d02/5e9a88aa057a02a5.jpg',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/228522/31/16662/27615/661f6d29Ffb698eaf/b432b4e8eb1f6298.jpg',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员锁权礼';
  console.log(document.title, 'activityData======');
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
