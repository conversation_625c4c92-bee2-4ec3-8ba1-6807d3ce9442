<template>
  <div class="myprize-bk" v-if="!prizeList.length">
    <div class="content">
      <div class="list">
        <div class="no-data">
          暂无数据
        </div>
      </div>
    </div>
    <div class="close" @click="closeMyPrize"></div>
  </div>
  <div v-else class="myprize-bk">
    <div class="content">
<!--      <div v-if="!hasAddress"  class="address-btn" @click="saveAddress">填写地址</div>-->
<!--      <div v-else class="address-btn" @click="previewAddress">查看地址</div>-->
      <div class="list">
        <div class="item" v-for="(item, index) in prizeList" :key="index">
          <div class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
          <div class="prize-name">{{ item.prizeName }}</div>
          <div class="point">{{ item.point }}</div>
          <div class="status">
            <span style="margin-right: 0.1rem" @click='handleEditAddress(item)'>{{ getStatus(item.status) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="close" @click="closeMyPrize"></div>
  </div>
<!--  <VanPopup teleport="body" v-model:show="addressPopup">-->
<!--    <AddressPopup v-if="addressPopup" :isPreview="isPreview" :echoInfo="form" @close="closeAddress"></AddressPopup>-->
<!--  </VanPopup>-->
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { reactive, ref } from 'vue';
import dayjs from 'dayjs';
import AddressPopup from './AddressPopup.vue';

const emits = defineEmits(['close']);
const addressPopup = ref(false);
const isPreview = ref(false);

const form = reactive({
  address: '',
  city: '',
  county: '',
  hasAddress: true,
  mobile: '',
  postalCode: '',
  province: '',
  realName: '',
});
const closeMyPrize = () => {
  emits('close');
};
// const previewAddress = (item: any) => {
//   isPreview.value = true;
//   console.log(isPreview.value);
//   addressPopup.value = true;
// };
//
// const saveAddress = () => {
//   isPreview.value = false;
//   addressPopup.value = true;
// };

const handleEditAddress = (item:any) => {
  Object.keys(form).forEach((key) => {
    form[key] = item[key];
  });
  if (item.status === 1) {
    isPreview.value = false;
    addressPopup.value = true;
  } else if (item.status === 2) {
    isPreview.value = true;
    addressPopup.value = true;
  }
};

const getStatus = (val:number) => {
  // if (val === 1) {
  //   return '填写地址';
  // }
  // if (val === 2) {
  //   return '修改地址';
  // }
  // if (val === 3) {
  //   return '待发货';
  // } if (val === 4) {
  //   return '已发货';
  // }
  if (val === 3 || val === 4) {
    return '领取成功';
  }
  return '领取失败';
};

// 是否填写地址
const hasAddress = ref(false);

const prizeList = ref<any[]>([]);

const getMyPrize = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/yiLiCBA/myPrize');
    console.log('data', data);
    prizeList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};
getMyPrize();

const addressMessage = ref({
  address: '',
  city: '',
  county: '',
  hasAddress: true,
  mobile: '',
  postalCode: '',
  province: '',
  realName: '',
});

// const getUserAddressInfo = async () => {
//   try {
//     showLoadingToast({
//       message: '加载中...',
//       forbidClick: true,
//       duration: 0,
//     });
//     // const { data } = await httpRequest.post('/nestleLockRight/getUserAddressInfo');
//     // hasAddress.value = data.hasAddress;
//     // Object.keys(form).forEach((key) => {
//     //   form[key] = data[key];
//     // });
//     // addressMessage.value = data;
//     closeToast();
//   } catch (error: any) {
//     closeToast();
//     showToast(error.message);
//   }
// };
// getUserAddressInfo();

// const closeAddress = (isSuccess: boolean) => {
//   if (isSuccess) {
//     getMyPrize();
//     // getUserAddressInfo();
//   }
//   addressPopup.value = false;
// };

</script>

<style scoped lang="scss" >
::-webkit-scrollbar {
  display: none;
}
.myprize-bk {
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/262701/1/1863/71086/6768df4aF3253c0d4/1c01bdb01d124636.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.5rem;
  height: 8rem;
  padding-top: 1.6rem;
  padding-bottom: 0.3rem;
  position: relative;
  .content {
    width: 5.92rem;
    height: 2.7rem;
    //background: red;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: 3.55rem;
    //.address-btn{
    //  margin: 0 auto 0.2rem;
    //  width: 3.02rem;
    //  height: 0.75rem;
    //  text-align: center;
    //  background: url("../assets/btn.png") no-repeat;
    //  background-size: 100%;
    //  padding: 0.2rem 0 0;
    //  color:#fff;
    //  font-size: 0.3rem;
    //}
    .list {
      width: 5.92rem;
      height: 2.7rem;
      overflow-y: auto;
      //padding:0.2rem 0 0;
      .item {
        width: 100%;
        display: flex;
        align-items: center;
        font-size: 0.24rem;
        text-align: center;
        margin: 0.2rem 0;
        justify-content: space-around;
        .prize-name {
          flex: 1;
        }
        .point {
          flex: 1;
        }
        .time {
          flex: 1;
        }
        .status {
          flex: 1;
          color: #3646ab;
          font-weight: 600;
        }
      }
    }
    .no-data {
      font-size: 0.4rem;
      color: #797da5;
      text-align: center;
      margin: 1rem auto 0;
    }
  }
}
.close {
  width: 0.9rem;
  height: 0.9rem;
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: 0.3rem;
  //background: #2d8cf0;
}
</style>
