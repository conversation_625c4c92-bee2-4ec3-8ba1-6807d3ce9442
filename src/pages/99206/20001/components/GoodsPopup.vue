<template>
  <CommonDrawer title="活动商品" @close="emits('close')">
    <div class="h-[40vh] px-4 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-gray-400 text-sm flex justify-center pb-4" v-if="orderSkuisExposure !== 0">
        <div class="grid grid-cols-2 gap-2">
          <div v-for="(item, index) in skuList.length ? skuList : data" class="bg-white py-2 px-3.5" :key="index" @click="goToSku(item)">
            <div class="flex justify-center">
              <img class="w-32 h-32" :src="item.skuMainPicture" alt="" />
            </div>
            <div class="text-xs mt-5 lz-multi-ellipsis--l2" v-text="item.skuName"></div>
            <div class="text-red-500 text-xs mt-3">¥ <span v-text="item.jdPrice"></span></div>
          </div>
          <!--          <div class="w-[6.34rem] text-gray-400 text-xs text-center my-1" v-if="skuList.length > 4 || data.length > 4">—— 没有更多了 ——</div>-->

          <div class="text-gray-400 text-xs text-center my-1" v-if="isPreview">
            <div class="more-btn" v-if="isPreview && data.length && data.length !== total" @click="loadMorePreview">点我加载更多</div>
          </div>
          <div class="text-gray-400 text-xs text-center my-1" v-else>
            <div class="more-btn" v-if="!isPreview && pageNum < pagesAll" @click="loadMore">点我加载更多</div>
          </div>
        </div>
      </div>
      <div v-else class="no-data">
        活动商品为本店全部商品
        <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
      </div>
    </div>
  </CommonDrawer>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps(['data', 'orderSkuList', 'orderSkuisExposure']);
const emits = defineEmits(['close']);
const skuList = ref<any[]>([]);
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
const cursorNum = ref(0);
const pageSize = ref(10);
// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    let queryData = {};
    if (props.orderSkuisExposure === 2) {
      queryData = {
        type: 1,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        cursorNum: cursorNum.value,
      };
    } else {
      queryData = {
        type: 1,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
      };
    }
    const { data } = await httpRequest.post('/99206/getExposureSkuPage', queryData);
    skuList.value.push(...data.records);
    total.value = data.total;
    pagesAll.value = data.pages;
    if (props.orderSkuisExposure === 2) {
      cursorNum.value = data.current;
    }
    closeToast();
  } catch (error) {
    closeToast();
  }
};
const goToSku = (item:any) => {
  if (isPreview) {
    showToast('活动预览,仅供查看');
    return;
  }
  gotoSkuPage(item.skuId);
};

const loadMore = async () => {
  pageNum.value++;
  await getSkuList();
};
const loadMorePreview = () => {
  showToast('活动预览,仅供查看');
};
if (!isPreview) {
  getSkuList();
} else {
  watch(props.data, () => {
    skuList.value = props.data;
  });
}
</script>

<style scoped lang="scss">
.title {
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
}
.no-data {
  text-align: center;
  padding-top: 3rem;
  font-size: 0.3rem;
  .btn {
    width: 2.4rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    color: white;
    font-size: 0.3rem;
    border-radius: 0.1rem;
    background-color: #ff9900;
    margin: 0.3rem auto;
  }
}
.more-btn {
  width: 1.8rem;
  height: 0.5rem;
  font-size: 0.2rem;
  color: #fff;
  background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
  background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 0.3rem;
}
.my-1 {
  width: 6.86rem;
}
</style>
