<script setup lang="ts">
import { defineProps } from 'vue';

const props = defineProps(['dialogImgList', 'current', 'dialogBtnFunction']);

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

</script>

<template>

  <div class="container-dialog">
    <img class="container-dialog-bg" :src="dialogImgList[current].bg" alt="">
    <div class="container-dialog-close" @click="close"></div>
    <div v-if="dialogImgList[current].textBg" class="container-dialog-text">
      <img :src="dialogImgList[current].textBg" alt="">
    </div>
    <img
      v-if="dialogImgList[current].btn"
      :class="dialogImgList[current] === 'info' ? 'container-dialog-InfoBtn' : 'container-dialog-btn'"
      :src="dialogImgList[current].btn"
      @click="dialogBtnFunction[current]"
      alt="">
    <slot></slot>
  </div>

</template>

<style scoped lang="scss">
.container {
  &-dialog {
    position: relative;
    width: 6.15rem;
    height: 7.71rem;
    &-bg {
      width: 100%;
      height: 100%;
    }
    &-close {
      width: 0.88rem;
      height: 0.88rem;
      position: absolute;
      z-index: 99999;
      top: 6.8rem;
      left: 50%;
      transform: translateX(-50%);
    }
    &-btn {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 1.45rem;
      width: 2.81rem;
      height: 0.72rem;
    }
    &-InfoBtn {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 1.35rem;
      width: 2.81rem;
      height: 0.72rem;
    }
    &-text {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 1.4rem;
      height: 5.1rem;
      overflow-y: scroll;
      padding: 0 .6rem;
      img {
        width: 4.5rem;
      }
    }
  }
}

</style>
