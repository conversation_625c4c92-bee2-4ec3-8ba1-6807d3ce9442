<template>
  <popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false">
    <div class="box">
      <div class="dialog">
        <div class="dialog-title">领取成功!</div>
        <img class="gift-img" :src="prizeInfo.prizeImg" alt="" />
        <div class="gift-tips">恭喜您获得{{ prizeInfo.prizeName }}</div>
        <template v-if="prizeInfo.prizeType === 3">
          <div class="save-btn" @click="handleSave">填写地址</div>
        </template>
        <template v-if="prizeInfo.prizeType === 7">
          <div class="key-box">
            <div class="key">{{ prizeInfo.giftCardCode }}</div>
            <div class="copy-btn" :copy-text="prizeInfo.giftCardCode">复制</div>
          </div>
        </template>
      </div>
      <icon @click="handleClose" class="close-btn" name="close" size="44" color="#fff"> </icon>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref, PropType } from 'vue';
import { Popup, Icon, showToast } from 'vant';
import type { BaseInfo } from '@/types/BaseInfo';
import Clipboard from 'clipboard';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  prizeInfo: {
    type: Object as PropType<any>,
    required: true,
    default: () => ({}),
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog', 'saveAddress']);
const handleClose = () => {
  emits('closeDialog');
};
const handleSave = () => {
  emits('saveAddress');
};
const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>
<style lang="scss" scoped>
.box {
  padding-bottom: 1rem;
  position: relative;
  .close-btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0.1rem;
  }
  .dialog {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/206352/34/36170/12280/67205eb9F62c53f27/02f7dc0016f6bc37.png) no-repeat center center;
    background-size: contain;
    width: 5.88rem;
    height: 6.72rem;
    box-sizing: border-box;
    padding: 0.5rem 0.35rem 0;
    position: relative;
    text-align: center;
    text-align: center;
    .dialog-title {
      font-size: 0.6rem;
      color: #00047f;
      font-family: 'FZLTZHK--GBK1-0';
    }
    .gift-img {
      width: 100%;
      height: 3rem;
      object-fit: contain;
      margin: 0.3rem auto;
    }
    .key-box {
      margin-top: 0.1rem;
      background-color: #ffd26b;
      box-shadow: 0rem 0.1rem 0.1rem 0rem rgba(183, 183, 183, 0.47), inset 0rem 0.03rem 0.08rem 0rem #ffffff;
      border-radius: 0.05rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.05rem;
      box-sizing: border-box;
      .key {
        flex: 1;
        background-color: #ffffff;
        box-shadow: inset 0rem 0.01rem 0.03rem 0rem #ccc0a1;
        border-radius: 0.05rem;
        font-size: 0.22rem;
        color: #7a5021;
        padding: 0.12rem 0;
        box-sizing: border-box;
      }
      .copy-btn {
        margin: 0 0.2rem;
        font-size: 0.22rem;
        color: #7a5021;
        max-width: 0.5rem;
      }
    }
    .gitf-tips {
      font-size: 0.27rem;
      color: #333333;
    }
    .save-btn {
      width: 2.81rem;
      background-image: linear-gradient(180deg, #ffecc0 0%, #ffd676 100%), linear-gradient(#ffd26b, #ffd26b);
      background-blend-mode: normal, normal;
      box-shadow: 0rem 0.03rem 0rem 0rem #ffd164, 0rem 0.1rem 0.1rem 0rem rgba(183, 183, 183, 0.47), inset 0rem 0.03rem 0.08rem 0rem #ffffff;
      border-radius: 0.36rem;
      border: solid 0.01rem rgba(255, 255, 255, 0.63);
      font-family: FZLTHK--GBK1-0;
      font-size: 0.37rem;
      color: #7a5021;
      line-height: 1;
      padding: 0.2rem 0;
      box-sizing: border-box;
      margin: 0.3rem auto;
    }
  }
}
</style>
