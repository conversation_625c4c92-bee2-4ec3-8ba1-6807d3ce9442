<template>
  <popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @closed="handleClose">
    <div class="box">
      <div class="dialog">
        <div class="dialog-title">填写信息</div>
        <div class="row">
          <div>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</div>
          <input type="text" v-model="form.realName" placeholder="请输入姓名" maxlength="10" />
        </div>
        <div class="row">
          <div>电&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;话：</div>
          <input type="text" v-model="form.mobile" placeholder="请填写手机号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" />
        </div>
        <div class="row">
          <div>地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;区：</div>
          <input type="text" v-model="addressCode" readonly="true" placeholder="选择省/市/区" @click="addressSelects = true" />
        </div>
        <div class="row">
          <div>详细地址：</div>
          <input type="text" v-model="form.address" placeholder="请填写地址" maxlength="30" />
        </div>
        <div class="row">
          <div>邮政编码：</div>
          <input v-model="form.addressCode" oninput="value=value.replace(/[^\d]/g,'')" maxlength="6" placeholder="请输入邮政编码" />
        </div>
        <div class="submit-btn" @click="checkForm">确认提交</div>
      </div>
      <!--地址选择-->
      <Popup v-model:show="addressSelects" teleport="#app" position="bottom">
        <VanArea title="请输入详细地址" :area-list="areaList" @confirm="confirmAddress" @cancel="onCancel" />
      </Popup>
      <icon @click="handleClose" class="close-btn" name="close" size="44" color="#fff"> </icon>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref, reactive, watch } from 'vue';
import { Popup, Icon, Area, showToast } from 'vant';
import type { BaseInfo } from '@/types/BaseInfo';
import { areaList } from '@vant/area-data';
import { containsEmoji, containsSpecialChars, isPhoneNumber, validateDataWithRules } from '@/utils/platforms/validator';
import { writeAddress } from '../script/ajax';
import { log } from 'console';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  id: {
    type: String,
    required: true,
    default: '',
  },
  addressInfo: {
    type: Object,
    default: () => ({}),
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const form = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '', // 区
  addressCode: '',
  address: '',
});
const handleClose = () => {
  Object.assign(form, {
    realName: '',
    mobile: '',
    province: '',
    city: '',
    county: '', // 区
    addressCode: '',
    address: '',
  });
  emits('closeDialog');
};
const addressCode = ref('');

const addressSelects = ref(false);
// 关闭三联地址框
const onCancel = () => {
  addressSelects.value = false;
};
// 确认三联地址信息
const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList?.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
  addressCode.value = addressItemList.selectedOptions.map((item: any) => item.text).join('/');
};
const ruleValidate = {
  realName: [
    {
      required: true,
      message: '请输入姓名',
    },
    {
      validator: containsSpecialChars,
      message: '姓名不能包含特殊字符',
    },
    {
      validator: containsEmoji,
      message: '姓名不能包含表情',
    },
  ],
  mobile: [
    {
      required: true,
      message: '请输入电话号码',
    },
    {
      validator: isPhoneNumber,
      message: '请输入正确的电话号码',
    },
  ],
  province: [
    {
      required: true,
      message: '请选择省/市/区',
    },
  ],
  address: [
    {
      required: true,
      message: '请输入详细地址',
    },
    {
      validator: containsEmoji,
      message: '详细地址不能包含表情',
    },
  ],
  addressCode: [
    {
      required: true,
      message: '请输入邮政编码',
    },
  ],
};
const checkForm = async () => {
  const valid = validateDataWithRules(ruleValidate, form);
  if (!valid) return;
  const res = await writeAddress({ ...form, id: props.id });
  if (res) {
    showToast('保存成功~');
    emits('closeDialog');
  }
};

watch(
  () => props.addressInfo,
  (newVal) => {
    if (newVal.mobile) {
      Object.assign(form, { ...props.addressInfo });
      addressCode.value = `${form.province}/${form.city}/${form.county}`;
    }
  },
  {
    deep: true,
  },
);
</script>
<style lang="scss" scoped>
.box {
  padding-bottom: 1rem;
  position: relative;
  .close-btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0.1rem;
  }
  .dialog {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/206352/34/36170/12280/67205eb9F62c53f27/02f7dc0016f6bc37.png) no-repeat center center;
    background-size: contain;
    width: 5.88rem;
    height: 6.72rem;
    box-sizing: border-box;
    padding: 0.5rem 0.35rem 0;
    position: relative;
    font-size: 0.27rem;
    text-align: center;
    .dialog-title {
      font-size: 0.6rem;
      color: #00047f;
      font-family: 'FZLTZHK--GBK1-0';
      margin-bottom: 0.3rem;
    }
    .row {
      display: flex;
      align-items: center;
      border-bottom: 0.02rem solid #968f80;
      padding: 0 0.2rem 0.11rem;
      margin-top: 0.29rem;
      color: #1c1c1c;
      div {
        font-weight: bold;
        min-width: 1rem;
        text-align: justify;
      }
      input {
        border: none;
        background: none;
        width: 3rem;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers，webkit内核浏览器 */
        color: #8d877b;
        font-size: 0.27rem;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #8d877b;
        font-size: 0.27rem;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #8d877b;
        font-size: 0.27rem;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10+ */
        color: #8d877b;
        font-size: 0.27rem;
      }
    }
  }
  .submit-btn {
    width: 2.81rem;
    background-image: linear-gradient(180deg, #ffecc0 0%, #ffd676 100%), linear-gradient(#ffd26b, #ffd26b);
    background-blend-mode: normal, normal;
    box-shadow: 0rem 0.03rem 0rem 0rem #ffd164, 0rem 0.1rem 0.1rem 0rem rgba(183, 183, 183, 0.47), inset 0rem 0.03rem 0.08rem 0rem #ffffff;
    border-radius: 0.36rem;
    border: solid 0.01rem rgba(255, 255, 255, 0.63);
    font-family: FZLTHK--GBK1-0;
    font-size: 0.37rem;
    color: #7a5021;
    line-height: 1;
    padding: 0.2rem 0;
    box-sizing: border-box;
    margin: 0.3rem auto;
  }
}
</style>
