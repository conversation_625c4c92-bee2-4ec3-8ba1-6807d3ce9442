<template>
  <div :class="`info-phto-${photoStyle}`">
    <div :class="`info-cover-view-${photoStyle}`" @click="handleOpenPhotoPage(photoInfo)">
      <div class="swiper">
        <div class="swiper-wrapper" style="display: flex; align-items: center">
          <div class="swiper-slide" v-for="(item, index) in photoInfo.workImageUrl" :key="index">
            <div class="prizeClass" style="display: flex; justify-content: center; align-items: center; min-height: 3rem">
              <img :src="item" alt="" style="width: 6rem; object-fit: contain" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div :class="`info-cover-view-${photoStyle}`" @click="handleOpenPhotoPage(photoInfo)">
      <img class="info-cover-img" :src="photoInfo.workImageUrl" />
    </div> -->
    <div class="info-photo-name" @click="handleOpenPhotoPage(photoInfo)">{{ photoInfo.workTitle }}</div>
    <div class="info-photo-desc">{{ photoInfo.workDescription }}</div>
    <div class="info-user-info">
      <div class="info-nick-img" :style="{ backgroundImage: `url(${photoInfo.headerImg})` }"></div>
      <div class="info-nick-name">{{ photoInfo.nickName }}</div>
      <div class="info-praise-view">
        <img style="width: 0.46rem; height: 0.41rem; margin-right: 0.05rem" :src="photoInfo.upWork ? '//img10.360buyimg.com/imgzone/jfs/t1/271306/35/23584/1330/68034d66F11d1d097/6355b49d7174e492.png' : '//img10.360buyimg.com/imgzone/jfs/t1/272673/30/25333/2649/68074c80Fd63ae695/212a1dc5c5f8ac98.png'" @click="task(photoInfo)" />
        <div class="info-praise-num">{{ photoInfo.popularity }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref, toRefs, defineProps, defineExpose, defineEmits, inject, onMounted, nextTick, watch } from 'vue';
import { Checkbox as VanCheckbox, Button as VanButton, Popup as VanPopup, Toast, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import Swiper, { Pagination } from 'swiper';
import 'swiper/swiper-bundle.css'; // 引入Swiper样式

const delayToast = async (e: string) => {
  setTimeout(() => {
    Toast(e);
  }, 1000);
};

const loveIconCom = ref();

// 作品审核状态对应的图标 0:待审核 1：审核通过 2：审核未通过
const EXAMINE = {
  0: 'https://img10.360buyimg.com/imgzone/jfs/t1/134434/18/36560/4467/645efaefFd138391b/370b0eba8251c53e.png',
  2: 'https://img10.360buyimg.com/imgzone/jfs/t1/129193/40/32088/4844/645efaefFfe0782b4/77c2ba04f6d7f14b.png',
};
const isShow = ref(false);
const props = defineProps({
  photoInfo: {
    type: Object,
    required: true,
  },
  isDisLove: {
    type: Boolean,
    required: false,
    default: false,
  },
  isCheckCancel: {
    type: Boolean,
    required: false,
    default: false,
  },
  isMyPush: {
    type: Boolean,
    required: false,
    default: false,
  },
  photoStyle: {
    type: Number,
    required: false,
    default: 0,
  },
});
const { photoInfo } = toRefs(props);
const { isCheckCancel } = toRefs(props);
const { isMyPush } = toRefs(props);
const { isDisLove } = toRefs(props);

// const postInfo = inject('postInfo') as string;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
// let lock = false;
const emits = defineEmits(['handleOpenPhotoPage', 'handleCancelLove', 'handleTaskLove', 'close']);
const handleOpenPhotoPage = (photoId: string) => {
  // setTimeout(() => {
  console.log('photoId', photoId);
  // }, 500);
};
const close = () => {
  emits('close');
};
const task = async (photoInfo: any) => {
  if (isDisLove.value) {
    console.log(isDisLove.value);
    return;
  }
  try {
    const config = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    };
    const { data, message } = await httpRequest.post('/dz/1912668770678595586/upWorks', { workId: photoInfo.id }, {}, { isLoading: true });
    if (message) {
      showToast(message);
    } else {
      photoInfo.upWork = true;
      photoInfo.popularity += 100;
      emits('handleTaskLove', photoInfo.id);
    }
  } catch (error) {
    console.log(error);
    showToast(error.message);
  }
};
const isShowTopTips = ref(props);
const mySwiper = ref();
const initSwiper = () => {
  // if (mySwiper.value) {
  //   mySwiper.value.destroy(true, true);
  // }
  mySwiper.value = new Swiper('.swiper', {
    pagination: {
      el: '.swiper-pagination',
      dynamicBullets: true,
      type: 'bullets',
    },
    observeSlideChildren: true,
    observer: true,
    observeParents: true,
  });
};
onMounted(() => {
  nextTick(initSwiper);
  Swiper.use([Pagination]);
});
watch(
  photoInfo,
  () => {
    if (mySwiper.value && typeof mySwiper.value.destroy === 'function') {
      mySwiper.value.destroy(true, true);
    }
    nextTick(initSwiper);
    Swiper.use([Pagination]);
  },
  { deep: true },
);
defineExpose({
  isShow,
});
</script>

<style lang="scss" scoped>
.photo-view {
  width: 2.92rem;
  height: 4.54rem;
}
.info-cover-img {
  width: 100%;
  height: auto;
  border-radius: 0.2rem;
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
  // background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/196595/16/34278/83680/645ddb4aFfbed970c/9d2f9e955a7ba666.png);
}
.info-cover-img-status {
  width: 2.88rem;
  height: 3.39rem;
  position: absolute;
  left: 0;
  top: 0;
}
.info-photo-name {
  padding: 0 0.1rem;
  margin-top: 0.2rem;
  width: 6.3rem;
  margin-left: 0.05rem;
  font-size: 0.3rem;
  color: #e64330;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.info-photo-desc {
  padding: 0 0.1rem;
  margin-top: 0.2rem;
  width: 6.3rem;
  height: 0.88rem;
  margin-left: 0.05rem;
  font-size: 0.27rem;
  color: #000;
  line-height: 0.3rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  word-break: break-all;
}
.info-user-info {
  padding: 0 0.1rem;
  width: 6.3rem;
  margin-left: 0.05rem;
  height: 0.76rem;
  margin-top: 0.1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.1rem;
}

.info-nick-img {
  width: 0.76rem;
  height: 0.76rem;
  margin-right: 0.05rem;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.info-nick-name {
  width: 1rem;
  font-size: 0.27rem;
  color: #000;
  word-break: keep-all;
}
.info-praise-view {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.info-praise-num {
  width: auto;
  white-space: nowrap;
  font-size: 0.27rem;
  color: #000;
}
.content {
  width: 6.5rem;
  height: 8.84rem;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/203525/37/22531/15665/6438fdd7Fc67238b4/78fdba30e081be33.png');
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 1.46rem;
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .rule_text {
    width: 5.3rem;
    height: 5.9rem;
    color: #000;
    overflow-y: scroll;
    font-size: 0.18rem;
    text-align: justify;
  }
}

.info-cover-view-0 {
  width: 6.2rem;
  height: auto;
  background-color: #ffffff;
  box-shadow: 0.05rem 0.04rem 0.09rem 0rem rgba(94, 88, 80, 0.19);
  border-radius: 0.24rem;
  margin: 0.13rem auto;
  max-height: 10rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  border-radius: 0.2rem;
}
.info-phto-0 {
  width: 6.5rem;
  height: auto;
  border-radius: 0.2rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/233284/21/33532/17074/68030cd1Fa02030fc/828f48897002cd17.png') no-repeat 100%;
  margin: 0.1rem;
  overflow: hidden;
  position: relative;
}
.info-cover-view-1 {
  width: 3.4rem;
  height: 3.92rem;
}
.info-phto-1 {
  width: 3.4rem;
  height: 5.12rem;
  border-radius: 0.2rem;
  background-color: #fff;
  margin: 0.1rem;
  position: relative;
}
.close-btn {
  width: 0.6rem;
  height: 0.6rem;
  position: absolute;
  right: 0.2rem;
  top: 0.2rem;
  background-image: url('https://img20.360buyimg.com/imgzone/jfs/t1/267350/1/7467/2070/6777bec0F8f9d1714/f769e18aa6aae4cc.png');
  background-position: bottom;
  background-repeat: no-repeat;
  background-size: contain;
}
</style>
<style lang="scss">
.rule_popup.van-popup {
  // width: 6.5rem;
  // height: 8.84rem;
  background: none;
  overflow-y: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
