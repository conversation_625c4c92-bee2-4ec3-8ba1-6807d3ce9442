<template>
  <div class="main-info">
    <div class="user-info-view">
      <div class="pin-img" :style="{ backgroundImage: `url(${baseUserInfo.avatar})` }"></div>
      <div style="flex: 1">
        <div class="user-name">用户昵称：{{ baseUserInfo.nickname }}</div>
        <div class="count-box">
          <span class="pop-label">人气值：</span>
          <span class="pop-value">{{ popularity }}</span>
        </div>
      </div>
    </div>
    <div class="type-tab">
      <div class="tab1" :class="pageType == '我发布的' ? 'select-tab1' : ''" @click="changePageType('我发布的')">我发布的</div>
      <div class="tab2" :class="pageType == '我点赞的' ? 'select-tab2' : ''" @click="changePageType('我点赞的')">我点赞的</div>
    </div>
    <div class="phto-box" v-show="pageType == '我发布的' && pageNewList.length == 0">
      <img class="upload-img" @click="toCreate" src="//img10.360buyimg.com/imgzone/jfs/t1/271328/16/21688/5663/6800a605Fcbc88d26/6a9254d76d12d2ac.png" />
    </div>
    <div class="phto-box" id="worksInfoBox1" ref="scrollableInfoContent" v-show="pageType == '我发布的' && pageNewList.length > 0">
      <div style="width: 3.6rem" class="photo-padding">
        <myPhoto v-for="(item, index) in pageNew0" :photoInfo="item" :photoStyle="index % 2 == 0 ? 0 : 1" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage" @handleEditPhotoPage="toEdit" @handleShare="handleShare"></myPhoto>
      </div>
      <div style="width: 3.6rem">
        <myPhoto v-for="(item, index) in pageNew1" :photoInfo="item" :photoStyle="index % 2 == 0 ? 1 : 0" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage" @handleEditPhotoPage="toEdit" @handleShare="handleShare"></myPhoto>
      </div>
    </div>
    <div class="phto-box" id="worksInfoBox2" ref="scrollableInfoContent2" v-show="pageType == '我点赞的'">
      <div style="width: 3.6rem">
        <photo v-for="(item, index) in pageHot0" :photoInfo="item" :photoStyle="index % 2 == 0 ? 1 : 0" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage"></photo>
      </div>
      <div style="width: 3.6rem" class="photo-padding">
        <photo v-for="(item, index) in pageHot1" :photoInfo="item" :photoStyle="index % 2 == 0 ? 0 : 1" :key="index" @handleOpenPhotoPage="handleOpenPhotoPage"></photo>
      </div>
    </div>
    <VanPopup teleport="body" v-model:show="showPrizeInfo" position="center">
      <div style="width: 6.7rem; height: auto; position: relative">
        <photoInfo :photoInfo="infoWork" :photoStyle="0" :key="index" @handleOpenPhotoPage="() => {}"></photoInfo>
      </div>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showHb" position="center">
      <div class="page12">
        <img class="photo" :src="photos" alt="" />
        <p style="position: absolute; bottom: 0; font-size: 0.25rem; color: #fff">长按图片保存至手机相册</p>
      </div>
    </VanPopup>
    <div id="poster" class="page13">
      <img class="poster-img" :src="HbBgImg" />
      <!-- <div class="poster-pet-img" :style="{backgroundImage: `url(${hbInfo.data.workImageUrl})`}"></div> -->
      <div class="poster-pet-img-box">
        <img class="poster-pet-img" :src="hbInfo.data.workImageUrl[0]" alt="" />
      </div>
      <!-- <img class="poster-pet-img" :src="hbInfo.data.workImageUrl" alt=""> -->
      <img class="userLogo" :src="hbInfo.data.headerImg" alt="" />
      <div class="name">{{ hbInfo.data.nickName }}</div>
      <span class="poster-answer">{{ hbInfo.data.workTitle }}</span>
      <vue-qrcode
        class="qr-code"
        :value="qrCodeActUrl"
        :options="{
          color: {
            dark: '#000', // dots
            light: '#ffffff', // background
          },
        }"></vue-qrcode>
      <!-- <span class="code-tip">京东APP扫码<br>解锁同款年度报告</span> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
/* eslint-disable */
import { ref, Ref, reactive, onBeforeMount, inject, onMounted, onUnmounted, watch } from 'vue';
import { emojiRegex, isValidChineseId, ruleFormat, formatDate } from '../common';
import { httpRequest } from '@/utils/service';
import { showLoadingToast, showToast, closeToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import photo from '../components/photo';
import myPhoto from '../components/myPhoto';
import photoInfo from '../components/photoInfo';
import VueQrcode from '@chenfengyuan/vue-qrcode';
import html2canvas from 'html2canvas';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const baseUserInfo: any = inject('baseUserInfo');
const pathParams: any = inject('pathParams');

const getcount = ref(100);

const infoWork = ref({});
const showPrizeInfo = ref(false);
const showHb = ref(false);
const HbBgImg = ref('');
const imgBkArray = [
  {
    backgroundImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/255794/6/9844/36527/677b858eF4d8f38e0/00de33abb36dd103.png',
  },
  {
    backgroundImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/258779/30/8827/34861/677b858dF43bb80b6/f73560e241ec7af2.png',
  },
  {
    backgroundImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/256971/21/8830/37929/677b858dFa9e781cf/9bbca5c4f513fcce.png',
  },
];
let qrCodeActUrl = location.href;

type DefineEmits = {
  (e: 'toggle-component', componentName: string): void;
};
const emits = defineEmits<DefineEmits>();

const toCreate = (id: string): void => {
  emits('toggle-component', 'CreatePage');
};
const toEdit = (info: any): void => {
  emits('edit-component', info);
};
const handleOpenPhotoPage = (info): void => {
  console.log(info);
  showPrizeInfo.value = true;
  infoWork.value = info;
};
// const handleShare = () => {

//     const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
//     console.log(`${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareId=${shareConfig.shareId}&shareUuid=${shareUuid}`);
//     callShare({
//       title: shareConfig.shareTitle,
//       content: shareConfig.shareContent,
//       imageUrl: shareConfig.shareImage,
//       shareUrl: `${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareId=${shareConfig.shareId}&shareUuid=${shareUuid}`,
//       afterShare: () => { },
//     });
//   };
const photos = ref('');
const takephotos = async () => {
  let save2 = document.getElementById('poster') as HTMLAnchorElement;
  await html2canvas(save2, {
    backgroundColor: null, //设置图片背景为透明
    scale: 2,
    width: save2.offsetWidth,
    height: save2.offsetHeight,
    allowTaint: true,
    useCORS: true,
  }).then(function (canvas: any) {
    const context: any = canvas.getContext('2d');
    context.mozImageSmoothingEnabled = false;
    context.webkitImageSmoothingEnabled = false;
    context.msImageSmoothingEnabled = false;
    context.imageSmoothingEnabled = false;
    const src64: any = canvas.toDataURL();
    const newImg: any = document.createElement('img');
    newImg.crossOrigin = 'Anonymous';
    newImg.src = src64;
    photos.value = newImg.src;
    // console.log(photo.value);
    showHb.value = true;
    // saveHb();
  });
};
const hbInfo = reactive({
  data: {
    nickName: '',
    headerImg: '',
    workTitle: '',
    workImageUrl: '',
  },
});
const handleShare = (workItem: any) => {
  HbBgImg.value = '//img10.360buyimg.com/imgzone/jfs/t1/283991/13/22667/142074/6803a457F23cd2743/f8ae354885e6b6ad.png';
  hbInfo.data.nickName = workItem.nickName;
  hbInfo.data.headerImg = workItem.headerImg;
  let title = '';
  hbInfo.data.workTitle = title;
  hbInfo.data.workImageUrl = workItem.workImageUrl;
  const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
  qrCodeActUrl = `${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareId=${shareConfig.shareId}&shareWork=${workItem.id}`;
  // HbBgImg.value = imgBkArray[Math.floor(Math.random() * 3)].backgroundImg;
  // 出分享海报弹窗
  setTimeout(() => {
    takephotos();
  }, 50);
  // console.log('分享');
  // console.log(`${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareId=${shareConfig.shareId}`);
  // callShare({
  //   title: shareConfig.shareTitle,
  //   content: shareConfig.shareContent,
  //   imageUrl: shareConfig.shareImage,
  //   shareUrl: `${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareId=${shareConfig.shareId}&shareWork=${workItem.id}`,
  //   afterShare: () => { },
  // });
};

const pageType = ref('我发布的');
const changePageType = (type) => {
  const worksInfoBox1 = document.getElementById('worksInfoBox1');
  worksInfoBox1?.scrollTo(0, 0);
  const worksInfoBox2 = document.getElementById('worksInfoBox2');
  worksInfoBox2?.scrollTo(0, 0);
  pageType.value = type;
};
// const pageNewList = ref([]);
const pageNewList = ref([1]);
const pageNew0 = ref();
const pageNew1 = ref();
const pageHotList = ref([]);
const pageHot0 = ref();
const pageHot1 = ref();
const handleSearch = (params: type) => {
  console.log(searchText.value);
};
const scrollableInfoContent = ref(null);
const scrollableInfoContent2 = ref(null);
const screen = window.innerWidth / 750;
const pageNo = ref(1);
const pageNo1 = ref(1);
// 最新作品
const getPageNewList = async (isSearch: any) => {
  try {
    const { data, message } = await httpRequest.get('/dz/1912668770678595586/myWorks', {}, { isLoading: true });
    if (data) {
      pageNewList.value = data;
      pageNew0.value = pageNewList.value.filter((item, index) => index % 2 == 0);
      pageNew1.value = pageNewList.value.filter((item, index) => index % 2 == 1);
    } else {
      showToast(message);
    }
  } catch (error) {
    showToast(error.message);
  } finally {
    closeToast();
  }
};
// 最热作品
const getPageHotList = async (isSearch: any) => {
  const { data, message } = await httpRequest.get('/dz/1912668770678595586/myUpWorks', {}, { isLoading: true });
  if (data) {
    // console.log(data);
    const temp = JSON.parse(JSON.stringify(pageHotList.value));
    data.forEach((element) => {
      temp.push(element);
    });
    pageHotList.value = temp;
    pageHot0.value = pageHotList.value.filter((item, index) => index % 2 == 0);
    pageHot1.value = pageHotList.value.filter((item, index) => index % 2 == 1);
  } else {
    showToast(message);
  }
};
const handleScroll = (event) => {
  const scrollTop = event.target.scrollTop;

  if (scrollTop / screen >= 1211 * pageNo.value) {
    // 滚动条距离顶部超过或等于200px时执行的方法
    for (let index = 0; index < 10; index++) {
      pageNewList.value.push(index + 10 * pageNo.value);
      pageNew0.value = pageNewList.value.filter((item, index) => index % 2 == 0);
      pageNew1.value = pageNewList.value.filter((item, index) => index % 2 == 1);
    }
    pageNo.value += 1;
    // 在这里调用你想要执行的方法
    // showLoadingToast('加载中')
    // return;
  }
};
const handleScroll1 = (event) => {
  const scrollTop = event.target.scrollTop;
  // console.log(scrollTop/screen);
  if (scrollTop / screen >= 1211 * pageNo1.value) {
    console.log(scrollTop / screen);

    // 滚动条距离顶部超过或等于200px时执行的方法
    console.log('我点赞的', pageNo1.value);
    for (let index = 0; index < 10; index++) {
      pageHotList.value.push(index + 10 * pageNo1.value);
      pageHot0.value = pageHotList.value.filter((item, index) => index % 2 == 0);
      pageHot1.value = pageHotList.value.filter((item, index) => index % 2 == 1);
    }
    pageNo1.value += 1;
    // 在这里调用你想要执行的方法
    // showLoadingToast('加载中')
    // return;
  }
};

const popularity = ref(0);
const getMian = async () => {
  try {
    const { data, message } = await httpRequest.post('/dz/1912668770678595586/user', {}, { isLoading: true });
    console.log(data);
    if (data) {
      popularity.value = data.totalPopularity;
      getPageNewList();
      getPageHotList();
    } else {
      showToast(message);
    }
  } catch (error) {
    showToast(error.message);
  }
};
onMounted(() => {
  getMian();
  // scrollableInfoContent.value.addEventListener('scroll', handleScroll);
  // scrollableInfoContent2.value.addEventListener('scroll', handleScroll1);
});

onUnmounted(() => {
  // if (scrollableInfoContent.value) {
  //     scrollableInfoContent.value.removeEventListener('scroll', handleScroll);
  //     scrollableInfoContent2.value.removeEventListener('scroll', handleScroll1);
  // }
});
</script>
<style lang="scss" scoped>
.main-info {
  width: 7.5rem;
  height: 100vh;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/279249/37/20887/15376/680076aaFe56c09be/369f8082a4ca51f5.jpg');
  background-repeat: repeat;
  background-size: 7.5rem auto;
  background-position: top;
  position: relative;
  box-sizing: border-box;
  background-color: #e64330;
  padding-top: 0.4rem;
  box-sizing: border-box;
  overflow-y: scroll;
  overflow-x: hidden;
}
.user-info-view {
  width: 6.9rem;
  height: 1.9rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/277225/18/22690/10574/680076f3F31656f5a/a8ef41eace1b0e66.png');
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
  margin: 0 auto;
  display: flex;
  flex-direction: row;
  padding: 0.3rem;
  box-sizing: border-box;

  .pin-img {
    width: 0.9rem;
    height: 0.9rem;
    border-radius: 50%;
    background-color: #fff;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    margin-right: 0.2rem;
  }
  .user-name {
    font-size: 0.3rem;
    line-height: 0.5rem;
    color: #333333;
    text-align: left;
  }
  .count-box {
    display: flex;
    align-items: center;
    border: 2px solid #bbaaff;
    border-radius: 0.2rem;
    overflow: hidden;
    background: #fff;
    width: 3.2rem;
    height: 0.4rem;
    margin-top: 0.12rem;
    .pop-label {
      background: #c3a6ef;
      color: #fff;
      font-size: 0.22rem;
      font-weight: 500;
      padding: 0 0.32rem;
      height: 0.4rem;
      line-height: 0.4rem;
      border-radius: 0.2rem 0 0 0.2rem;
      display: flex;
      align-items: center;
    }
    .pop-value {
      color: #000;
      font-size: 0.26rem;
      font-weight: bold;
      padding: 0 0.32rem;
      letter-spacing: 0.02rem;
      height: 0.4rem;
      line-height: 0.4rem;
      display: flex;
      align-items: center;
    }
  }
}
.type-tab {
  width: 7.5rem;
  margin-top: 0.7rem;
  height: auto;
  display: flex;
  align-items: flex-start;
  justify-content: space-around;
  font-size: 0.3rem;
  color: #fff;
  .tab1,
  .tab2 {
    width: 3.2rem;
    height: 0.5rem;
    text-align: center;
    line-height: 0.5rem;
  }
  .tab1 {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/282379/14/21065/716/68007caeF1fe381f3/e160f2e38e9ebd29.png') no-repeat center center;
    background-size: 100%;
    color: #000;
  }
  .tab2 {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/277327/2/22779/716/68007c98F4c41ea7a/5bb481a8cde1a67b.png') no-repeat center center;
    background-size: 100%;
    color: #000;
  }
  .select-tab1 {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/274260/18/21947/768/68007c98F524936ca/444d739ae46fdbe2.png') no-repeat center center;
    background-size: 100%;
    color: #fff;
  }
  .select-tab2 {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/283774/11/21191/700/68007caeFd794860f/77d1463dccbe7156.png') no-repeat center center;
    background-size: 100%;
    color: #fff;
  }
}
.phto-box {
  width: 7.5rem;
  height: auto;
  margin-top: 0.15rem;
  display: flex;
  justify-content: space-between;
  overflow-y: scroll;
  overflow-x: hidden;
  box-sizing: border-box;
  padding-left: 0.2rem;
  padding-right: 0.2rem;
}
.upload-img {
  width: 3.38rem;
  margin-left: auto;
  margin-right: auto;
  margin-top: 3rem;
}
.photo-padding {
  box-sizing: border-box;
  padding-top: 0.33rem;
}
.page13 {
  width: 5.5rem;
  margin: 0 auto;
  height: 9.76rem;
  position: relative;
  background-size: 100% 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  left: -666rem;
  top: -666rem;

  .poster-img {
    width: 5.5rem;
    height: 9.76rem;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
  }
  .userLogo {
    width: 0.4rem;
    height: 0.4rem;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    top: 7.9rem;
    left: 0.7rem;
  }

  .poster-pet-img-box {
    width: 3.5rem;
    height: 4.2rem;
    // max-width: 3.5rem;
    // height: auto;
    // max-height: 5.2rem;
    // background-repeat: no-repeat;
    // background-position: center;
    // background-size: 3.5rem auto;
    position: absolute;
    border-radius: 0.3rem;
    top: 3rem;
    left: 1rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .poster-pet-img {
    width: 3.5rem;
    height: auto;
    max-height: 4.2rem;
    border-radius: 0.3rem;
    object-fit: contain;
  }

  .photo-frame {
    width: 8.1rem;
    height: 10.22rem;
    background-size: 100% 100%;
    position: absolute;
    top: 1.19rem;
  }

  .name {
    width: 1.7rem;
    height: 0.5rem;
    text-align: center;
    font-size: 0.25rem;
    color: #000;
    position: absolute;
    top: 7.9rem;
    left: 1.4rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .MBTI {
    text-align: center;
    font-size: 0.75rem;
    color: #fff;
    position: absolute;
    top: 8rem;
    left: 1.25rem;
    display: inline-block;
    -webkit-text-stroke: 0.1rem #ea0202;
  }

  .poster-answer {
    width: 4rem;
    font-size: 0.26rem;
    color: #000;
    position: absolute;
    top: 6.9rem;
    left: 1rem;
    text-align: left;
  }

  .aaa {
    position: absolute;
    top: 10.7rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
  }

  .poster-tag {
    display: flex;
    flex-wrap: wrap;

    .tag {
      height: 0.5rem;
      line-height: 0.5rem;
      background-color: #ea0202;
      color: #fff;
      border-radius: 0.15rem;
      margin-right: 0.1rem;
      margin-bottom: 0.1rem;
      padding: 0 0.2rem;
      font-size: 0.28rem;
      flex: 0 1 auto;
    }
  }

  .qr-code {
    width: 1.3rem !important;
    height: 1.3rem !important;
    position: absolute;
    top: 7.57rem;
    left: 3.9rem;
  }

  .code-tip {
    width: 1.33rem;
    font-size: 0.16rem;
    line-height: 0.2rem;
    position: absolute;
    top: 15.55rem;
    left: 5.77rem;
  }
}
.page12 {
  width: 6rem;
  max-width: 750px;
  margin: 0 auto;
  height: 11.5rem;
  position: relative;
  // background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/149976/32/44113/150991/6744405fF8a512795/2fa07c55697b1a81.png");
  background-size: 100% 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  .photo {
    width: 6rem;
    height: auto;
    position: absolute;
    top: 0;
  }

  .save-page {
    width: 2.37rem;
    height: 0.5rem;
    background-size: 100% 100%;
    background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/147980/23/46907/5342/6746c751Ff434125b/ccfe41dccacf31b9.png);
    position: absolute;
    top: 14rem;
  }
}
</style>
