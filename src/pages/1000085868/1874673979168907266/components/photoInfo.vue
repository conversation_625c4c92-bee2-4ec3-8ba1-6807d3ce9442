<template>
    <div :class="`info-phto-${photoStyle}`">
      <div :class="`info-cover-view-${photoStyle}`" @click="handleOpenPhotoPage(photoInfo)">
        <img class="info-cover-img" :src="photoInfo.workImageUrl">
          <!-- <div class="info-cover-img" :style="{backgroundImage: `url(${photoInfo.workImageUrl})`}"></div> -->
      </div>
      <div class="info-photo-name" @click="handleOpenPhotoPage(photoInfo)">{{ photoInfo.workTitle }}</div>
      <div class="info-photo-desc">{{ photoInfo.workDescription }}</div>
      <div class="info-user-info">
        <div class="info-nick-img" :style="{backgroundImage: `url(${photoInfo.headerImg})`}"></div>
        <div class="info-nick-name">{{ photoInfo.nickName }}</div>
        <div class="info-praise-view">
          <img style="width: 0.46rem; height: 0.41rem;margin-right: 0.05rem;"
            :src="photoInfo.dayUpStatus?'https://img10.360buyimg.com/imgzone/jfs/t1/117198/28/35357/550/64585a46Fa54bb595/32adc95ad9a05a45.png':'//img10.360buyimg.com/imgzone/jfs/t1/204452/17/32502/894/645b2259Fbfda44e4/297c1f55258c945a.png'"
            @click="task(photoInfo)"
            />
          <div class="info-praise-num">{{ photoInfo.popularity }}</div>
        </div>
      </div>
  </div>
</template>

<script setup lang='ts'>
import {
  ref, Ref, toRefs, defineProps, defineExpose, defineEmits, inject } from 'vue';
import { Checkbox as VanCheckbox, Button as VanButton, Popup as VanPopup, Toast, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';

const delayToast = async (e: string) => {
  setTimeout(() => {
    Toast(e);
  }, 1000);
};

const loveIconCom = ref();

// 作品审核状态对应的图标 0:待审核 1：审核通过 2：审核未通过
const EXAMINE = {
  0: 'https://img10.360buyimg.com/imgzone/jfs/t1/134434/18/36560/4467/645efaefFd138391b/370b0eba8251c53e.png',
  2: 'https://img10.360buyimg.com/imgzone/jfs/t1/129193/40/32088/4844/645efaefFfe0782b4/77c2ba04f6d7f14b.png',
};
const isShow = ref(false);
const props = defineProps({
  photoInfo: {
    type: Object,
    required: true,
  },
  isDisLove: {
    type: Boolean,
    required: false,
    default: false,
  },
  isCheckCancel: {
    type: Boolean,
    required: false,
    default: false,
  },
  isMyPush: {
    type: Boolean,
    required: false,
    default: false,
  },
  photoStyle: {
    type: Number,
    required: false,
    default: 0,
  },
});
const { photoInfo } = toRefs(props);
const { isCheckCancel } = toRefs(props);
const { isMyPush } = toRefs(props);
const { isDisLove } = toRefs(props);

// const postInfo = inject('postInfo') as string;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
// let lock = false;
const emits = defineEmits(['handleOpenPhotoPage', 'handleCancelLove', 'handleTaskLove']);
const handleOpenPhotoPage = (photoId:string) => {
  // setTimeout(() => {
  console.log('photoId', photoId);
  // }, 500);
};

const task = async (photoInfo: any) => {
  if (isDisLove.value) {
    console.log(isDisLove.value);
    return;
  }
  try {
    const config = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    };
    const { data, message } = await httpRequest.post('/dz/1874673979168907266/upWorks', { workId: photoInfo.id }, {}, { isLoading: true });
    if (message) {
      showToast(message);
    } else {
      photoInfo.dayUpStatus = true;
      photoInfo.popularity += 100;
      emits('handleTaskLove', photoInfo.id);
    }
  } catch (error: any) {
    console.log(error);
    showToast(error.message);
  }
//   if (photoInfo.value.status) {
//     cancelLove();
//   } else {
//     takeLove();
//   }
};

const isShowTopTips = ref(props);
// 显示暴露的数据，才可以在父组件拿到
defineExpose({
  isShow,
});

</script>

<style lang='scss' scoped>
.photo-view {
    width: 2.92rem;
    height: 4.54rem;
}
.info-cover-img {
  width: 100%;
  height: auto;
  border-radius: 0.20rem;
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
  // background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/196595/16/34278/83680/645ddb4aFfbed970c/9d2f9e955a7ba666.png);
}
.info-cover-img-status {
  width: 2.88rem;
  height: 3.39rem;
  position: absolute;
  left: 0;
  top: 0;
}
.info-photo-name {
  margin-top: 0.2rem;
  width: 6.3rem;
  margin-left: 0.05rem;
  font-size: 0.3rem;
  color: #e64330;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.info-photo-desc {
  margin-top: 0.2rem;
  width: 6.3rem;
  height: 0.88rem;
  margin-left: 0.05rem;
  font-size: 0.27rem;
  color: #000;
  line-height: 0.3rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  word-break: break-all;
}
.info-user-info {
  width: 6.3rem;
  margin-left: 0.05rem;
  height: 0.76rem;
  margin-top: 0.1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.1rem;
}

.info-nick-img {
  width: 0.76rem;
  height: 0.76rem;
  margin-right: 0.05rem;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.info-nick-name {
  width: 1rem;
  font-size: 0.27rem;
  color: #000;
  word-break: keep-all;
}
.info-praise-view {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.info-praise-num {
  width: auto;
  white-space: nowrap;
  font-size: 0.27rem;
  color: #000;
}
  .content {
    width: 6.5rem;
    height: 8.84rem;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/203525/37/22531/15665/6438fdd7Fc67238b4/78fdba30e081be33.png");
    background-size: 100%;
    background-repeat: no-repeat;
    padding-top: 1.46rem;
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .rule_text {
      width: 5.3rem;
      height: 5.9rem;
      color: #000;
      overflow-y: scroll;
      font-size: 0.18rem;
      text-align: justify;
    }

    .close-btn {
      width: .6rem;
      height: .6rem;
      margin-top: .84rem;
    }
  }
  .info-cover-view-0 {
    width: 6.5rem;
    height: auto;
    max-height: 10rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    border-radius: 0.2rem;
  }
  .info-phto-0 {
    width: 6.5rem;
    height: auto;
    border-radius: 0.2rem;
    background-color: #fff;
    margin: 0.1rem;
    overflow: hidden;
}
.info-cover-view-1 {
    width: 3.4rem;
    height: 3.92rem;
  }
.info-phto-1 {
    width: 3.4rem;
    height: 5.12rem;
    border-radius: 0.2rem;
    background-color: #fff;
    margin: 0.1rem;
}
  </style>
  <style lang="scss">
  .rule_popup.van-popup {
    // width: 6.5rem;
    // height: 8.84rem;
    background: none;
    overflow-y: unset;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  </style>
