<template>
  <div class="bg">
    <div class="contentClass">
      <div class="content">
        <div class="itemClass">
          <div class="titleClass">姓名：</div>
          <div class="titleValueClass">
            <VanField v-model="dataAll.realName" type="text"  maxlength="15" placeholder="请输入收货人姓名" class="van-field__control" />
          </div>
        </div>
        <div class="itemClass">
          <div class="titleClass">电话：</div>
          <div class="titleValueClass">
            <VanField v-model="dataAll.mobile" type="number"  placeholder="请输入手机号" class="van-field__control" maxlength="11" />
          </div>
        </div>
        <div class="itemClass">
          <div class="titleClass">地区：</div>
          <div class="titleValueClass">
            <VanField v-model="dataAll.areaAll" readonly placeholder="请选择所在地区" @click="showAddress" class="van-field__control"></VanField>
          </div>
        </div>
        <div class="itemClass">
          <div class="titleClass">详细地址：</div>
          <div class="titleValueClass">
            <VanField v-model="dataAll.address"  maxlength="40" type="text" placeholder="请输入详细地址" class="van-field__control" />
          </div>
        </div>
        <!-- <div class="itemClass">
          <div class="titleClass">邮政编码</div>
          <div class="titleValueClass">
            <VanField maxlength="6" type="number" :readonly="!!addressData.realName" v-model="dataAll.addressCode" placeholder="请输入邮政编码" class="van-field__control" />
          </div>
        </div> -->
      </div>
      <div class="submitClass" @click="submitClick()"></div>
    </div>

    <div class="closeClass" @click="closeClickPop"></div>
  </div>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import { areaList } from '@vant/area-data';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

// const props = defineProps({
//   addressData: {
//     type: Object,
//     required: true,
//   },
// });
const addressSelects = ref(false);

const dataAll = ref({
  realName: '',
  mobile: '',
  address: '',
  areaAll: '',
  province: '',
  city: '',
  county: '',
  addressCode: '',
});
const emits = defineEmits(['closePop']);

const showAddress = () => {
  addressSelects.value = true;
};

const closeClickPop = () => {
  emits('closePop');
};
const confirmAddress = (addressItemList: any) => {
  dataAll.value.province = addressItemList.selectedOptions[0].text;
  dataAll.value.city = addressItemList.selectedOptions[1].text;
  dataAll.value.county = addressItemList.selectedOptions[2].text;
  dataAll.value.areaAll = dataAll.value.province + dataAll.value.city + dataAll.value.county;
  addressSelects.value = false;
};
// 提交地址
const submitClick = async () => {
  // TODO 输入校验
  if (!dataAll.value.realName) {
    showToast('请输入收货人姓名');
    return;
  }
  if (!dataAll.value.mobile) {
    showToast('请输入收货人手机号');
    return;
  }
  if (!dataAll.value.province || !dataAll.value.city || !dataAll.value.county) {
    showToast('请输入收货人所在地区');
    return;
  }
  if (!dataAll.value.address) {
    showToast('请输入收货人详细地址');
    return;
  }
  // if (!dataAll.value.addressCode) {
  //   showToast('请输入收货人邮政编码');
  //   return;
  // }
  const regex = /^1[3-9]\d{9}$/;
  if (!regex.test(dataAll.value.mobile)) {
    showToast('手机号格式不正确');
    return;
  }
  // if (dataAll.value.addressCode.length < 6) {
  //   showToast('邮政编码格式不正确，应为6位数字');
  //   return;
  // }
  // console.log('提交地址信息', dataAll.value);
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const subObj = JSON.parse(JSON.stringify(dataAll.value));
    delete subObj.areaAll;
    console.log('提交地址信息', subObj);
    const res = await httpRequest.post('/dz/1874673979168907266/rankDraw', subObj);
    console.log(res, '填写地址');
    closeToast();
    emits('closePop', true);
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};
// 初始化组件
// const initData = () => {
//   if (props.addressData && props.addressData.receiverName) {
//     dataAll.value = {
//       realName: props.addressData.receiverName,
//       mobile: props.addressData.mobile,
//       address: props.addressData.address,
//       areaAll: props.addressData.province + props.addressData.city + props.addressData.county,
//       province: props.addressData.province,
//       city: props.addressData.city,
//       county: props.addressData.county,
//       addressCode: props.addressData.addressCode,
//       activityPrizeId: props.activityPrizeId,
//       addressId: props.addressId,
//     };
//   } else {
//     dataAll.value = {
//       realName: '',
//       mobile: '',
//       address: '',
//       areaAll: '',
//       province: '',
//       city: '',
//       county: '',
//       addressCode: '',
//       activityPrizeId: props.activityPrizeId,
//       addressId: props.addressId,
//     };
//   }
// };
// watch(
//   () => props.activityPrizeId,
//   (val) => {
//     console.log('11111111111');

//     console.log(props.addressData.receiverName, 'props.addressData.receiverName');

//     initData();
//   },
// );
// initData();
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
.van-picker {
  width: 7.5rem;
}
</style>
<style lang="scss" scoped>
.bg {
  display: flex;
  flex-direction: column;
  align-items: center;
  .contentClass {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/264516/37/8149/96852/677a42d8F14253ae8/f3df2468fdc4ab73.png) no-repeat;
    background-size: 100%;
    width: 6.5rem;
    height: 7.15rem;
    padding-top: 1.24rem;
    display: flex;
    //justify-content: center;
    flex-direction: column;
    align-items: center;
    .content {
      width: 5.50rem;
      // max-height: 4.1rem;
      overflow-y: scroll;
      .itemClass {
        display: flex;
        justify-content: space-between;
        color: #000;
        margin-bottom: 0.4rem;
        .titleClass {
          font-size: 0.44rem;
          white-space: nowrap;
          width: 1.2rem;
          text-align: right;
          color: #fff
        }
        .titleValueClass {
          background-color: #fff;
          border-radius: 0.2rem;
          margin-left: 0.11rem;
          width: 3.4rem;
          height: 0.6rem;
          .van-field__control {
            width: 100%;
            height: 100%;
            color: #000;
            // border: none;
            // background-color: #645041;
            // border-radius: 0.36rem;
            font-size: 0.23rem;
            padding-left: 0.24rem;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
  ::v-deep input {
    color: #b3aca7;
    //background-color:transparent !important;
  }
  .submitClass {
    background-image: url(//img20.360buyimg.com/imgzone/jfs/t1/257542/14/8371/2097/677a434eFc7887ab3/9b4a6b310fa3e570.png);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 3.96rem;
    height: 0.64rem;
    margin-top: 0.36rem;
  }
  .closeClass {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/22151/22/20356/1774/66988ba1Fba0d4cc9/a2893f03a1e25920.png) no-repeat;
    background-size: 100%;
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.4rem;
  }
}
</style>
