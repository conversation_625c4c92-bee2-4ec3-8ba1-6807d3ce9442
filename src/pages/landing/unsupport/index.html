<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>

  <style>
    * {
      padding: 0;
      margin: 0;
    }
    .container {
      box-sizing: border-box;
      text-align: center;
      display: flex;
      align-items: center;
      width: 100vw;
      height: 100vh;
      flex-direction: column;
      padding:1rem;
      margin-top: 2rem;
    }
    .title {
      font-size: 1.4rem;
      font-weight: 500;
    }
    .url {
      word-wrap:break-word;
      word-break:break-all;
      overflow: hidden;
      margin-top: 1rem;
    }
  </style>
  <script>
    window.onload = function () {
      const result = new URLSearchParams(window.location.search);
      const urlInput = document.getElementsByClassName('url')[0];
      const url = result.get('url');
      const urlObj = new URL(url);
      urlObj.searchParams.delete('token');
      urlInput.innerHTML = urlObj.toString();
    }
  </script>
</head>
<body>
<div class="container">
  <div class="title">如需浏览，请长按网址复制后使用浏览器访问</div>
  <div class="url"></div>
</div>

</body>
</html>
