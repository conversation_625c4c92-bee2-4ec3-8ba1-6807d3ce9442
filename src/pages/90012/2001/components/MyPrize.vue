<template>
  <div class="rule-bk">
    <!-- <div class="close" @click="close" /> -->
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="time">{{ item.createTime }}</div>
        <div class="name">{{ item.prizeName }}</div>
        <div class="status" v-if="item.winStatus === 0">
          <div class="red">发放失败</div>
        </div>
        <div class="status" v-else-if="item.prizeType === '3'">
          <div class="orange" v-if="!item.realName" @click="toSaveAddress(item.userPrizeId)">填写地址</div>
          <div class="green" v-else>已填写地址</div>
        </div>
        <div class="status" v-else>
          <div class="green">已发放</div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <VanPopup teleport="body" v-model:show="saveAddressPopup" :closeOnClickOverlay="false">
    <SaveAddress v-if="saveAddressPopup" @close="closeSaveAddress" :userPrizeId="userPrizeId"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: string;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
  realName: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90012/getUserPrizes');
    res.data.forEach((item: Prize) => {
      item.createTime = dayjs(item.createTime).format('YYYY-MM-DD');
    });
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const saveAddressPopup = ref(false);
const userPrizeId = ref('');

const toSaveAddress = (id: string) => {
  userPrizeId.value = id;
  saveAddressPopup.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  saveAddressPopup.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('../assets/myPrizePopup.png');
  background-size: 100%;
  width: 6rem;
  height: 6.85rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.7rem;

  .close {
    position: absolute;
    bottom: 0;
    left: 2.7rem;
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
  }

  .content {
    height: 3.9rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    padding: 0 0.2rem;

    .prize {
      padding: 0.2rem 0;
      color: #814534;
      display: flex;
      align-items: center;
      .time,
      .name,
      .status {
        width: 33.33%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .no-data {
      text-align: center;
      line-height: 3.95rem;
      font-size: 0.24rem;
      color: #814534;
    }
  }
}
</style>
