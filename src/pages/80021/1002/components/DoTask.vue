<template>
  <div class="task-bk">
    <div class="title">
      <div class="close" @click="close"></div>
    </div>
    <div class="times">
      今天还有 <span>{{ times }}</span> 次抽奖机会
    </div>
    <div class="content">
      <div v-for="(item, index) in tasks" :key="item.id" class="task">
        <img :src="taskInfo[item.taskType].icon" alt="" class="icon" />
        <div class="info">
          <div class="name">{{ taskInfo[item.taskType].label }}</div>
          <div class="rule">{{ taskRule[item.taskType](item) }}</div>
        </div>
        <img class="button" v-if="item.taskFinishCount < item.limit || item.taskType === 8" @click="doTask(index)" :src="taskInfo[item.taskType].button" alt="" />
        <img class="button" v-else :src="taskInfo[item.taskType].buttonDIs" alt="" />
      </div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSku" position="bottom">
    <ShowSku v-if="showSku" :detail="tasks[taskDetailIndex]" @close="showSku = false" @refreshTask="refreshTask" @openShowGoShop="openShowGoShop"></ShowSku>
  </VanPopup>
</template>

<script lang="ts" setup>
import { PropType, inject, ref } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import ShowSku from './ShowSku.vue';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import { Task } from 'pages/80021/1001/ts/type';
import constant from '@/utils/constant';

const isPreview = (inject('isPreview') as boolean) ?? false;

const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) as string);

const pros = defineProps({
  times: {
    type: Number,
    default: 0,
  },
  tasks: {
    type: Array as PropType<Task[]>,
    default: () => [],
    required: true,
  },
  shareImg: {
    type: String,
    default: '',
  },
  shareTitle: {
    type: String,
    default: '',
  },
  shopId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close', 'refreshTask', 'openShowGoShop']);

const openShowGoShop = () => {
  emits('openShowGoShop');
};
const close = () => {
  emits('close');
};

const refreshTask = () => {
  emits('refreshTask');
};

const showSku = ref(false);
const taskDetailIndex = ref(0);

// 上报任务完成
const reportTask = async (taskId: number, skuId = '') => {
  try {
    const apis = {
      9: '/80021/shareSku',
      10: '/80021/shareShop',
      12: '/80021/shareActivity',
    };
    const res = await httpRequest.post(apis[taskId], { skuId });
    if (res.code === 200) {
      refreshTask();
    } else {
      showToast(res.msg);
    }
  } catch (error: any) {
    console.error(error);
  }
};

const doTask = (index: number) => {
  if (isPreview) return;
  if (pros.tasks[index].taskType <= 9) {
    taskDetailIndex.value = index;
    showSku.value = true;
  } else if (pros.tasks[index].taskType === 10) {
    callShare({
      title: shareConfig.shareTitle,
      shareUrl: `https://shop.m.jd.com/?shopId=${pros.shopId}`,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  } else if (pros.tasks[index].taskType === 12) {
    callShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareTitle,
      shareUrl: window.location.href,
      imageUrl: shareConfig.shareImage,
      afterShare: () => {
        reportTask(pros.tasks[index].taskType);
      },
    });
  }
};

const taskInfo = {
  5: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/134713/33/39142/3528/64e31a32F1685f038/fadb6735188f6ad6.png',
    label: '关注商品',
    button: '//img10.360buyimg.com/imgzone/jfs/t1/101548/7/43468/6947/64e31a33Fb438d469/49307581ea2b9994.png',
    buttonDIs: '//img10.360buyimg.com/imgzone/jfs/t1/85211/3/38569/7736/64e31a51Fefc5e0a1/e6b9eb91b2599816.png',
  },
  6: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/134713/33/39142/3528/64e31a32F1685f038/fadb6735188f6ad6.png',
    label: '预约商品',
    button: '//img10.360buyimg.com/imgzone/jfs/t1/230652/22/7607/6955/6576d0dfF07803b2e/0af78441ace4bc07.png',
    buttonDIs: '//img10.360buyimg.com/imgzone/jfs/t1/237268/36/7069/7929/6576d0dfF1a7203d0/86f51094102f3885.png',
  },
  7: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/106825/3/44010/3474/64e31a32Fe10ac272/f6cd32b40b4cb2b6.png',
    label: '加购商品',
    button: '//img10.360buyimg.com/imgzone/jfs/t1/109768/7/43832/7032/64e31a33F05634156/ad474890142cf1f7.png',
    buttonDIs: '//img10.360buyimg.com/imgzone/jfs/t1/113966/1/41129/7733/64e31a51F2013635e/1061ba11d854e228.png',
  },
  8: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/105185/29/43051/3251/64e31a32Fb2993139/5be823be0c26bb82.png',
    label: '购买商品',
    button: '//img10.360buyimg.com/imgzone/jfs/t1/154457/27/34147/7120/64e31a33F2c3c2fbe/23d91ef7e9d242f2.png',
    buttonDIs: '//img10.360buyimg.com/imgzone/jfs/t1/108147/35/44951/7640/64e31a51F7494b26b/9a59680af7b02d6e.png',
  },
  9: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/151948/9/34384/3205/64e31a32F99ab6401/db0290238e693c3f.png',
    label: '分享商品',
    button: '//img10.360buyimg.com/imgzone/jfs/t1/180434/19/36927/6910/64e31a33F5521428b/513675ea701f1a3b.png',
    buttonDIs: '//img10.360buyimg.com/imgzone/jfs/t1/130603/14/38487/7686/64e31a51F723c3438/07b61e03a15a7034.png',
  },
  10: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/106881/15/43744/3240/64e31a32Fc2d8add7/9c9470c3fc4c8c59.png',
    label: '分享店铺',
    button: '//img10.360buyimg.com/imgzone/jfs/t1/180434/19/36927/6910/64e31a33F5521428b/513675ea701f1a3b.png',
    buttonDIs: '//img10.360buyimg.com/imgzone/jfs/t1/130603/14/38487/7686/64e31a51F723c3438/07b61e03a15a7034.png',
  },
  12: {
    icon: '//img10.360buyimg.com/imgzone/jfs/t1/204314/2/32984/3310/64e31a32F1f2c0864/e920ca65274924ed.png',
    label: '分享活动',
    button: '//img10.360buyimg.com/imgzone/jfs/t1/180434/19/36927/6910/64e31a33F5521428b/513675ea701f1a3b.png',
    buttonDIs: '//img10.360buyimg.com/imgzone/jfs/t1/130603/14/38487/7686/64e31a51F723c3438/07b61e03a15a7034.png',
  },
};

const taskRule = {
  5: (info: any) => {
    if (info.optWay === 1) {
      return `每关注${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功关注全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  6: (info: any) => `每预约${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`,
  7: (info: any) => {
    if (info.optWay === 1) {
      return `每加购${info.perOperateCount}件商品，可获得${info.perLotteryCount}次抽奖机会`;
    }
    return `成功加购全部商品，可获得${info.lotteryCount}次抽奖机会`;
  },
  8: (info: any) => `每成功下${info.perOperateCount}单，可获得${info.perLotteryCount}次抽奖机会`,
  9: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  10: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
  12: (info: any) => `每成功分享${info.perOperateCount}位好友，可获得${info.perLotteryCount}次抽奖机会`,
};
</script>

<style scoped lang="scss">
.task-bk {
  width: 7.5rem;
  max-height: 9.7rem;
  min-height: 7rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/221948/24/24351/179509/64e31a33Fdc1eea44/8f239c2cd8391664.png) no-repeat;
  background-size: 100%;

  .title {
    position: relative;
    height: 1.2rem;
  }

  .close {
    position: absolute;
    top: 0.5rem;
    right: 0.24rem;
    width: 0.5rem;
    height: 0.6rem;
  }

  .times {
    text-align: center;
    color: #262626;
    font-size: 0.24rem;
    margin-top: 0.3rem;

    span {
      color: rgb(242, 39, 12);
    }
  }

  .content {
    max-height: 8.6rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;

    .task {
      background-color: #fff;
      margin-bottom: 0.1rem;
      border-radius: 0.2rem;
      padding: 0.2rem 0.3rem;
      display: flex;
      align-items: center;

      .icon {
        width: 0.83rem;
      }

      .info {
        flex: 1;
        padding: 0 0.39rem;
      }

      .name {
        font-size: 0.3rem;
        color: #6958ac;
      }

      .rule {
        font-size: 0.2rem;
        color: #6958ac;
      }

      .button {
        width: 1.36rem;
        height: 0.46rem;
        border-radius: 0.23rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .button-dis {
        background: #ffe3e3;
      }
    }
  }
}
</style>
