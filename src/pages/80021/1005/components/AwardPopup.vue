<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img :src="prize.showImg" alt="" class="prize-img" />
    <div class="content">
      <p class="prize-name">{{ prize.prizeName }}</p>
      <div>
        <p class="p3" v-if="prize.prizeType === 5">仅中奖人可享受优惠,仅可使用一次</p>

        <p class="p3" v-if="prize.prizeType === 2">京豆已放到您的账户中 京东-我的-京豆 中查看</p>

        <p class="p3" v-if="prize.prizeType === 1">已发放到您的账户 京东-我的-我的钱包-优惠券 中查看</p>
        <p class="p3" v-if="prize.prizeType === 4">积分已发放到您的账户中 店铺会员页 中查看</p>
        <p class="p3" v-if="prize.prizeType === 6">红包已发放到您的账户中 京东-我的-我的钱包-红包 中查看</p>
        <p class="p3" v-if="prize.prizeType === 7">礼品卡需手动兑换，请根据 兑换指引 前往兑换</p>
        <p class="p3" v-if="prize.prizeType === 8">京东E卡已发放到您的账户中 京东-我的-我的钱包-礼品卡 中查看</p>
        <p class="p3" v-if="prize.prizeType === 3">实物奖品系统不能自动发放，请<span>填写邮寄地址</span></p>

        <p class="p3" v-if="prize.prizeType === 12">
          1:权益非自动发放,需首先填写权益领取信息 <br />
          2:如放弃领取权益,活动结束权益不予补发
        </p>
        <p class="p3" v-if="prize.prizeType === 9 || prize.prizeType === 10"></p>
      </div>
      <div class="btn-list">
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/210578/2/32959/6008/65019640F5356a644/8db355929368dd5f.png" alt="" @click="shareAct" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/109174/33/44369/5779/6501967eF80928246/33b73e1f040282fe.png" alt="" v-if="prize.prizeType === 3" @click="saveAddress" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/102987/3/44853/5871/65019640F905ad550/c0b308f8957609c3.png" alt="" v-else-if="prize.prizeType === 5" @click="gotoSkuPage(prize.result)" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/99144/1/46101/6039/65019640Fcbc40d1c/37c89782cd2335b1.png" alt="" v-else-if="prize.prizeType === 7" @click="showCardNum" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/100501/20/44535/5908/65019640F62dea9f2/ee47b913f70bb69f.png" alt="" v-else-if="prize.prizeType === 9 || prize.prizeType === 10" @click="exchangePlusOrAiqiyi" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/192487/3/37587/5851/65019640F0825b3e3/136411a41743e85c.png" alt="" v-else-if="prize.prizeType === 12" @click="savePhone" />
        <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/111177/7/31224/5482/65019640Fccf7eeb0/89065e200c1404bb.png" alt="" v-else @click="gotoShopPage" />
      </div>
    </div>
    <div class="close" @click="close"></div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage"></div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { gotoSkuPage, exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { PropType, inject } from 'vue';
import { isPreview } from '@/utils';
import { showToast } from 'vant';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone', 'openShowGoShop']);

const close = () => {
  emits('close');
};
const gotoShopPage = () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  emits('openShowGoShop');
};
const saveAddress = () => {
  emits('saveAddress', props.prize.result.result, props.prize.activityPrizeId);
};

const showCardNum = () => {
  emits('showCardNum', { ...props.prize.result, showImg: props.prize.showImg, prizeName: props.prize.prizeName });
};

const savePhone = () => {
  emits('savePhone', props.prize.userPrizeId, props.prize.result.result.planDesc);
};

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bk {
  height: 7.89rem;
  width: 6rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/166908/32/40589/98464/65019763Fff6d6346/af37c0263c52fd05.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1.3rem;
  .prize-img {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 50%;
    margin: 0 auto;
  }
  .content {
    padding: 0 0.3rem;
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.24rem;
      font-weight: 500;
    }
    .prize-name {
      font-size: 0.45rem;
      font-weight: bold;
      margin: 0.27rem 0 0;
      text-align: center;
      color: #fff;
    }
    .p3 {
      font-size: 0.24rem;
      color: #fff;
      display: block;
      text-align: center;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        color: #f2270c;
      }
    }
    .btn-list {
      display: flex;
      justify-content: space-between;
      padding: 0 0.3rem;
      margin-top: 0.2rem;
      .btn {
        width: 2.1rem;
      }
      .btn-left {
        background: linear-gradient(to right, #f2270c, #ff6320);
      }
      .btn-right {
        background: #ff9900;
      }
    }
  }
}
.thanks-join {
  width: 6rem;
  height: 7.89rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/98130/39/37445/93706/65019837Fca4f3e8e/02538ed5f33a63ec.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 5.7rem;
  .btn {
    display: block;
    margin: 0 auto;
    width: 2.6rem;
    height: 0.76rem;
  }
}
.close {
  height: 0.6rem;
  width: 0.6rem;
  position: absolute;
  bottom: 0;
  left: 2.7rem;
}
</style>
