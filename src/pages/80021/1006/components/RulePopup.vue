<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/87857/33/45147/10864/653741e8Fc1afb011/15cc8f28f23bfda2.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/162848/14/38752/131205/653741eaF443eb161/11ee9c46e792c760.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 1.00rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem 0.1rem 0.33rem;
    .text {
      height: 0.62rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }

  .content {
    height: 8.45rem;
    width: 7.01rem;
    margin: 0 auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/42765/24/22486/5201/65375b11Fa63e8a5f/f09e12ed4c3f92ae.png) no-repeat;
    background-size: 100%;
    padding: 0.3rem 0.3rem 0 0.3rem;
    font-size: 0.24rem;
    color: #262626;
    white-space: pre-wrap;
    div {
      height: 100%;
      overflow-y: scroll;
    }
  }
}
</style>
