<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img v-if="furnish.actBg" :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>

        <div class="header-btn-all">
          <img class="header-btn" :src="furnish.ruleBg" alt="" @click="showRulePopup" v-click-track="'hdgz'" />
          <img class="header-btn" :src="furnish.prizeBg" alt="" @click="showMyPrize = true" v-click-track="'wdjp'" />
        </div>
      </div>
    </div>
    <div class="select-hover wheelAll">
      <div class="wheel">
        <lz-lucky-wheel ref="myLucky" width="88vw" height="88vw" :blocks="furnishStyles.params.value.blocks" :prizes="furnishStyles.params.value.prizes" :buttons="furnishStyles.params.value.buttons" @start="startCallback" @end="endCallback" :defaultConfig="furnishStyles.params.value.defaultConfig" />
        <!-- <img :src="wheelImg" alt="" class="wheel-img" /> -->
      </div>
      <div class="draws-num" :class="{ margin0: baseInfo.activityMainId === '1744178084716081154' }" :style="furnishStyles.drawsNum.value"><span v-threshold-if>当前还有 {{ chanceNum }}次 抽奖机会</span></div>
      <div class="draw-btn">
        <img :src="furnish.drawBtn ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/223338/24/2898/19533/61946f11E40826e26/a2e11c29a67b4717.png'" alt="" v-threshold-click="showTaskPopup" v-click-track="'hqgdcjjh'" />
      </div>
    </div>
    <div class="winners select-hover" :style="furnishStyles.winnersBg.value">
      <div class="winners-content">
        <div class="winner-list swiper-container" ref="swiperRef">
          <!-- <div class="swiper-wrapper" v-if="activityGiftRecords.length != 0">
            <div class="winner swiper-slide" v-for="(item, index) in activityGiftRecords" :key="index">
              <div>
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" v-if="!item.avatar" />
                <img v-else :src="item.avatar" alt="" />
                <span>{{ item.nickName }}</span>
              </div>
              <span>{{ item.prizeName }}</span>
            </div>
          </div> -->
          <vueDanmaku v-if="activityGiftRecords.length !== 0" ref="danmaku" v-model:danmus="activityGiftRecords" useSlot loop :channels="3" :speeds="150" class="danmaku">
            <template v-slot:dm="{ danmu }">
              <div class="winner">
                <img src="https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png" alt="" v-if="!danmu.avatar" />
                <img v-else :src="danmu.avatar" alt="" />
                <span>恭喜{{ danmu.nickName }}抽中了{{ danmu.prizeName }}</span>
              </div>
            </template>
          </vueDanmaku>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup v-model:show="showMyPrize" position="bottom">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 做任务弹窗  -->
  <VanPopup v-model:show="showTask" position="bottom">
    <DoTask @openShowGoShop="showGoShop = true" :times="chanceNum" :tasks="tasks" @close="showTask = false" :shopId="baseInfo.shopId" @refreshTask="refreshTask"></DoTask>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup v-model:show="showAward">
    <AwardPopup @openShowGoShop="showGoShop = true" :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
  <!-- 进店逛逛 -->
  <VanPopup teleport="body" v-model:show="showGoShop" position="bottom" z-index="10000">
    <GoShopPop v-if="showGoShop" @close="showGoShop = false"></GoShopPop>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import DoTask from '../components/DoTask.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { Task, CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import vueDanmaku from 'vue3-danmaku';
import GoShopPop from '../components/GoShopPop.vue';

import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const shopName = ref(baseInfo.shopName);

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};

// 抽奖次数
const chanceNum = ref(0);

const showMyPrize = ref(false);
// 进店逛逛
const showGoShop = ref(false);
const handler = Handler.getInstance();
onMounted(() => {
  handler.on('onGoShopOpen', () => {
    showGoShop.value = true;
  });
});

const tasks = reactive([] as Task[]);
const showTask = ref(false);
const showAward = ref(false);
const award = ref({
  prizeType: 0,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
});
const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/80021/chanceNum');
    chanceNum.value = data;
  } catch (error: any) {
    console.error(error);
  }
};

const myLucky = ref();
// 抽奖接口
const lotteryDraw = async () => {
  try {
    const res = await httpRequest.post('/80021/lotteryDraw');
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ?? '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      myLucky.value.stop(index);
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      myLucky.value.stop(index);
    }
  } catch (error: any) {
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    myLucky.value.stop(index);
    console.error(error);
  }
  getChanceNum();
};
const startCallback = async () => {
  lzReportClick('kscj');

  if (baseInfo.status === 1) {
    showToast('活动未开始');
    return;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return;
  }
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  lotteryDraw();
};
// 抽奖结束会触发end回调
const endCallback = () => {
  showAward.value = true;
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/80021/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
  } catch (error: any) {
    console.error(error);
  }
};

// 获取任务列表
const getTask = async () => {
  try {
    const { data } = await httpRequest.post('/80021/getTask');
    tasks.splice(0);
    tasks.push(...data);
  } catch (error: any) {
    console.error(error);
  }
};

const danmakuRef = ref(null);

// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/80021/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
  } catch (error: any) {
    console.error(error);
  }
};

// 展示任务弹窗
const showTaskPopup = () => {
  if (baseInfo.status === 1) {
    showToast('活动未开始');
    return;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return;
  }
  showTask.value = true;
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getChanceNum(), getPrizes(), getWinners()]);
    await getTask();
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();

// 做任务后刷新信息
const refreshTask = async () => {
  getChanceNum();
  getTask();
};
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  min-height: 3.7rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }
  .header-btn-all{
    margin-top: 1.84rem;
    margin-right:-0.18rem;
    .header-btn {
      height: 0.55rem;
      margin-bottom: 0.1rem;
    }
  }
}
.wheelAll{
  position: absolute;
  top:4.1rem;
  width: 100%;
  .wheel {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .wheel-img {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      object-fit: contain;
    }
  }

}
.draws-num {
  text-align: center;
  font-size: 0.24rem;
  margin-left: 3.15rem;
  margin-bottom: 0.04rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/175751/16/40543/3779/65015eb9F48865c0b/fd667614118cdd6a.png) no-repeat;
  background-size: 100%;
  width: 3.35rem;
  height: 0.89rem;
  padding-top: 0.12rem;
}
.margin0 {
  margin-bottom: 0;
}

.draw-btn {
  width: 5.3rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.31rem;
  height: 6.44rem;
  margin: 1.2rem auto 0;
  padding-top: 1.88rem;

  .winners-content {
    width: 6.3rem;
    height: 3.95rem;
    // background-color: #fff;
    border-radius: 0.3rem;
    margin: 0 auto;
    overflow: hidden;
    padding-top: 0.2rem;
  }
  .danmaku {
    width: 6.2rem;
    height: 3.95rem;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.winner {
  display: flex;
  align-items: center;
  height: 0.95rem;
  background-color: #ffe1ba;
  border-radius: 0.471rem;
  border: solid 0.03rem #ff472e;
  padding-left: 0.15rem;
  padding-right: 0.4rem;
  color: #333;
  font-size: 0.3rem;
  margin: 0.1rem 0;
  img {
    width: 0.73rem;
    height: 0.73rem;
    border-radius: 50%;
    margin-right: 0.2rem;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
