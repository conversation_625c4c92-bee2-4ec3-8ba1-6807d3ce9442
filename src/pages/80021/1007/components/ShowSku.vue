<template>
  <div class="rule-bk">
    <div class="title">
      <img :src="taskInfo[detail.taskType].label" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div v-if="detail.taskType === 8">
        <div class="tip-title">参与条件：</div>
        <div class="tip-text">
          <span
            >在
            <span class="color-cus">{{ dayjs(baseInfo.startTime).format('YYYY-MM-DD HH:mm:ss') }}~{{ dayjs(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
            期间，购买
            <span class="color-cus">{{ skuList.length ? '以下任意商品' : '店铺内任意商品' }}</span
            >，且订单状态为<span class="color-cus">已完成</span>
          </span>
        </div>
      </div>
      <div :class="['sku-list', detail.taskType === 8 ? 'sku-list8' : 'sku-list']">
        <div class="sku" v-for="item in skuList" :key="item.skuId" @click="goSkuPage(item.skuId)">
          <img :src="item.skuMainPicture" alt="" class="sku-img" />
          <div class="sku-name">{{ item.skuName }}</div>
          <div v-if="detail.taskType === 5 && detail.optWay === 1">
            <div v-if="!item.isOperated" class="sku-button" @click="followSku(item.skuId)">点我关注</div>
            <div v-else class="sku-button sku-button-op">已关注</div>
          </div>
          <div v-else-if="detail.taskType === 6">
            <div v-if="!item.isOperated" class="sku-button" @click="appointSku(item.skuId)">立即预约</div>
            <div v-else class="sku-button sku-button-op">已预约</div>
          </div>
          <div v-else-if="detail.taskType === 7 && detail.optWay === 1">
            <div v-if="!item.status" class="sku-button" @click="addSku(item.skuId)">立即加购</div>
            <div v-else class="sku-button sku-button-op">已加购</div>
          </div>
          <div v-else-if="detail.taskType === 8">
            <div class="price">￥{{ item.jdPrice }}</div>
          </div>
          <div v-else-if="detail.taskType === 9">
            <div v-if="!item.isOperated" class="sku-button" @click="shareSku(item.skuId)">立即分享</div>
            <div v-else class="sku-button sku-button-op">已分享</div>
          </div>
        </div>
        <div v-if="skuList.length > 0" class="load-more-all">
          <div class="load-more" @click="handleLoadMore">加载更多</div>
        </div>
      </div>
    </div>
    <div v-if="detail.taskType === 5">
      <!-- <div v-if="detail.optWay === 1" class="bottom-btn">
        已关注商品 <span class="num">{{ detail.taskUnitFinishCount }}</span
        >件，获得 <span class="num">{{ detail.taskFinishGiveAllLotteryCount }}</span
        >次抽奖机会
      </div> -->
      <div v-if="detail.optWay !== 1" class="bottom-btn" @click="followSku('')">一键关注商品</div>
    </div>
    <div v-if="detail.taskType === 7">
      <!-- <div v-if="detail.optWay === 1" class="bottom-btn">
        已加购商品 <span class="num">{{ detail.taskUnitFinishCount }}</span
        >件，获得 <span class="num">{{ detail.taskFinishGiveAllLotteryCount }}</span
        >次抽奖机会
      </div> -->
      <div v-if="detail.optWay !== 1" class="bottom-btn" @click="addSku('')">一键加购商品</div>
    </div>
    <div v-if="detail.taskType === 8" class="go-buy">
      <div class="bottom-btn">通过购物已获得{{ detail.taskFinishGiveAllLotteryCount }}次抽奖机会</div>
      <div class="bottom-btn go-shop" @click="gotoShopPage">立即进店购物</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import constant from '@/utils/constant';
import { addSkuToCart, gotoSkuPage } from '@/utils/platforms/jump';
import { callShare } from '@/utils/platforms/share';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { PropType, ref, inject } from 'vue';
import { isPreview } from '@/utils';

const baseInfo = inject('baseInfo') as BaseInfo;
const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) as string);

const pros = defineProps({
  detail: {
    type: Object as PropType<any>,
  },
});

interface Sku {
  skuId: string;
  skuMainPicture: string;
  skuName: string;
  jdPrice: number;
  isOperated?: boolean;
  status?: number;
}
const pageNum = ref(1);
const skuList = ref([] as Sku[]);

const emits = defineEmits(['close', 'refreshTask', 'openShowGoShop']);

const close = () => {
  emits('close');
};
const gotoShopPage = () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  emits('openShowGoShop');
};
const taskInfo = {
  5: { label: '//img10.360buyimg.com/imgzone/jfs/t1/121183/4/38184/7474/65016dfdF67e36524/ec520e497b603b1a.png', button: '去关注', buttonDIs: '已关注', getSkuApi: '/80021/getFollowSkuTaskSkuList' }, // 关注商品
  6: { label: '//img10.360buyimg.com/imgzone/jfs/t1/226902/40/7599/11482/6576da0fF721e3580/97af131d3fc8c545.png', button: '立即预约', buttonDIs: '已预约', getSkuApi: '/80021/getAppointSkuTaskSkuList' }, // 预约商品
  7: { label: '//img10.360buyimg.com/imgzone/jfs/t1/182592/7/37683/7440/65016dfdF986fb842/725ef46123a51142.png', button: '立即加购', buttonDIs: '已加购', getSkuApi: '/80021/getTaskAddSku' }, // 加购商品
  8: { label: '//img10.360buyimg.com/imgzone/jfs/t1/103053/28/38461/11897/65016dfdFdd9d2646/39d1f087db30515b.png', button: '去购买', buttonDIs: '已购买', getSkuApi: '/80021/getOrderSkuTaskSkuList' }, // 购买商品
  9: { label: '//img10.360buyimg.com/imgzone/jfs/t1/174800/21/39789/7389/6543502eFc992566e/d1dfb4fd911f3d4d.png', button: '去分享', buttonDIs: '已分享', getSkuApi: '/80021/getShareSkuTaskSkuList' }, // 分享商品
};

// 获取商品列表
const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post(taskInfo[pros.detail.taskType].getSkuApi, {
      taskId: pros.detail.id,
      pageNum: pageNum.value,
      pageSize: 20,
    });
    closeToast();
    if (res.code === 200) {
      skuList.value.push(...res.data.records);
      // 对skuList中jdPrice除与100
      skuList.value.forEach((item) => {
        item.jdPrice /= 100;
      });
    }
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
// 加载更多
const handleLoadMore = async () => {
  if (isPreview) {
    showToast('活动预览，仅供查看');
    return;
  }
  pageNum.value++;
  const params = {
    pageNum: pageNum.value,
    pageSize: 20,
  };
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 2000,
    overlay: true,
  });
  try {
    const res = await httpRequest.post(taskInfo[pros.detail.taskType].getSkuApi, { ...params, taskId: pros.detail.id });
    if (res.code === 200) {
      if (!res.data.records || res.data.records.length === 0) {
        showToast({
          message: '没有更多数据了',
          duration: 2000,
        });
        return;
      }
      skuList.value.push(...res.data.records);
      // 对skuList中jdPrice除与100
      skuList.value.forEach((item) => {
        item.jdPrice /= 100;
      });
    }
  } catch (error: any) {
    console.error(error);
  }
};
// 去商详页
const goSkuPage = (skuId: string) => {
  if (pros.detail.taskType === 8) {
    gotoSkuPage(skuId);
  }
};

const init = async () => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  if (pros.detail.skuList) {
    skuList.value = pros.detail.skuList;
  } else {
    await getSkuList();
  }
  closeToast();
};

// 关注商品
const followSku = async (skuId: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80021/followSku', { skuId });
    closeToast();
    if (res.code === 200) {
      showToast('关注成功');
      emits('refreshTask');
      if (!skuId) return;
      const index = skuList.value.findIndex((item) => item.skuId === skuId);
      skuList.value[index].isOperated = true;
    } else {
      showToast(res.message);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 预约商品
const appointSku = async (skuId: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80021/appointSku', { skuId });
    closeToast();
    if (res.code === 200) {
      showToast('预约成功');
      emits('refreshTask');
      if (!skuId) return;
      const index = skuList.value.findIndex((item) => item.skuId === skuId);
      skuList.value[index].isOperated = true;
    } else {
      showToast(res.message);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
// 加购商品
const addSku = async (skuId: string) => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/80021/addSku', { skuId });
    closeToast();
    if (res.code === 200) {
      showToast('加购成功');
      emits('refreshTask');
      if (!skuId) {
        addSkuToCart(skuList.value.map((item) => item.skuId));
        return;
      }
      addSkuToCart(skuId);
      const index = skuList.value.findIndex((item) => item.skuId === skuId);
      skuList.value[index].status = 1;
    } else {
      showToast(res.message);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};
// 分享商品
const shareSku = async (skuId: string) => {
  callShare({
    title: shareConfig.shareTitle,
    shareUrl: `https://item.m.jd.com/product/${skuId}.html`,
    afterShare: async () => {
      try {
        showLoadingToast({
          forbidClick: true,
          duration: 0,
        });
        const res = await httpRequest.post('/80021/shareSku', { skuId });
        closeToast();
        if (res.code === 200) {
          showToast('分享成功');
          emits('refreshTask');
          if (!skuId) return;
          const index = skuList.value.findIndex((item) => item.skuId === skuId);
          skuList.value[index].status = 1;
        } else {
          showToast(res.message);
        }
      } catch (error: any) {
        closeToast();
        console.error(error);
      }
    },
  });
};

init();
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 0.86rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.6rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }

  .tip-title {
    font-size: 0.3rem;
    color: #262626;
    font-weight: bold;
  }

  .tip-text {
    font-size: 0.24rem;
    color: #262626;
    font-weight: 500;
    margin-top: 0.27rem;

    .color-cus {
      color: #f2270c;
    }
  }

  .content {
    max-height: 10.4rem;
    width: 7rem;
    margin: 0.2rem auto 0;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/102250/3/42352/11653/65016dfeF263c3d2b/250af9864bd1312f.png) no-repeat;
    background-size: 100%;
    padding: 0.3rem 0.3rem 0 0.3rem;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
  }

  .sku-list {
    max-height: 9.7rem;
    min-height: 2.5rem;
    overflow-y: scroll;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding-bottom:0.2rem;
    .sku {
      width: 3.1rem;
      border-radius: 0.2rem;
      overflow: hidden;
      margin-bottom: 0.2rem;
      background-color: #fff;
      box-shadow: 0rem 0.04rem 0.05rem 0rem rgba(253, 99, 44, 0.46);
    }

    .sku-img {
      width: 3.1rem;
      height: 3.1rem;
      object-fit: cover;
    }

    .sku-name {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
      font-size: 0.3rem;
      line-height: 0.35rem;
      height: 0.7rem;
      color: #333333;
      margin: 0.2rem;
    }

    .sku-button {
      width: 1.86rem;
      height: 0.55rem;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/198040/4/39277/3952/65017bc7F0d560988/e67ffed4c850b15a.png) no-repeat;
      background-size: 100%;
      font-size: 0.28rem;
      color: rgb(255, 255, 255);
      border-radius: 0.23rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 0.3rem;
    }

    .sku-button-op {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/220521/9/35995/2744/65017bc7F5127e7b8/fb6ca860614f483e.png) no-repeat;
      background-size: 100%;
    }

    .price {
      font-size: 0.24rem;
      color: #f2270c;
      margin-left: 0.2rem;
      margin-bottom: 0.2rem;
    }
  }
  .sku-list8 {
    max-height: 8.7rem;
    padding-bottom: 0.6rem;
  }
  .load-more-all{
    width:100%;
    .load-more {
      width: 2rem;
      height: 0.4rem;
      line-height: 0.4rem;
      font-size: 0.25rem;
      text-align: center;
      background: linear-gradient(to right, #ff1f53, #ffd102);
      border-radius: 0.2rem;
      color: white;
      font-weight: 600;
      margin: auto;
    }
  }
  .bottom-btn {
    height: 1rem;
    text-align: center;
    line-height: 1rem;
    font-size: 0.24rem;
    color: #262626;
    background: #ffe3e3;
  }

  .go-buy {
    display: flex;

    .bottom-btn {
      flex: 0.5;
    }

    .go-shop {
      color: #fff;
      background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    }
  }
}
</style>
