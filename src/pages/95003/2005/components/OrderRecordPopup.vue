<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/231650/6/9890/1869/658574f0F8f26ae51/bb6ae5028b7717c3.png" alt="" class="text" />
      <div class="close" @click="emits('close')"></div>
    </div>
    <div class="h-[40vh]pb-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
      <div class="text-white">下单状态为已完成时，才可以领取对应奖励。</div>
      <div v-if="orderList.length" class="orderListDivAll">
        <div class="orderItemList" v-for="(order, index) in orderList" :key="index">

          <div class="orderBox">
            <div class="orderTopBox">
              <span class="orderNum">订单编号</span>
              <span class="orderTime">下单时间:{{ order.orderStartTime ? dayjs(order.orderStartTime).format('YYYY-MM-DD HH:mm:ss') : '&#45;&#45;' }}</span>
            </div>
            <div class="orderBottomBox">
              <span class="orderIdNum">{{order.orderId }}</span>
              <span class="orderPrice">￥{{order.orderPrice }}</span>
              <span class="orderStatus">{{order.orderStatus }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="noDataDiv text-white text-sm h-[80%] flex justify-center items-center">暂无订单记录哦～</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';

interface Order {
  orderId: string;
  orderStatus: string;
  orderPrice: string;
  orderEndTime: number;
  orderStartTime: string;
}

const orderList = ref<Order[]>([]);
const props = defineProps(['orderRestrainStatus']);
const emits = defineEmits(['close']);

const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/95003/getMyOrder');
    orderList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

!isPreview && getRecord();
</script>

<style lang="scss" scoped>
.rule-bk {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;
  min-height: 7rem;
  .text-white{
    padding-left: 0.33rem;
  }
  .noDataDiv{
    padding-top: 2rem;
  }
  .orderListDivAll {
    max-height: 5.30rem;
    overflow-y: scroll;

    .orderItemList {
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/105172/35/44995/1314/6503b473F030c0abf/73e798967da06461.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 6.90rem;
      height: 1.06rem;
      margin-left: calc(50% - 6.90rem / 2);
      margin-bottom: 0.14rem;
    }

    .orderBox {

      font-size: 0.2rem;
      color: #aaa;
      padding: 0 0.3rem;
      margin: 0 auto;

      .orderTopBox {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        height: 0.42rem;

        .orderNum {
          color: #999999;
          font-size: 0.19rem;
        }

        .orderTime {
          color: #999999;
          font-size: 0.19rem;
        }
      }

      .orderBottomBox {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        font-size: 0.3rem;
        color: #303030;
        height: 0.62rem;

        .orderIdNum {
          color: #262626;
          font-size: 0.23rem;
        }

        .orderPrice {
          color: #262626;
          font-size: 0.23rem;
        }

            .orderStatus {
          color: #262626;
          font-size: 0.23rem;
          text-align: right;
          width: 2rem;
        }
      }
    }

  }

  .title {
    position: relative;
    height: 1rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem 0.1rem 0.33rem;
    .text {
      height: 0.62rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }
}
</style>
