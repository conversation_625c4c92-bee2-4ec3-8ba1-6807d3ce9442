import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBg: '',
  pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/16682/40/20778/69621/668ccdecFc86a6f0a/25921e7117d57aa4.png',
  actBgColor: '#801e19',
  shopNameColor: '#fff',
  btnColor: '#cf1314',
  btnBgColor: '#fd8936',
  btnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/69102/18/19891/3425/65dd401eF25112cfc/34caf2d752cfd49c.png',
  btnBorderColor: '',
  wheelBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/226452/9/21165/6147/668cd35eFb1b60866/1b5448c2fec9ceac.png',
  wheelPanel: '//img10.360buyimg.com/imgzone/jfs/t1/243958/8/13617/50333/66821e7bF5c2ec8f3/9fe67865b9057ad5.png',
  wheelBg: '//img10.360buyimg.com/imgzone/jfs/t1/243958/8/13617/50333/66821e7bF5c2ec8f3/9fe67865b9057ad5.png',
  drawBtn: '',
  wheelTextColor: '#333333',
  drawsNum: '#fef2e4',
  drawsNumBg: '//img10.360buyimg.com/imgzone/jfs/t1/246540/8/12299/2318/667935d3Fd11462d0/675938cfe58f3724.png',
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/245564/40/3523/57672/65dd4cd6F665d6238/dc5b7d477f6dea1e.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/244441/16/13910/82329/6687c1d3F0d34415a/9d64b21616ace1d7.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/224067/39/19954/82404/6687c1d4Fd1c7fc09/9ac3a7d1875871f4.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/231066/38/22654/10368/6687c1d4F2269292c/0bd68b2716a5275c.png',
  wheelType: '2',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '消费满额抽奖(实付金额交易完成)';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
