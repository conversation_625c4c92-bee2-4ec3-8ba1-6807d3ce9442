<template>
  <div v-if="isShow" id="shopping">
    <div v-if="tabList.length > 0" class="tab-container">
      <div v-for="(item, index) in tabList" :key="index" class="tab-content" @click="handleSwitchTab(index)">
        <span :class="{ active: curTabIndex === index }">{{ item }}</span>
      </div>
    </div>
    <!-- tab 内容 -->
    <div v-if="tabContentList.length > 0" class="sku-container">
      <div v-for="(item, index) in tabContentList" :key="index" class="sku-item" @click="gotoSkuPage(item.skuId)">
        <div class="sku-image">
          <img v-click-track="{ code: 'ip_click', value: `${fromShopId}-sku-${item.skuId}` }" :src="item.skuImg" alt="" />
        </div>
        <div class="sku-name">
          <span v-click-track="{ code: 'ip_click', value: `skuId-${item.skuId}` }">{{ item.skuName }}</span>
        </div>
        <div class="sku-price">
          <span>￥</span>
          <span>{{ item.pricePrev }}.</span>
          <span>{{ item.priceNext }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { ref, onMounted, nextTick, Ref, inject } from 'vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast } from 'vant';
import { delayToast } from '../../common';
import { gotoSkuPage } from '@/utils/platforms/jump';

const { fromShopId } = inject<any>('pathParams');

const isShow: Ref<boolean> = ref(false);
const curTabIndex: Ref<number> = ref(0);
const tabList: Ref<any[]> = ref([]);
/**
 *切换tab
 */
const tabContentList: Ref<any[]> = ref([]);
const handleSwitchTab = (index: number): void => {
  curTabIndex.value = index;
  switch (index) {
    case 0:
      tabContentList.value = skuList1.value;
      break;
    case 1:
      tabContentList.value = skuList2.value;
  }
};
const skuList1: Ref<any[]> = ref([]);
const skuList2: Ref<any[]> = ref([]);
const tabContent_sku = async (): Promise<void> => {
  try {
    showLoadingToast({});
    const res = await httpRequest.post('/zhipu/babycare/member/getSkuList', {
      shopId: fromShopId,
    });
    let {
      sku1List,
      sku2List,
      monthOldName1,
      monthOldName2,
    } = res.data;
    tabList.value = [monthOldName1, monthOldName2];
    tabContentList.value = handleSkuList(sku1List);
    skuList1.value = handleSkuList(sku1List);
    skuList2.value = handleSkuList(sku2List);
    isShow.value = true;
  } catch (error: any) {
    await delayToast(error);
  } finally {
    closeToast();
  }
};
const handleSkuList = (array: any[]): any[] => {
  array.forEach((item) => {
    item.pricePrev = item.skuPrice.split('.')[0];
    item.priceNext = item.skuPrice.split('.')[1];
  });
  return array;
};
onMounted(() => {
  nextTick(() => {
    tabContent_sku();
  });
});
</script>

<style lang="scss" scoped>
#shopping {
  width: 7.1rem;

  .tab-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 0.16rem;
    box-sizing: border-box;

    .tab-content {
      &:nth-child(n + 2) {
        margin-left: 0.6rem;
      }

      span {
        font-size: 0.32rem;
        color: #b5b5b5;
      }

      .active {
        color: #3c3c3c;
      }
    }
  }

  .sku-container {
    width: 7.1rem;
    // background-color: #fff;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 0.12rem 0.12rem 0.3rem 0.15rem;
    box-sizing: border-box;
    margin-top: 0.54rem;

    .sku-item {
      width: 3.33rem;
      height: 4.29rem;

      &:nth-child(even) {
        margin-left: 0.13rem;
      }

      &:nth-child(n + 3) {
        margin-top: 0.4rem;
      }

      .sku-image {
        width: 3.33rem;
        height: 3.12rem;
        border-radius: 0.18rem;
        background-color: #e7e7e7;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .sku-name {
        margin-top: 0.16rem;
        line-height: 0.3rem;
        text-align: left;
        min-height: 0.62rem;

        span {
          font-size: 0.24rem;
          color: #272727;
          overflow: hidden; /*溢出隐藏*/
          text-overflow: ellipsis; /*溢出用省略号显示*/
          display: -webkit-box; /*作为弹性伸缩盒子模型显示*/
          -webkit-box-orient: vertical; /*设置伸缩盒子的子元素排列方式：从上到下垂直排列*/
          -webkit-line-clamp: 2; /*显示的行数*/
          line-clamp: 2;
        }
      }

      .sku-price {
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
        margin-top: 0.15rem;

        span {
          font-weight: bold;
          color: #ca8970;

          &:nth-child(1) {
            font-size: 0.17rem;
          }

          &:nth-child(2) {
            font-size: 0.27rem;
            line-height: 0.25rem;
          }

          &:nth-child(3) {
            font-size: 0.2rem;
          }
        }
      }
    }
  }
}
</style>
