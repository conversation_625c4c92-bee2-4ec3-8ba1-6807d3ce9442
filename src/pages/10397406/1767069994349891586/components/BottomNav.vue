<template>
  <div class="bottom-nav-list" :style="{ backgroundImage: `url(${jsonData.navBgImg}})` }">
    <div class="bottom-nav" v-for="(nav, index) in jsonData.navList" :key="nav">
      <img class="nav-img" :src="nav.navImg" @click="goLink(nav.navUrl, index)" v-click-track="`dban${index + 1}`" />
    </div>
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { ref, computed, toRefs, inject, reactive } from 'vue';
import { configData } from '../common';
import { showToast } from 'vant';

// 模块数据
const moduleName = 'BottomNav';
const { jsonData } = configData.value[moduleName];

const goLink = (link: string, index: number) => {
  if (index === 1) return;
  if (link) {
    window.location.href = link;
  } else {
    showToast('即将上线 敬请期待');
  }
};
</script>

<style lang="scss" scoped>
.bottom-nav-list {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 1.36rem;
  padding: 0.3rem 0.3rem 0.1rem 0.6rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  z-index: 2;
}

.bottom-nav {
}

.nav-img {
  width: 1.16rem;
  height: 1.16rem;
}
</style>
