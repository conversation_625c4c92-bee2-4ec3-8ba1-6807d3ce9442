<!-- 积分锁权 -->
<template>
  <div class="point-lock" :style="{ backgroundImage: `url(${jsonData.pointLockbgImg})` }">
    <img class="point-rule-button" :src="configData.CommonSetting.jsonData.ruleButtonImg" alt="" @click="showRule">
    <img class="point-record-button" :src="configData.CommonSetting.jsonData.lockButtonImg" alt=""
      @click="getLockPrizeData">
    <!-- 立即锁定 -->
    <img class="lock-button" v-if="!configData.PointLock.lockStatus" :src="jsonData.lockButtonImg" alt=""
      @click="showGuide()">
    <!-- 已锁定去下单 -->
    <img class="locked-button" v-else-if="configData.PointLock.lockStatus && !configData.PointLock.scoreOrderStatus"
      :src="jsonData.lockedButtonImg" alt="" @click="showOrder()">
    <!-- 点击领取 -->
    <img class="receive-button"
      v-else-if="configData.PointLock.lockStatus && configData.PointLock.scoreOrderStatus && !configData.PointLock.status"
      :src="jsonData.receiveButtonImg" alt="" @click="getPointLockPrize()">
    <!-- 已领取 -->
    <img class="received-button" v-else-if="configData.PointLock.status" :src="jsonData.receivedButtonImg" alt="">

    <!-- 规则弹窗 -->
    <RulePopup ref="rulePopup" :ruleInfo="rule" />
    <!-- 锁权记录弹窗 -->
    <LockPrizePopup ref="lockPrizePopup" :lockPrizeList="lockPrizeList.values" @goToSavePage="goToSavePage"
      @showCopyCard="showCopyCard" />
    <!-- 锁权攻略弹窗 -->
    <GuidePopup ref="guidePopup" @lockPrize="lockPrize()" />
    <!-- 活动商品弹窗 -->
    <OrderPopup ref="orderPopup" :lockSkuList="configData[moduleName].skuList" />
    <!-- 中奖结果弹窗 -->
    <DrawResPopup ref="winPrizePopup" :drawRes="drawResInfo.value" @goToSavePage="goToSavePage" />
  </div>
</template>

<script lang="ts" setup>
/* eslint-disable */
import { Ref, inject, reactive, ref } from 'vue';
import { configData, getConfigData, getPrizeRecord } from '../common';
import RulePopup from './popup/RulePopup.vue';
import LockPrizePopup from './popup/LockPrizePopup.vue';
import GuidePopup from './popup/GuidePopup.vue';
import OrderPopup from './popup/OrderPopup.vue';
import DrawResPopup from './popup/DrawResPopup.vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const goToSavePage = inject('goToSavePage') as () => void;
const emits = defineEmits(['showMemberPopup']);

// 模块数据
const moduleName = 'PointLock';
const { jsonData, rule } = configData.value[moduleName];

// 显示活动规则弹窗
const rulePopup = ref();
const showRule = () => {
  rulePopup.value.show = true;
};

// 获取锁权记录返回信息
const lockPrizeList = reactive({
  values: [],
});
// 锁权记录弹窗
const lockPrizePopup = ref();
const getLockPrizeData = async () => {
  lockPrizeList.values = await getPrizeRecord(3);
  lockPrizePopup.value.show = true;
};

const isDrawing: Ref<Boolean> = ref(false);

// 锁权攻略弹窗
const guidePopup = ref();
const showGuide = () => {
  // 前置条件
  if (!configData.value.activityContent.memberLevel) {
    emits('showMemberPopup');
    return;
  }
  if (!configData.value.PointLock.startFlag) {
    showToast('活动未开始');
    return;
  }
  if (configData.value.PointLock.overFlag) {
    showToast('活动已结束');
    return;
  }
  // 不符合参与条件
  if (configData.value.PointLock.activityLevel.indexOf(configData.value.activityContent.memberLevel) === -1) {
    showToast('不符合参与条件');
    return;
  }
  // 积分小于锁定所需积分
  if (configData.value.PointExchange.score < configData.value.PointLock.score) {
    showToast('积分不足，去赚积分吧');
    return;
  }
  guidePopup.value.show = true;
};

// 立即锁定
const lockPrize = async () => {
  if (isDrawing.value) return;
  isDrawing.value = true;
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/dingzhi/chunzhen/gift/lockScore');
    closeToast();
    // 锁定成功
    if (res.data.lockStatus) {
      configData.value.PointExchange.score = res.data.score;
      // 关闭锁权攻略弹窗
      guidePopup.value.show = false;
      showToast('锁定成功');
      // 按钮变为已锁定去下单
      await getConfigData('PointLock');
    } else {
      showToast('锁定失败，请刷新后重试');
    }
    isDrawing.value = false;
  } catch (error: any) {
    closeToast();
    showToast(error.message);
    isDrawing.value = false;
  }
};

// 下单商品弹窗
const orderPopup = ref();
const showOrder = () => {
  orderPopup.value.show = true;
};

// 兑换结果
const winPrizePopup = ref();
const drawResInfo = reactive({
  value: {},
});

// 领取
const getPointLockPrize = async () => {
  if (isDrawing.value) return;
  isDrawing.value = true;
  if (!configData.value.activityContent.memberLevel) {
    emits('showMemberPopup');
    isDrawing.value = false;
    return;
  }
  if (!configData.value.PointLock.startFlag) {
    showToast('活动未开始');
    return;
  }
  if (configData.value.PointLock.overFlag) {
    showToast('活动已结束');
    return;
  }
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/dingzhi/chunzhen/gift/exchangeLockScore');
    closeToast();
    // 领取成功
    if (res.data.sendStatus) {
      drawResInfo.value = res.data;
      winPrizePopup.value.show = true;
    } else {
      showToast('领取失败，请刷新后重试');
    }
    await getConfigData('PointLock');
    isDrawing.value = false;
  } catch (error: any) {
    closeToast();
    showToast(error.message);
    isDrawing.value = false;
  }
};

// 展示卡密
const showCopyCard = (info: any) => {
  drawResInfo.value = info;
  lockPrizePopup.value.show = false;
  winPrizePopup.value.show = true;
};

</script>

<style lang="scss" scoped>
.point-lock {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 6.66rem * 1.05;
  height: 6.2rem * 1.05;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  margin-top: 0.4rem;
  margin-left: 0.2rem;
  // background-color: aliceblue;

  .point-rule-button {
    position: absolute;
    top: 0.65rem;
    right: 0.27rem;
    width: 1.11rem *1.05;
    height: 0.455rem *1.05;
  }

  .point-record-button {
    position: absolute;
    top: 1.25rem;
    right: 0.27rem;
    width: 1.11rem *1.05;
    height: 0.455rem *1.05;
  }

  .lock-button {
    position: absolute;
    top: 5.15rem;
    width: 3.46rem;
    height: 0.62rem;
  }

  .locked-button {
    position: absolute;
    top: 5.15rem;
    width: 3.34rem;
    height: 0.67rem;
  }

  .receive-button {
    position: absolute;
    top: 5.15rem;
    width: 2.47rem;
    height: 0.68rem;
  }

  .received-button {
    position: absolute;
    top: 5.15rem;
    width: 2.47rem;
    height: 0.68rem;
  }
}
</style>
