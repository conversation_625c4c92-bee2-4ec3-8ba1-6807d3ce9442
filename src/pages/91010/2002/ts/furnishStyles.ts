import { computed, reactive } from 'vue';

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  prizeBg: '', // 奖品背景
  ruleBtnBg: '', // 规则背景
  disableShopName: 0,
  skuListDeco: [],
  joinVipPopBg: '', // 入会弹窗
  submitTipPopBg: '', // 提交信息提示弹窗
  submitSuccPopBg: '', // 提交信息成功弹窗
  reviewSuccPopBg: '', // 图片审核成功弹窗
  otherPopBg: '', // 动态文案弹窗背景
  otherTextPop: '', // 弹窗动态文案颜色
  infoUploadBg: '', // 申请流程1用户资料上传背景
  applyTitleBg: '', // 申请流程标题
  stepTwpTitleBg: '', // 申请流程2标题
  stepTwpTopBG: '', // 申请流程2头部背景
  stepTwpCenterBG: '', // 申请流程2中部背景
  stepTwpBottomBG: '', // 申请流程2下部背景
  infoTipBg: '', // 档案说明图片
  ruleBg: '', // 活动规则说明
  reviewResultPop: '', // 审核结果
  myPrizePop: '', // 我的奖品
  submitFailPopBg: '', // 不符合老客身份
  infoPop: '', //  填写档案弹窗
  infoPopQuery: '', // 查看档案弹窗
  upLoadPop: '', // 上传出生证明弹唱
  orderFailPop: '', // 订单审核失败
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));
export default {
  pageBg,
  shopNameColor,
  headerBtn,
};
