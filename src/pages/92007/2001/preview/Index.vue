<template>
  <div class="page-bg">
    <div class="kv">
      <div class="btn-list">
        <div class="btn1"></div>
        <div class="btn2"></div>
      </div>
      <img :src="decoData.gitft1" class="gift1" v-if="sixPrizeOpen" />
      <img :src="decoData.gitft2" class="gift2" v-if="twPrizeOpen" />
      <img :src="decoData.step" class="step" />
      <!-- 锁权按钮 -->
      <img :src="decoData.lockBtn" class="lockBtn" />
      <div class="content-text"></div>
      <div class="sku-bg">
        <div class="tab-box">
          <div :class="quanhu" @click="handleQuanhuClick"></div>
          <div :class="qihu" @click="handleQihuClick"></div>
        </div>
        <div class="sku-list-box" v-if="skuListType === 1">
          <div class="sku-item" v-for="(item, index) in quanhuSkuList" :key="index">
            <img :src="item.skuMainPicture" class="sku-img" />
            <div class="sku-name">{{ item.skuName }}</div>
            <img :src="decoData.goBuyBtn" class="buy-button" />
          </div>
        </div>
        <div class="sku-list-box" v-if="skuListType === 2">
          <div class="sku-item" v-for="(item, index) in qihuSkuList" :key="index">
            <img :src="item.skuMainPicture" class="sku-img" />
            <div class="sku-name">{{ item.skuName }}</div>
            <img :src="decoData.goBuyBtn" class="buy-button" />
          </div>
        </div>
      </div>
      <img :src="decoData.goShopBtn" class="go-shop-btn" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, reactive, computed } from 'vue';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import { showToast } from 'vant';

const decoData = reactive((inject('decoData') as any) || {});
const activityData = inject('activityData') as any;

const isLoadingFinish = ref(false);

const kvImg = computed(() => `url(${decoData.kv})`);
const skuBkImage = computed(() => `url(${decoData.skuBK})`);
const ruleBkImage = computed(() => `url(${decoData.ruleBk})`);

const sixPrizeOpen = ref(true);
const twPrizeOpen = ref(true);
const lockConfirmShow = ref(false);
const lockStatus = ref(0); // 锁权状态 0-未锁权 1-已锁权 -1-无法参与（已参与了1期）4-库存不足
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const successPopup = ref(false);
const addressPopup = ref(false);
const gift1Stock = ref(0);
const gift2Stock = ref(0);
const thresholdShow = ref(false);
const orderStartTime = ref('');
const orderEndTime = ref('');
const toSaveAddress = () => {
  successPopup.value = false;
  addressPopup.value = true;
};
const quanhuSkuList = ref<any[]>([]);
const qihuSkuList = ref<any[]>([]);
// 1 全护  2 启护
const skuListType = ref(1);
const quanhu = ref('quanhu-act');
const qihu = ref('qihu');

// 点击全护
const handleQuanhuClick = () => {
  if (quanhu.value === 'quanhu-act') {
    return;
  }
  quanhu.value = 'quanhu-act';
  skuListType.value = 1;
  qihu.value = 'qihu';
};
// 点击启护
const handleQihuClick = () => {
  if (qihu.value === 'qihu-act') {
    return;
  }
  quanhu.value = 'quanhu';
  skuListType.value = 2;
  qihu.value = 'qihu-act';
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const isCreateImg = ref(false);
const createImg = async () => {
  showSelect.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      decoData[item] = data[item];
    });
    console.log('C', decoData);

    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    sixPrizeOpen.value = data.sixPrizeOpen;
    twPrizeOpen.value = data.twPrizeOpen;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    sixPrizeOpen.value = activityData.sixPrizeOpen;
    twPrizeOpen.value = activityData.twPrizeOpen;
  }
  if (decoData) {
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
const toast = () => {
  showToast('活动预览，仅供参考');
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.page-bg {
  background: #dfd2bf;
}
.kv {
  position: relative;
  background-image: v-bind(kvImg);
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.5rem;
  padding-top: 2rem;
  //height: auto;
  min-height: 100vh;
  .btn-list {
    position: absolute;
    top: 0.4rem;
    right: 0;
    .btn1 {
      width: 1.2rem;
      height: 0.32rem;
      margin-bottom: 0.15rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/234256/32/18067/2908/665832c6Fa6eb5758/79e4908337570dc8.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .btn2 {
      width: 1.2rem;
      height: 0.32rem;
      margin-bottom: 0.15rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/238551/1/10543/3242/665832c6Fa3f4d16d/184d783969f778c9.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .gift1 {
    width: 7.3rem;
    margin: 0 0.1rem 0.2rem;
  }
  .gift2 {
    width: 7.3rem;
    margin: 0 0.1rem 0.2rem;
  }
  .step {
    width: 7.33rem;
    margin: 0 0.1rem -0.68rem;
  }
  .lockBtn {
    width: 4rem;
    margin: 0 auto;
  }
  .content-text {
    width: 7.5rem;
    height: 3.54rem;
    text-align: left;
    margin: 0rem auto;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/283064/23/16251/28756/67f36515Fca4a144e/4b426c36a3cc3dfa.png') no-repeat;
    background-image: v-bind(ruleBkImage);
    background-size: 100%;
    margin-top: -0.03rem;
    position: relative;
  }
  .sku-bg {
    //width: 7.2rem;
    height: 12rem;
    margin: 0 auto;
    background-image: v-bind(skuBkImage);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-top: 1.5rem;
    .tab-box {
      width: 6.08rem;
      height: 0.68rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/240897/15/9717/5334/6656cc38F4cb72912/c4de7da62153af54.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: relative;
      .quanhu-act {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/242148/14/7821/18934/6656cc37Ffff1d6b4/12428ad5c441e12f.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .qihu-act {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/230058/17/18549/5047/6656cc37Fb476dbcb/a91f92d3678a4daa.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .quanhu {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/192454/4/45171/7450/6656cc37Fd1c7abd8/9a15a9a0707c0fba.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 2.41rem;
        background-position: center;
        background-repeat: no-repeat;
      }
      .qihu {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/187050/36/45647/5511/6656cc37F5543637e/fc6d0df610e810fa.png');
        width: 2.93rem;
        height: 0.49rem;
        background-size: 1.54rem;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
    .sku-list-box {
      width: 7rem;
      height: 8rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      overflow-y: scroll;
      margin-top: 0.3rem;
      .sku-item {
        width: 3rem;
        height: 4rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .sku-img {
          width: 3rem;
        }
        .sku-name {
          color: #6f492c;
          height: 0.5rem;
          line-height: 0.5rem;
          width: 3rem;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .buy-button {
          width: 2rem;
        }
      }
    }
  }
  .join-btn {
    width: 5rem;
    margin: 0 auto;
    margin-top: 0.3rem;
  }
  .go-shop-btn {
    width: 7.5rem;
    margin-top: 0.3rem;
  }
}
.success-bk {
  position: relative;
  .success-img {
    width: 6.5rem;
    height: auto;
  }
  .content-text-box {
    position: absolute;
    width: 5rem;
    height: 3rem;
    left: 50%;
    transform: translate(-50%);
    bottom: 2.3rem;
    font-size: 0.24rem;
    text-align: center;
    color: #8c5c35;
    .see-text {
      height: 0.8rem;
    }
    .see-content {
      line-height: 0.4rem;
    }
  }
  .btn {
    position: absolute;
    top: 6.2rem;
    left: 50%;
    transform: translate(-50%);
    width: 2rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
  .close-btn {
    position: absolute;
    bottom: 0.1rem;
    left: 50%;
    transform: translate(-50%);
    width: 0.7rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
}
.threshold-bk {
  position: relative;
  .threshold-img {
    width: 6.5rem;
    height: auto;
  }
  .btn {
    position: absolute;
    top: 6.2rem;
    left: 50%;
    transform: translate(-50%);
    width: 2rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
  .close-btn {
    position: absolute;
    bottom: 0.1rem;
    left: 50%;
    transform: translate(-50%);
    width: 0.7rem;
    height: 0.6rem;
    //background: #3cc51f;
  }
}
</style>
<style>
@font-face {
  font-family: 'FZZZHJT';
  font-style: normal;
  font-weight: normal;
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZZZHJT/FZZZHJT.TTF');
  font-display: swap;
}
* {
  font-family: 'FZZZHJT';
}
</style>
