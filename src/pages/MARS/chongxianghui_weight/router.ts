import path from 'path';
import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('./pages/home/<USER>'),
  },
  {
    path: '/create',
    name: 'CreatePet',
    component: () => import('./pages/home/<USER>'),
  },
  {
    path: '/select',
    name: 'SelectPet',
    component: () => import('./pages/home/<USER>'),
  },
  {
    path: '/photo',
    name: 'Photo',
    component: () => import('./pages/photo/Index.vue'),
  },
  {
    path: '/photoerror',
    name: 'PhotoError',
    component: () => import('./pages/photo/error.vue'),
  },
  {
    path: '/question',
    name: 'Question',
    component: () => import('./pages/question/Index.vue'),
  },
  {
    path: '/step1',
    name: 'Step1',
    component: () => import('./pages/question/step1.vue'),
  },
  {
    path: '/step2',
    name: 'Step2',
    component: () => import('./pages/question/step2.vue'),
  },
  {
    path: '/loadding',
    name: 'Loadding',
    component: () => import('./pages/result/loadding.vue'),
  },
  {
    path: '/result',
    name: 'Result',
    component: () => import('./pages/result/Index.vue'),
  },
  {
    path: '/post',
    name: 'Post',
    component: () => import('./pages/result/post.vue'),
  },
  {
    path: '/pageerror',
    name: 'Pageerror',
    component: () => import('./pages/pageError.vue'),
  },
  {
    path: '/end',
    name: 'End',
    component: () => import('./pages/end.vue'),
  },
  {
    path: '/iframe',
    name: 'iframe',
    component: () => import('./pages/result/iframe.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
    name: 'notFound',
    hidden: true,
  },
];

function initializeRouter() {
  return createRouter({
    history: createWebHashHistory(`${process.env.VUE_APP_PATH_PREFIX_NO_CDN}MARS/chongxianghui_weight`),
    routes,
  });
}
export default () => initializeRouter();
