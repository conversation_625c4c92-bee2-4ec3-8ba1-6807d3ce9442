<template>
  <div id="container">
    <!-- 用动画如果在组件页内没有根节点，组件会加载失败，页面清空，可以不用动画或者自己写一个动画 -->
    <Transition enter-active-class="animate__animated animate__fadeIn" mode="out-in">
      <RouterView></RouterView>
    </Transition>
    <Disclaimers2 :isShow="isShowDisclaimers" @closePopup="isShowDisclaimers = false" @showprivacy="showprivacy" @showtermsOfUse="showtermsOfUse"/>
    <LinkPopup :isShow="isShowLinkPopup" :type="linkpoupType" @closePopup="isShowLinkPopup = false"/>
  </div>
</template>

<script lang="ts" setup>

import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { PAGE_CONFIG } from './config/config';
import Disclaimers2 from './components/Disclaimers2.vue';
import LinkPopup from './components/LinkPopup.vue';
import { lzReportClick } from '@/pages/MARS/lzReport';

provide('PAGE_CONFIG', PAGE_CONFIG);

const isShowDisclaimers = ref(false);
const isShowDisclaimersnNo = ref(0);
const isShowLinkPopup = ref(false);
const linkpoupType = ref('');

const showprivacy = () => {
  linkpoupType.value = 'privacyLink';
  isShowLinkPopup.value = true;
  // isShowDisclaimers.value = false;
};
const showtermsOfUse = () => {
  linkpoupType.value = 'termsOfUse';
  isShowLinkPopup.value = true;
  // isShowDisclaimers.value = false;
};
const openPopup = () => {
  console.log(123);
  if (isShowDisclaimersnNo.value) return;
  isShowDisclaimers.value = true;
  isShowDisclaimersnNo.value += 1;
};
provide('openPopup', openPopup);
lzReportClick('mainPage');
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Medium.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
#container {
  width: 100vw;
  min-height: 100vh;
  max-width: 100vw;
  line-height: 1;
  font-family: SourceHanSansCN;
  overflow-x: hidden;
}
</style>

<style lang="scss" scoped>
/* 隐藏Webkit内核浏览器的滚动条 */
::-webkit-scrollbar {
  width: 0.09rem;
}
::-webkit-scrollbar-thumb {
  background-color: #123c9d;
  border-radius: 0.04rem;
}
// 禁止页面回弹行为
html,
body {
  overscroll-behavior-y: none;
  overscroll-behavior-x: none;
}

.van-popup {
  width: 7.5rem;
  background: none;
  overflow-y: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.van-popup--center {
  max-width: 100%;
}
.van-picker {
  width: 100%;
}
.add-btn {
  width: 1.38rem;
  height: 1.36rem;
  position: fixed;
  right: 0;
  bottom: 1.5rem;
}
.bottom-tab-view {
  width: 7.5rem;
  height: 1.32rem;
  background: #fff;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  position: fixed;
  left: 0;
  bottom: 0;
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/253761/40/8156/6692/67775ce8Ff34cdcaf/977461fdf93fd06f.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  display: flex;
}
</style>
