<template>
    <div class="loading-box">
      <van-loading type="spinner" size="1.28rem" color="#ec001a" text-size="0.28rem" textColor="#000" vertical>正在提交照片进行审核...</van-loading>
      <div style="width: 7.5rem;text-align: center;font-size: 0.28rem;margin-top: 0.2rem;">预计等待1min...</div>
      <div class="loading-info-box" :style="`background-color: ${PAGE_CONFIG.mainBgColor};color: ${PAGE_CONFIG.mainTextColor}`">
        <div class="loading-info-title">有趣的知识</div>
        <img class="loading-info-dog" :src="require(`../asset/loadingDog${index + 1}.png`)"/>
        <div class="loading-info-text">{{loaddingText}}</div>
      </div>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import LoadingSpinner from './LoadingSpinner.vue';
import { getPetBreed, getPetList, getCheckUpResult, getQaInfo, uploadToothImg, saveQaInfo, savePetInfo } from '../config/api';

const store = useStore<RootState>();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');

const props = defineProps({
  pageNo: {
    type: Number,
    default: 1,
  },
});

const index = ref(Math.floor(Math.random() * PAGE_CONFIG[`LoadingTextList${props.pageNo}`].length));
const loaddingText = ref(PAGE_CONFIG[`LoadingTextList${props.pageNo}`][index.value]);

const emits = defineEmits(['toggleComponent']);

const init = () => {
  setInterval(() => {
    if (index.value === PAGE_CONFIG[`LoadingTextList${props.pageNo}`].length - 1) {
      index.value = 0;
      loaddingText.value = PAGE_CONFIG[`LoadingTextList${props.pageNo}`][index.value];
    } else {
      index.value += 1;
      loaddingText.value = PAGE_CONFIG[`LoadingTextList${props.pageNo}`][index.value];
    }
  }, 10000);
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
