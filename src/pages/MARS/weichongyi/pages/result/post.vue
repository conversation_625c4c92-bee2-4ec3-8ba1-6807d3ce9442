<template>
  <div class="main-view" style="overflow-y: scroll;padding-top: 0.43rem;overflow-x: hidden;">
    <img v-if="imgurl" style="width: 7.5rem;height: auto;margin-bottom: 3rem;" :src="imgurl">
    <div class="bottom-tip">长按保存</div>
    <img class="common-btn" style="top: 25.4rem;" :src="require('../../asset/backbtn.png')" @click="router.back()"/>
    <div id="poster">
      <img :src="require('../../asset/logo.png')" class="post-logo-img"/>
      <div class="post-ai-res-bg" :style="{ backgroundImage: `url(${require('../../asset/posterAiResBg.png')})`,color: `${PAGE_CONFIG.mainBgColor}`}">
        <div class="ai-res-time">分析时间：{{ dayjs().format('YYYY-MM-DD') }}</div>
        <div class="ai-res-desc">我们使用人工智能分析了【{{PetInfo?.petNick || qustionRes?.petNick}}】的牙齿照片，结果显示:</div>
        <div class="ai-tooth-img" :style="`background-image: url(${toothImg})`"></div>
        <img class="num-text" style="top: 650px;" :src="require(`../../asset/num/${aiRes?.teethScanned}.png`)">
        <img class="num-text" style="top: 819px;" :src="require(`../../asset/num/${aiRes?.tartarTeethCount}.png`)">
        <img class="num-text" style="top: 988px;" :src="require(`../../asset/num/${aiRes?.gumInflammationCount}.png`)">
      </div>
      <div class="post-question-bg" :style="{ backgroundImage: `url(${require('../../asset/posterQustionResBg.png')})`,color: `${PAGE_CONFIG.mainBgColor}` }">
        <img class="res-icon" v-if="qustionRes?.ifHalitosis" style="top: 277px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/274026/20/19905/1637/67ff27d7Fc23b1bd6/95da037f2aa09106.png"/>
        <img class="res-icon" v-else style="top: 277px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/272434/11/21899/910/67ff2c27F80fc8caf/20590e3c2c740675.png"/>
        <img class="res-icon" v-if="qustionRes?.ifBleeding" style="top: 422px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/281715/7/20897/1541/67ff27d7F95674187/2fc1183dc0ebc2bc.png"/>
        <img class="res-icon" v-else style="top: 422px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/277384/33/21871/893/67ff2c27F8bb5f3fc/3aab8ab4f583f5d6.png"/>
        <img class="res-icon" v-if="qustionRes?.ifDiscomfort" style="top: 555px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/283072/13/20581/1912/67ff27d7F78ef96f5/e90f2053c3e817fa.png"/>
        <img class="res-icon" v-else style="top: 555px;" src="https://img10.360buyimg.com/imgzone/jfs/t1/275504/24/21019/1160/67ff2c27F1510aa62/ec543204d7e15c4d.png"/>
        <img class="post-qustion-tips-box" v-if="!qustionRes?.ifHalitosis && !qustionRes?.ifBleeding && !qustionRes?.ifDiscomfort" :src="require('../../asset/resdesc1.png')" />
        <img class="post-qustion-tips-box" v-else :src="require('../../asset/resdesc2.png')" />
      </div>
      <div class="post-bottom-box">
        <div style="margin-top: 50px;">
          识别右侧二维码<br>了解狗狗口腔健康问题潜在迹象
        </div>
        <div class="qrcode-box">
          <img class="code-img" :src="qrCodeActUrl"/>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, computed, ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { RootState } from '../../store/state';
import VueQrcode from '@chenfengyuan/vue-qrcode';
import html2canvas from 'html2canvas';
import dayjs from 'dayjs';
import { httpRequest } from '@/utils/service';
import { saveCheckResultImg, marsWxUploadToOss } from '../../config/api';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

const store = useStore<RootState>();

const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = computed(() => store.state.petInfo);
const toothImg = ref(store.state.toothImg);
const { checkId } = route.query;

const qustionRes = computed(() => store.state.qustionRes);
const aiRes = computed(() => store.state.aiRes);
// 目前是测试链接，发正式后需要替换
const qrCodeActUrl = process.env.VUE_APP_WEICHONGYI_QRCODE;
const imgurl = ref('');
const config = {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
};
const takephotos = async () => {
  const save2 = document.getElementById('poster') as HTMLAnchorElement;
  console.log(save2.offsetWidth, save2.offsetHeight);
  await html2canvas(save2, {
    backgroundColor: null, // 设置图片背景为透明
    scale: 2,
    width: save2.offsetWidth,
    height: save2.offsetWidth / 0.31,
    allowTaint: true,
    useCORS: true,
  }).then((canvas:any) => {
    const context: any = canvas.getContext('2d');
    context.mozImageSmoothingEnabled = false;
    context.webkitImageSmoothingEnabled = false;
    context.msImageSmoothingEnabled = false;
    context.imageSmoothingEnabled = false;
    const src64: any = canvas.toDataURL();
    const newImg: any = document.createElement('img');
    newImg.crossOrigin = 'Anonymous';
    newImg.src = src64;
    imgurl.value = newImg.src;
    canvas.toBlob(async (blob: Blob) => {
      const formData = new FormData();
      formData.append('file', blob, 'babyAvatar.png');
      const res = await marsWxUploadToOss(formData);
      saveCheckResultImg({
        checkId,
        checkResultImg: res.data,
      }).then();
    });
  });
};

const init = () => {
  takephotos();
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}
</style>
