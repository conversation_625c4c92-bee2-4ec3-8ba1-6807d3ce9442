<template>
  <div class="main-view" style="display: flex;align-items: center;">
    <img style="width: 7.5rem;" :src="require(`../../asset/pageError.png`)" @click="router.push('/')"/>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();
</script>
<style lang="scss" scoped>
@import '../../config/page.scss';
</style>
<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none;
}
</style>
