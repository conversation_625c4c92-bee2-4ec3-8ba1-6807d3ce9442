import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  // 活动主图
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/272808/35/18804/88215/67f7af74Fd2d50607/e54eb177405316fa.png',
  // 页面背景图
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/236087/37/11174/14302/6596861eF8a7c5840/596ef1d14b83d307.png',
  // 页面背景颜色
  actBgColor: '',
  // 活动规则图
  ruleImg: '//img10.360buyimg.com/imgzone/jfs/t1/279162/31/18263/1717/67f7af74F616737c9/aace214116988f93.png',
  // 活动详情页主图
  detailsKvImg: '//img10.360buyimg.com/imgzone/jfs/t1/285141/6/17407/17476/67f7af73F3286d1c0/c0c5e5715bc31c48.png',
  // 活动详情页颜色
  detailsBgColor: '#e6e4e9',
  // 活动详情页主图
  details2KvImg: '//img10.360buyimg.com/imgzone/jfs/t1/279887/30/18124/17397/67f7af74Fb11372d6/70303903b65946fa.png',
  // 活动详情页颜色
  details2BgColor: '#e6e4e9',
  // 申领按钮
  joinBtn: '//img10.360buyimg.com/imgzone/jfs/t1/273229/34/18602/859/67f7af74F27b1bdba/b41e450100d96c49.png',
  // 店铺名称颜色
  shopNameColor: '',
  disableShopName: 0,
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/240530/40/2124/15952/6594c38aF6498196b/94225ea5e9a7ae85.jpg',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员令牌首购';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
