<template>
  <div class="pageBg relative" style="background-repeat: no-repeat!important" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div>
      <div class="relative header-kv">
        <div class="box-border flex flex-row justify-between z-10 absolute w-full">
          <div class="text-white text-sm">
            <span v-if="furnish.disableShopName === 1" >{{ shopName || 'xxx自营旗舰店' }}</span>
          </div>
          <div class="btnAllClass flex flex-col">
            <div class="btnClass" @click="showRule = true" :style="furnishStyles.ruleBtnBg.value"></div>
            <div class="btnClass" @click="showOrderRecord = true" :style="furnishStyles.myOrderBtnBg.value"></div>
            <div class="btnClass" @click="showReceiveRecord = true" :style="furnishStyles.myPrizeBtnBg.value"></div>
            <div class="btnClass" @click="showGoods = true" :style="furnishStyles.goodsBtnBg.value"></div>
          </div>
        </div>
      </div>
      <div class="prizeBox" :style="furnishStyles.prizeAreaBg.value">
        <div class="countDownDivAll" :style="furnishStyles.countDownBg.value">
          <div class="text-class text">距活动结束剩余：</div>
          <div class="count-down">{{countdownTime}}</div>
        </div>
        <div class="mt-3.5 goBuyClass" :style="furnishStyles.toBuyBtn.value" @click="toast"/>
        <div class="pl-2 mt-3.5 pr-3 prizeListAllClass">
          <div class="topTips" :style="furnishStyles.pointTextColor.value">
            <div class="customFont text-xs font-bold mb-0.5">您当前累计消费0元，待使用0元，距离下一个大奖还差{{nextPotCount}}元</div>
            <div class="text-xs">温馨提示:确认收货后才计算进度哦~</div>
          </div>
          <div class="prizeListClass">
            <div class="flex flex-row items-center mt-3" v-for="(item, index) in ladderInfoList" :key="index">
              <div v-if="index === 0" class="relative">
                <img class="h-4 mr-2.5 " :src="IMAGE_MAP.STEP_ICON" alt="">
                <ul class="process processDiv" v-if="ladderInfoList.length > 1">
                  <li v-for="item in 8" :key="item"></li>
              </ul>
            </div>
              <div v-else class="relative">
                <div class="circle mr-2.5 ml-0.5"></div>
                <ul class="process" v-if="index !== ladderInfoList?.length - 1" >
                  <li v-for="item in 8" :key="item"></li>
                </ul>
              </div>
              <div class="prizeItem bg-white w-full rounded p-2 chat relative">
                <div class="flex flex-row relative">
                  <div class="prizeImgDivAll">
                    <div class="imgBox">
                      <img class="prizeImgDiv w-24" :src="item.ladderImg ? item.ladderImg : 'https://img10.360buyimg.com/imgzone/jfs/t1/255070/30/12893/8403/6788ba46F1dce28a3/65431093a30bacdb.png'" alt="">
                    </div>
<!--                    <img class="bottom-0 absolute w-24" :src="IMAGE_MAP.PRIZE_BORDER" alt="">-->
                    <div class="absolute bottom-0 text-center w-24 text-white text-xs">{{prizeType[item.prizeType]}}</div>
                  </div>
                  <div class="prizeRight flex-1 ml-3 mr-1 flex justify-around">
                    <div class="nameTips">
                      <div class="prizeNameBox font-bold tracking-widest text-base" :style="furnishStyles.littleTextColor.value">{{item.ladderName}}</div>
                      <div class="customFont text-xs font-bold" :style="furnishStyles.littleTextColor.value">满{{item.moneyNum || 'x'}}元可领奖品</div>
                    </div>
                    <div class="flex flex-row text-xs justify-between items-center">
                      <div class="receive-btn" :style="furnishStyles.getPrizeBtn.value" @click="toast"/>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
      </div>
      <div class="skuListDiv p-3.5 relative" :style="furnishStyles.showGoodsBg.value" v-if="isExposure === 1">
        <div class="gridDiv grid grid-cols-2 gap-1.5 absolute pl-4 pr-7 overflow-auto">
          <div v-for="(item, index) in skuListPreview" class="gridItemDiv py-2 px-3.5" :key="index">
            <div class="skuImgDiv flex justify-center">
              <img class="skuImg" :src="item.skuMainPicture" alt="">
            </div>
            <div class="skuBottomDiv">
              <div class="text-xs lz-multi-ellipsis--l2">{{item.skuName}}</div>
            </div>
          </div>
          <div class="more-btn" v-if="skuListPreview.length > 10" @click="toast">点我加载更多</div>
        </div>
      </div>
    </div>
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showOrderRecord">
      <OrderRecordPopup @close="showOrderRecord = false"></OrderRecordPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showReceiveRecord">
      <ReceiveRecordPopup @close="showReceiveRecord = false"></ReceiveRecordPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <VanPopup teleport="body" v-model:show="addressPopup">
      <SaveAddress v-if="addressPopup" :isPreview="isPreview" :echoInfo="form" @close="closeAddress"></SaveAddress>
    </VanPopup>
    <!-- 单词领取提示 -->
    <VanPopup teleport="body" v-model:show="showTip" position="center">
      <div class="drawPromptPopDivALL w-[5rem] h-[0.8rem] relative flex justify-center items-center">
        <div class="absolute closePopDiv" @click="showTip = false"></div>
        <div class="rounded-b font-bold text-xs relative contentDiv">
          <div class="leading-5 promptDiv1">活动的奖品非多次领取，满足多个阶梯仅可领取其中的一个奖品</div>
          <div class="leading-5 promptDiv2">请确认是否要继续领奖，领取后不可更换</div>
          <div class="btnDivALL flex justify-between absolute text-xs font-light">
            <div class="reSelectDiv px-4 py-1.5" @click="showTip = false"></div>
            <div class="surePrizeDiv px-4 py-1.5" @click="toast"></div>
          </div>
        </div>
      </div>
    </VanPopup>
    <!-- 选择奖品 -->
    <VanPopup teleport="body" v-model:show="showSelectPrize">
      <SelectPrizePopup v-if="showSelectPrize"></SelectPrizePopup>
    </VanPopup>
<!--    领奖成功-->
    <VanPopup teleport="body" v-model:show="showGetSuccess">
      <GetSuccess v-if="showGetSuccess" @close="showGetSuccess = false"></GetSuccess>
    </VanPopup>
    <!--参与活动的订单商品-->
    <VanPopup teleport="body" v-model:show="showGoods" position="center">
      <GoodsPopup :data="orderSkuListPreview" :orderSkuList="orderSkuList" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import SaveAddress from '../components/SaveAddress.vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultStateList, IMAGE_MAP, prizeType } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import ReceiveRecordPopup from '../components/ReceiveRecordPopup.vue';
import AwardPopup from '../components/AwardPopup.vue';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useCountdown from '@/hooks/useCountdown';
import { showToast } from 'vant';
import SelectPrizePopup from '../components/SelectPrizePopup.vue';
import GetSuccess from '../components/GetSuccess.vue';
import GoodsPopup from '../components/GoodsPopup.vue';

const showGetSuccess = ref(false);
const showSelectPrize = ref(false);
// 单次领取弹窗
const showTip = ref(false);
const activityData = inject('activityData') as any;
const addressPopup = ref(false);
const form = reactive({
  address: '',
  city: '',
  county: '',
  hasAddress: true,
  mobile: '',
  postalCode: '',
  province: '',
  realName: '',
});

const closeAddress = (isSuccess: boolean) => {
  addressPopup.value = false;
};

const isPreview = ref(false);
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();
// 默认设置结束时间戳为1小时后
const endTime = ref(dayjs().add(30, 'day').valueOf());
const countdownTime = useCountdown(endTime);

const isLoadingFinish = ref(true);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  nextPotCount: number;
  remainCount: number;
  sendTotalCount: number;
}
const ladderInfoList = ref<Prize[]>(defaultStateList);
type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const isExposure = ref(0);
const skuListPreview = ref<Sku[]>([]);
const skuList = ref<Sku[]>([]); // 曝光商品
const orderSkuListPreview = ref<Sku[]>([]);
const orderSkuList = ref<Sku[]>([]);

const nextPotCount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const showOrderRecord = ref(false);
const showReceiveRecord = ref(false);
const showGoods = ref(false);
const showAward = ref(false);

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

const close = () => {
  showLimit.value = false;
};

const handleGetPrize = () => {
  showTip.value = true;
};

const createImg = async () => {
  showRule.value = false;
  showGoods.value = false;
  showOrderRecord.value = false;
  showReceiveRecord.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  console.log(data);
  // const efficientPrizeList = data.ladderList.filter((e: { prizeType: number }) => e.prizeType);
  // if (efficientPrizeList.length) {
  //   ladderInfoList.value = efficientPrizeList;
  //   nextPotCount.value = ladderInfoList.value[0].nextPotCount || 0;
  // } else {
  //   ladderInfoList.value = defaultStateList;
  // }
  ladderInfoList.value = data.ladderList;
  nextPotCount.value = ladderInfoList.value[0].moneyNum || 0;
  isExposure.value = data.isExposure;
  skuListPreview.value = data.skuListPreview ? data.skuListPreview : [];
  skuList.value = data.skuList ? data.skuList : [];
  orderSkuListPreview.value = data.orderSkuListPreview ? data.orderSkuListPreview : [];
  orderSkuList.value = data.potSkuList ? data.potSkuList : [];
  ruleTest.value = data.rules;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    ladderInfoList.value = activityData.ladderList;
    ruleTest.value = activityData.rules;
    endTime.value = dayjs(activityData.endTime).valueOf();
    shopName.value = activityData.shopName;
    isExposure.value = activityData.isExposure;
    skuListPreview.value = activityData.skuListPreview ? activityData.skuListPreview : [];
    skuList.value = activityData.skuList ? activityData.skuList : [];
    orderSkuListPreview.value = activityData.orderSkuListPreview ? activityData.orderSkuListPreview : [];
    orderSkuList.value = activityData.potSkuList ? activityData.potSkuList : [];
    nextPotCount.value = ladderInfoList.value[0].moneyNum || 0;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
@font-face {
  font-family: 'fzfwqytFont';
  src: url('../style/fzfwqyt-j-E.TTF') format('truetype');
}
</style>
<style scoped lang="scss">
.pageBg {
  background-size: 100%;
  min-height:100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
}
.header-kv{
  padding: 0 0 6rem 0;
  .btnAllClass{
    margin-top: 0.1rem;
    .btnClass{
      width: 1.61rem;
      height: 0.47rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.1rem 0.05rem 0 0;
    }
  }
}
.prizeBox{
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 7.3rem;
  height: 11.4rem;
  margin: 0 auto;
  padding: .35rem 0 0 0;
}
.countDownDivAll{
  background-size: 100%;
  background-repeat: no-repeat;
  width: 5.77rem;
  height: 0.85rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 0 0 0.3rem;
  .text-class{
    font-size: 0.28rem;
    font-weight: bolder;
    color:#f7dfc0;
    font-family: 'fzfwqytFont';
  }
  .count-down{
    position: absolute;
    top: 6.52rem;
    right: 1.1rem;
    font-size: 0.24rem;
    color: #f7dfc0;
    background: transparent;
    font-family: 'fzfwqytFont';
  }
}
.goBuyClass{
  background-size: 100%;
  background-repeat: no-repeat;
  width: 2.78rem;
  height: 0.76rem;
  margin: 0.5rem auto 0;
}
.prizeListAllClass{
  height: 7.8rem;
  margin: 0.5rem auto 0;
  padding-top: 0.6rem;
  .topTips{
    width: 6.4rem;
    margin: 0 auto;
    padding: 0.2rem 0;
    text-align: center;
    background-color: #fff;
    border-radius: 0.2rem;
    .customFont{
      font-family: 'fzfwqytFont';
    }
  }
  .prizeListClass{
    max-height: 6.2rem;
    overflow-y: scroll;
    margin: 0.2rem 0.16rem;
    .prizeItem{
      min-height: 1.4rem;
      align-items: center;
      display: flex;
    }
    .prizeImgDivAll{
      max-height: 6rem;
      overflow-y: scroll;
      margin: 0 0.16rem;
      display: flex;
      align-items: center;
      .imgBox{
        width: 1.2rem;
      }
      .prizeImgDiv{
        max-height: 1.2rem;
        width: auto;
        margin: 0 auto;
      }
    }
    .prizeRight{
      align-items: center;
    }
    .nameTips{
      width: 2.5rem;
      word-break: break-all;
    }
    .prizeNameBox{
      font-size: 0.24rem;
      font-family: 'fzfwqytFont';
    }
    .text-gray-800{
      font-size: 0.17rem;
    }
    .receive-btn{
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 1.42rem;
      height: 0.44rem;
      font-size:0.21rem;
    }
  }
}
.skuListDiv{
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 7.28rem;
  height: 10.59rem;
  margin: 0.5rem auto 0;
  .gridDiv{
    max-height: 8.4rem;
    margin: 1.2rem 0 0 0;
    .gridItemDiv{
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/253514/20/13434/11985/67887d8eF57003010/6b8785d3069ef3ed.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 3rem;
      height: 4.1rem;
      padding: 0.27rem 0.28rem;
      .skuImgDiv{
        //background: #fff;
        border-radius: 0.16rem 0.16rem 0 0;
        .skuImg{
          height: 2.1rem;
        }
      }
      .skuBottomDiv{
        padding: 0.2rem 0 0rem;
        border-radius: 0 0 0.16rem 0.16rem;
        line-height: 0.3rem;
        .skuPriceDiv{
          margin-top: 0.12rem;
        }
      }
    }
  }
}
.drawPromptPopDivALL {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/237520/28/10203/76617/6593bcc3F7e1601b9/359d2b437b1572a4.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.59rem;
  height: 6.26rem;
  .btnDivALL {
    bottom: 0.6rem;
  }
  .contentDiv {
    padding-top: 2.3rem;
    height: 5.9rem;
    margin: 0 0.4rem;
    .promptDiv1 {
      text-align: center;
    }
    .promptDiv2 {
      color: #ff3333;
      text-align: center;
    }
  }
  .closePopDiv {
    width: 0.6rem;
    height: 0.6rem;
    top: 0;
    right: 0.1rem;
    z-index: 10;
  }
  .reSelectDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/235699/8/10438/16946/6593bcc2F3ca98e38/8f74a52894b4f493.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 2.86rem;
    height: 0.7rem;
  }
  .surePrizeDiv {
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/235575/8/10932/17129/6593bcc2Fe34fa7fc/e4e1ce75d2128684.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 2.86rem;
    height: 0.7rem;
  }
}
.more-btn {
  width: 1.8rem;
  height: 0.5rem;
  font-size: 0.2rem;
  color: #fff;
  background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
  background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 0.3rem;
  margin-left: 70%;
}
.my-1 {
  width: 6.84rem;
}
</style>
