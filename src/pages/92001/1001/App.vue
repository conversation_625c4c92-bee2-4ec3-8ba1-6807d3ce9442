<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="kv">
      <img v-if="hasGet==2" :src="furnish.actBg" alt="" />
      <div v-else style="position: relative">
        <img  :src="furnish.actBgSuccess" alt="" />
        <div class="nickNameBox">
          <div class="nick">
             <div>恭喜:</div>
             <p>{{userInfo.nickname}}</p>
          </div>
          <p>您已成功申领</p>
        </div>
      </div>
      <div class="btn">
        <img style="width: 1.6rem" :src="furnish.prizeBtn" alt="" @click="myPrize = true" />
        <img style="width: 1.22rem" :src="furnish.ruleBtn" alt="" @click="showRulePopup" />
      </div>
    </div>
    <div class="prize-list">
      <div class="item" v-for="(item, index) in prizeList" :key="index" :style="{ backgroundImage: `url(${item.seriesImg})` }">
        <div class="num" :style="furnishStyles.prizeTextColor.value">剩余数量：{{ item.stock }}</div>
        <img class="get-btn" :src="furnish.getPrizeBtn" alt="" @click="getPrize(item)" />
      </div>
    </div>
    <div class="index-rule-bk" :style="furnishStyles.ruleBox.value">
      <div class="content">{{ ruleTest }}</div>
    </div>
    <img class="more"  :src="furnish.moreBg" alt="" @click="gotoShopPage(baseInfo.shopId)" />
  </div>
  <!--非会员弹窗-->
  <VanPopup teleport="body" v-model:show="showNotMember">
    <NotMember @close="showNotMember = false"></NotMember>
  </VanPopup>
  <!--非注册会员弹窗-->
  <VanPopup teleport="body" v-model:show="notMeisuMemberPopup">
    <NotMeisuMember @close="notMeisuMemberPopup = false"></NotMeisuMember>
  </VanPopup>
  <!--验证手机号弹窗-->
  <VanPopup teleport="body" v-model:show="editPhonePopup">
    <Phone @toCheck="toCheckPhone" @close="editPhonePopup = false"></Phone>
  </VanPopup>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 不可领取 -->
  <VanPopup teleport="body" v-model:show="notReceivePopup">
    <NotReceive :notReceiveCode="notReceiveCode" @close="notReceivePopup = false" @showRule="showRulePopup"></NotReceive>
  </VanPopup>
  <!-- 二次确认 -->
  <VanPopup teleport="body" v-model:show="confirmGetPopup">
    <ConfirmReceive :prizeInfo="prizeInfo" @close="confirmGetPopup = false" @showRule="showRulePopup" @confirm="confirmGetPrize"></ConfirmReceive>
  </VanPopup>
<!-- 我的权益 -->
  <VanPopup teleport="body" v-model:show="myPrize">
    <PrizePopup @close="myPrize = false"></PrizePopup>
  </VanPopup>
<!-- 已经领取过 -->
  <VanPopup teleport="body" v-model:show="hasJoined">
    <HasJoin @close="hasJoinClose"></HasJoin>
  </VanPopup>
<!-- 领取成功 -->
  <VanPopup teleport="body" v-model:show="prizeSuccess">
    <PrizeSuccessPopup :prizeInfo="award" @close="prizeSuccess = false"></PrizeSuccessPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, ref } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import RulePopup from './components/RulePopup.vue';
import PrizePopup from './components/MyPrizePopup.vue';
import PrizeSuccessPopup from './components/PrizeSuccessPopup.vue';
import NotMember from './components/NotMember.vue';
import Phone from './components/Phone.vue';
import NotMeisuMember from './components/NotMeisuMember.vue';
import NotReceive from './components/NotReceive.vue';
import HasJoin from './components/HasJoin.vue';
import ConfirmReceive from './components/ConfirmReceive.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { gotoShopPage } from '@/utils/platforms/jump';
import { UserInfo } from '@/utils/products/types/UserInfo';
import useThreshold from '@/hooks/useThreshold';

const decoData = inject('decoData') as DecoData;
const userInfo = inject('userInfo') as UserInfo;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

const isLoadingFinish = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品
const myPrize = ref(false);
// 已经参与过
const hasJoined = ref(false);
const prizeSuccess = ref(false);
// 展示门槛显示弹框
const showLimit = ref(false);
// 奖品信息
const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  prizeId: '',
  userReceiveRecordId: '',
});
const prizeList = ref<any>([]);
// 展示手机号弹框
const editPhonePopup = ref(false);
// 是否领取成功
const success = ref(false);
// 非会员弹窗
const showNotMember = ref(false);
// 非美素会员
const notMeisuMemberPopup = ref(false);
// 不能领取弹窗
const notReceivePopup = ref(false);
// 不能领取原因码 0：不是新客 F0：不是一段 F2-N：不是二段 F3：不是三段 99：没有可领取段位
const notReceiveCode = ref('0');
// 确认领取弹窗
const confirmGetPopup = ref(false);
// 验证手机号
const userMobile = ref('');
// 品牌会员
const isMeisuMember = ref(false);
// 判断新客  1 不是  2 是
const newCustomer = ref(false);
// 月龄有可领取的权益
const hasBabyMonth = ref(false);
const hasGet = ref(2);
const prizeInfo = ref<any>({});
const hasJoinClose = (type: number) => {
  hasJoined.value = false;
  if (type === 1) {
    myPrize.value = true;
  }
};
// 获取规则
const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};
// 活动规则相关
const showRulePopup = async () => {
  try {
    showRule.value = true;
    notReceivePopup.value = false;
  } catch (error: any) {
    console.error();
  }
};
// 活动详情
const getActivity = async () => {
  try {
    const { data } = await httpRequest.post('/92001/activity', {
      mobile: userMobile.value,
    });
    prizeList.value = data.prizeList;
    hasGet.value = data.receive;
    if (userMobile.value === '') return;
    isMeisuMember.value = data.mobile === 2;
    newCustomer.value = data.newCustomer === 2;
    hasBabyMonth.value = data.baby === 2;

    if (!isMeisuMember.value) {
      notMeisuMemberPopup.value = true;
    } else if (!hasBabyMonth.value) {
      notReceiveCode.value = '99';
      notReceivePopup.value = true;
    } else if (!newCustomer.value) {
      notReceiveCode.value = '0';
      notReceivePopup.value = true;
    }
  } catch (error: any) {
    console.error(error);
  }
};
// 领取奖品
const getPrize = async (item: any) => {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
  });
  if (baseInfo.status === 2) {
    if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
      showNotMember.value = true;
      return;
    }
    if (item.canGet === 4 || hasGet.value === 1) {
      hasJoined.value = true;
      return;
    }
    if (userMobile.value === '' && hasGet.value === 2) {
      //  && hasGet.value === 2
      editPhonePopup.value = true;
      return;
    }
    if (!isMeisuMember.value) {
      notMeisuMemberPopup.value = true;
      return;
    }
    if (!hasBabyMonth.value || [1, 5].includes(item.canGet)) {
      notReceiveCode.value = item.stageId;
      notReceivePopup.value = true;
      return;
    }
    if (!newCustomer.value) {
      notReceiveCode.value = '0';
      notReceivePopup.value = true;
      return;
    }
    if (item.canGet === 3) {
      showToast('奖品已发完');
      return;
    }

    prizeInfo.value = item;
    confirmGetPopup.value = true;
  }
};
// 领取奖品
const confirmGetPrize = async () => {
  try {
    const { data } = await httpRequest.post('/92001/receive', {
      prizeId: prizeInfo.value.prizeId,
    });
    award.value = data;
    prizeSuccess.value = true;
    confirmGetPopup.value = false;
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};
// 检查电话号码
const toCheckPhone = async (phone: string) => {
  userMobile.value = phone;
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  await getActivity();
  closeToast();
  editPhonePopup.value = false;
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActivity(), getRule()]);
    isLoadingFinish.value = true;
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
    });
    if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
      showNotMember.value = true;
    } else if (baseInfo.status === 2 && hasGet.value === 2) {
      // && hasGet.value === 2
      editPhonePopup.value = true;
    }
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

init();
</script>

<style>
@font-face {
  font-family: 'FZRuiZHJW_Zhun';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZRUIZHJW/FZRuiZHJW_Zhun.TTF');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'FZRuiZHJW_Zhun';
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.2rem;
  position: relative;
}
.kv {
  margin-bottom: 0.35rem;
  position: relative;
  img {
    width: 100%;
  }
  .btn {
    position: absolute;
    right: 0;
    top: 0.23rem;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    img {
      width: fit-content;
      height: 0.44rem;
      margin-bottom: 0.1rem;
    }
  }
  .nickNameBox {
    position: absolute;
    width: 100%;
    top: 1.2rem;
    font-size: 0.62rem;
    color: #b3a053;
    font-weight: bold;
    text-align: center;
    text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
    .nick {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 0.1rem;
        div {
          margin-right: 0.1rem;
        }
        p {
          text-align: left;
          max-width: 4rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
    }
  }
}
.prize-list {
  .item {
    width: 7.1rem;
    height: 7.9rem;
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0 auto 0.4rem;
    padding-top: 5.3rem;
    .num {
      font-size: 0.25rem;
      line-height: 0.25rem;
      height: 0.25rem;
      text-align: center;
      font-weight: bold;
      margin-bottom: 0.65rem;
    }
    .get-btn {
      width: 2.56rem;
      margin: 0 auto;
    }
  }
}
.index-rule-bk {
  width: 7.11rem;
  height: 9.5rem;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: 100%;
  padding: 1.1rem 0.23rem 0.25rem;
  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    font-size: 0.2rem;
  }
}
.more {
  margin: 0.3rem auto;
  width: 3.96rem;
  height: 0.58rem;
  cursor: pointer;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
