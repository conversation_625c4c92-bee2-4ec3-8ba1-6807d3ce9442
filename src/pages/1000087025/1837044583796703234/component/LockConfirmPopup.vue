<!--
 * 锁权确认弹窗
 * @author: wuhao
 * @since: 2024-09-24
 * LockConfirmPopup.vue
-->
<template>
  <Popup teleport="body" v-model:show="isShowPopup">
    <div class="container">
      <div class="bg">
        <div class="confirm-btn" @click="lock"></div>
      </div>
      <img class="close-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/228668/36/25189/1605/66f2730dF1d833fda/36b51fc2b705daeb.png" alt="" @click="closePopup" />
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed } from 'vue';
import { Popup, showToast } from 'vant';

import { lockRights } from '../ajax/ajax';

const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);
const emit = defineEmits(['closePopup', 'refresh', 'showPopup']);
const closePopup = () => {
  emit('closePopup');
};
const lock = async () => {
  const result = await lockRights();
  if (result.code === 200) {
    closePopup();
    emit('showPopup');
    setTimeout(() => {
      emit('refresh');
    }, 1000);
  } else {
    closePopup();
  }
};
</script>

<style scoped lang="scss">
.container {
  height: 8rem;
  .bg {
    width: 5.7rem;
    height: 6.85rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/296440/17/1565/40612/681193ebF2f835ed0/fdf17f5b3d4dd006.png);
    background-repeat: no-repeat;
    background-size: 100%;
    padding-top: 5.45rem;
    .confirm-btn {
      width: 2.2rem;
      height: 0.5rem;
      margin: 0 auto;
    }
  }
  .close-btn {
    width: 0.7rem;
    height: 0.7rem;
    position: absolute;
    top: 7rem;
    left: 2.5rem;
  }
}
</style>
