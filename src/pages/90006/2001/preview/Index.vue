<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }" @click="onSelected(1)">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="rulePopup = true">活动规则</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="strategyPopup = true">活动攻略</div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="recordPopup = true">锁权记录</div>
        </div>
      </div>
    </div>
    <div class="blank">
      <div class="exchange-area select-hover" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)">
        <img :src="furnish.giftImg" alt="" class="gift-img" />
        <div class="btn" @click="toast"></div>
      </div>
<!--      <div class="btn" @click="toast"></div>-->
      <div class="select-hover" :class="{ 'on-select': selectedId === 3 }" @click="onSelected(3)">
        <img :src="furnish.step" alt="" class="step-img" />
      </div>
      <div class="sku-title" v-if="isExposure === 1"/>
      <div class="sku-box">
        <div class="sku-item" v-for="(item,index) in skuListPreview" :key="index">
          <img class="sku-img" :src=item.skuMainPicture alt="">
        </div>
        <div class="more-btn" v-if="skuList.length > 18" @click="toast">点我加载更多</div>
      </div>
    </div>
  </div>
  <van-popup v-model:show="rulePopup" :closeOnClickOverlay="false">
    <Rule :ruleText="ruleText" @close="rulePopup = false"></Rule>
  </van-popup>
  <van-popup v-model:show="recordPopup" :closeOnClickOverlay="false">
    <Record v-if="recordPopup" @close="recordPopup = false"></Record>
  </van-popup>
  <van-popup v-model:show="strategyPopup" :closeOnClickOverlay="false">
    <Strategy @close="strategyPopup = false"></Strategy>
  </van-popup>
</template>

<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useSendMessage from '@/hooks/useSendMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import Rule from '../components/Rule.vue';
import Record from '../components/Record.vue';
import Strategy from '../components/Strategy.vue';
import { Sku } from '../ts/logic';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);
// 规则弹窗
const rulePopup = ref(false);
const ruleText = ref('');
// 记录弹窗
const recordPopup = ref(false);
// 领取攻略弹窗
const strategyPopup = ref(false);

const skuList = ref<Sku[]>([]);
const skuListPreview = ref<Sku[]>([]); // 曝光商品
const isExposure = ref(1);

const powerRangeDate = ref([]);
const orderDataList = ref<any[]>([]);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  recordPopup.value = false;
  strategyPopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  powerRangeDate.value = data.powerRangeDate;
  orderDataList.value = data.orderDataList;
  isExposure.value = data.isExposure;
  if (data.skuList) {
    skuList.value = data.skuList;
    skuListPreview.value = data.skuListPreview;
  }
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});
// // 弹窗监听
// registerHandler('popup', (data: any) => {
//   failPopup.value = data;
// });

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    powerRangeDate.value = activityData.powerRangeDate;
    orderDataList.value = activityData.orderDataList;
    skuList.value = activityData.skuList;
    skuListPreview.value = activityData.skuListPreview;
    isExposure.value = activityData.isExposure;

    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>
<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.54rem 0rem 0.3rem 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0;
  }

  .header-btn {
    width: 1.1rem;
    height: 0.36rem;
    line-height: 0.38rem;
    margin-bottom: 0.1rem;
    font-size: 0.19rem;
    text-align: center;
    border-radius: 0.23rem 0 0 0.23rem;
    background-size: 100%;
    background-repeat: no-repeat;
    cursor: pointer;
  }
}
.blank {
  padding: 0.3rem 0 0 0;
}
.exchange-area {
  border-radius: 0.1rem;
  .gift-img {
    width: 100%;
    margin-bottom: 0.2rem;
  }
  .btn {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/311254/21/2217/9418/682af5e0F255fb919/772d5355b7f4df16.png) no-repeat;
    background-size: 100%;
    width: 2.15rem;
    height: 0.5rem;
    margin: 0 auto;
    font-size: 0.28rem;
    text-align: center;
    line-height: 0.65rem;
    color: #fff;
    position: absolute;
    top: 11.8rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .no-point {
    text-align: center;
    font-size: 0.18rem;
    color: #e3101c;
    margin-bottom: 0.15rem;
  }
  .tip {
    text-align: center;
    font-size: 0.14rem;
    color: #000;
  }
}
.btn {
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/131857/18/44601/22380/6612017aF2065a13b/4a0f62daeecc2166.png') no-repeat;
  background-size: 100%;
  width: 5.19rem;
  height: 0.63rem;
  margin: 0.1rem auto 0.6rem;
  font-size: 0.28rem;
  text-align: center;
  line-height: 0.65rem;
  color: #fff;
}
.segmentation {
  width: 100%;
  margin-bottom: 0.3rem;
}

.step-img {
  width: 100%;
}
.sku-title{
  width: 3.37rem;
  height: 0.56rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/321234/12/2002/4144/682af36dFfe79b9c7/d570c7124dc5a03e.png") no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
.sku-box{
  width: 6.9rem;
  height: 6.4rem;
  overflow: hidden;
  overflow-y: scroll;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0.2rem;
  margin: 0 auto;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #000;
    background: linear-gradient(to right, #fdf6e9, #d5b687, #fdd389);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
  .sku-item{
    display: flex;
    flex-direction: column;
    width: 3rem;
    height: 3rem;
    margin: 0 0.1rem 0.2rem 0.1rem;
    .sku-img{
      background: url("//img10.360buyimg.com/imgzone/jfs/t1/233292/2/15604/5617/6612017dF31ddb359/2754908b4010e402.png") no-repeat;
      background-size: 100%;
      width: 3rem;
      height: 3rem;
      border-radius: 0.3rem;
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
