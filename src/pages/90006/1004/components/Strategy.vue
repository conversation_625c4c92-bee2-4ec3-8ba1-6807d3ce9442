<template>
  <div class="popup-bk" :style="furnishStyles.popupImg.value">
    <div class="title">
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>

import furnishStyles from '../ts/furnishStyles';

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.popup-bk {
  width: 5.16rem;
  height: 3.03rem;
  //background: url(//img10.360buyimg.com/imgzone/jfs/t1/218494/10/39843/19383/6612017fF765ccf58/e070e583f3e9e969.png) no-repeat;
  background-size: 100%;
  background-repeat: no-repeat;
  .title {
    width: 100%;
    height: 0.74rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 0.2rem 0 0.8rem;
    img {
      height: 0.34rem;
    }
  }
  .content {
    width: 5.6rem;
    height: 6.45rem;
    margin: 0 auto;
    padding: 0.2rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
.close {
  width:0.49rem;
  height: 0.49rem;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/174703/25/39883/1360/66120180Ffcba11af/1727b01325d0590a.png") no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
