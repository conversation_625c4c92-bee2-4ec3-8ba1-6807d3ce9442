<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <img :src="furnish.ruleBtn" alt="" class="rule-btn" @click="showRule = true" />
      <img :src="furnish.myPrizeBtn" alt="" class="my-prize-btn" @click="showMyPrize = true" />
    </div>
    <div>
      <div class="friend-avatar-list" v-if="isInvite === 2">
        <div v-for="item in peopleNum" :key="item">
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/274382/12/22850/1325/68009ca3F46560841/5d27899de0f56ab8.png" alt="" class="avatar" />
        </div>
      </div>
      <div class="has-invite-num">已邀请0人</div>
      <div>
        <div class="get-prize-btn">立即领取</div>
        <!-- <div class="get-prize-btn has-get-btn">已领取</div> -->
      </div>
    </div>
    <img :src="furnish.rankPrize" alt="" class="rank-bg" />
    <div class="get-prize-btn invite-btn">邀请更多好友</div>
    <div class="list-bk" :style="furnishStyles.rankBg.value">
      <div class="tabs">
        <div class="tab" v-for="(item, index) in ['排行榜', '我邀请的好友']" :key="item" :class="{ 'tab-act': tabActive === index }" @click="tabActive = index">{{ item }}</div>
      </div>
      <div class="content">
        <div class="rank" v-if="tabActive === 0">
          <div class="title row">
            <div class="index">排名</div>
            <div class="nickname">昵称</div>
            <div class="num">邀请好友入会数量</div>
          </div>
          <div class="scroll">
            <div class="no-data">暂无数据~</div>
          </div>
        </div>
        <div class="friend-list" v-else-if="tabActive === 1">
          <div class="title">已成功邀请0位好友</div>
          <div class="scroll">
            <div class="no-data">
              <img src="//img10.360buyimg.com/imgzone/jfs/t1/273492/30/21138/2896/68009ca2Fe7998272/3c0dad72b6f000bd.png" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!-- 邀请好友弹窗 -->
    <VanPopup teleport="body" v-model:show="showShareFriend" position="bottom">
      <ShareFriends v-if="showShareFriend" @close="showShareFriend = false"></ShareFriends>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" :userPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, inject } from 'vue';
import furnishStyles, { furnish, prizeInfo, prizeType } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import { PrizeInfo, Rank } from '../ts/type';
import dayjs from 'dayjs';
import { showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import ShareFriends from '../components/ShareFriends.vue';

Swiper.use([Autoplay]);
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);
const shopName = ref('xxx旗舰店');
const isLoadingFinish = ref(false);
const showRule = ref(false);
const ruleTest = ref('');
const showMyPrize = ref(false);
const showShareFriend = ref(false);
const peopleNum = ref(3);
const tabActive = ref(0);
const times = ref(0);
// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
  prizeImg: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};
const sharePrizeList = ref<PrizeInfo[]>(prizeInfo);
const rankPrizeList = ref<PrizeInfo[]>(prizeInfo);
const rank = ref(1); // 0 未开启 1开启
const isInvite = ref(1);
// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage(
    {
      from: 'C',
      type: 'deco',
      event: 'changeSelect',
      data: id,
    },
    '*',
  );
  selectedId.value = id;
};

const rankInfo = ref<Rank[]>([]);

const numImg = ['//img10.360buyimg.com/imgzone/jfs/t1/214317/31/16906/3536/624f9d31E2e45d8cf/8363020f60f115ff.png', 'https://img10.360buyimg.com/imgzone/jfs/t1/219322/7/17032/3636/624f9d31E79f6ea41/442e2f2451d5e1f3.png', 'https://img10.360buyimg.com/imgzone/jfs/t1/145169/18/26369/3813/624f9d31E2fd9a567/021e24c4f043c941.png'];
const prizeSendNum = ref(0); // 邀请领奖机会
const prizeSendType = ref(1); // 奖品类型 1 单一型 2阶梯型
const btnClick = (type: string) => {
  if (type === 'rule') {
    showRule.value = true;
  } else if (type === 'prize') {
    showMyPrize.value = true;
  } else if (type === 'shareFriend') {
    showShareFriend.value = true;
  }
};

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showAward.value = false;
  showSaveAddress.value = false;

  showSelect.value = false;
  isCreateImg.value = true;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    prizeSendType.value = data.prizeSendType;
    if (data.prizeList.length) {
      sharePrizeList.value = data.prizeList.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 0);
      rankPrizeList.value = data.prizeRankList.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 1);
    }
    if (sharePrizeList.value && sharePrizeList.value.length > 1) {
      nextTick(() => {
        const mySwiper2 = new Swiper('.swiper-share-prize', {
          autoplay: {
            delay: 2000,
            disableOnInteraction: false,
          },
          loop: true,
          slidesPerView: 1,
          loopedSlides: 10,
          allowTouchMove: true,
        });
      });
    }
    if (rankPrizeList.value && rankPrizeList.value.length > 1) {
      nextTick(() => {
        const mySwiper2 = new Swiper('.swiper-rank-prize', {
          autoplay: {
            delay: 2000,
            disableOnInteraction: false,
          },
          loop: true,
          slidesPerView: 1,
          loopedSlides: 10,
          allowTouchMove: true,
        });
      });
    }
    ruleTest.value = data.rules;
    rank.value = data.isRank;
    shopName.value = data.shopName;
    isInvite.value = data.isInvite;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showMyPrize.value = false;
    showAward.value = false;
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeSendType.value = activityData.prizeSendType;
    if (activityData.prizeList.length) {
      sharePrizeList.value = activityData.prizeList.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 0);
      rankPrizeList.value = activityData.prizeRankList.filter((e: Pick<PrizeInfo, 'type'>): boolean => e.type === 1);
    }
    if (sharePrizeList.value && sharePrizeList.value.length > 1) {
      nextTick(() => {
        const mySwiper2 = new Swiper('.swiper-share-prize', {
          autoplay: {
            delay: 2000,
            disableOnInteraction: false,
          },
          loop: true,
          slidesPerView: 1,
          loopedSlides: 10,
          allowTouchMove: true,
        });
      });
    }
    if (rankPrizeList.value && rankPrizeList.value.length > 1) {
      nextTick(() => {
        const mySwiper2 = new Swiper('.swiper-rank-prize', {
          autoplay: {
            delay: 2000,
            disableOnInteraction: false,
          },
          loop: true,
          slidesPerView: 1,
          loopedSlides: 10,
          allowTouchMove: true,
        });
      });
    }
    ruleTest.value = activityData.rules;
    rank.value = activityData.isRank;
    shopName.value = activityData.shopName;
    isInvite.value = activityData.isInvite;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
const toast = () => {
  showToast('活动预览，仅供参考');
};
</script>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
&::-webkit-scrollbar {
  display: none;
}
</style>
<style lang="scss">
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Regular.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'GucciSans';
  src: url('https://lzcdn.dianpusoft.cn/fonts/GucciSans/GucciSans-Bold.otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'GucciSans', 'SourceHanSansCN';
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-color: #f2f2f2;
  padding-bottom: 0.5rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }
  .rule-btn {
    position: absolute;
    top: 1.04rem;
    right: 0;
    width: 1.05rem;
  }
  .my-prize-btn {
    position: absolute;
    top: 1.84rem;
    right: 0;
    width: 1.05rem;
  }
}
.friend-avatar-list {
  padding: 0 1.6rem;
  margin-bottom: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .avatar {
    width: 0.8rem;
    height: 0.8rem;
    border-radius: 50%;
  }
}
.has-invite-num {
  font-size: 0.2rem;
  text-align: center;
  margin-bottom: 0.2rem;
}
.get-prize-btn {
  width: 1.83rem;
  height: 0.45rem;
  background-color: #000;
  color: #fff;
  font-size: 0.21rem;
  letter-spacing: 0.08rem;
  text-align: center;
  line-height: 0.45rem;
  margin: 0 auto 0.85rem;
}
.invite-btn {
  width: 2.4rem;
  margin-bottom: 0.5rem;
}
.has-get-btn {
  letter-spacing: 0.2rem;
}
.rank-bg {
  width: 100%;
}
.list-bk {
  width: 6.86rem;
  height: 5.77rem;
  margin: 0 auto;
  background-size: 100%;
  background-repeat: no-repeat;
  .tabs {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 0.9rem;
    font-size: 0.3rem;
    .tab-act {
      font-size: 0.33rem;
      font-weight: bold;
    }
  }
  .content {
    width: 6.63rem;
    height: 4rem;
    margin: 0 auto;
  }
  .rank {
    .row {
      height: 0.6rem;
      display: flex;
      align-items: center;
      font-size: 0.29rem;
      .index {
        width: 20%;
        text-align: center;
      }
      .nickname {
        width: 25%;
        text-align: center;
      }
      .num {
        width: 55%;
        text-align: center;
      }
    }
    .scroll {
      height: 3.4rem;
      overflow: hidden;
      overflow-y: auto;
    }
    .no-data {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.28rem;
      color: #999;
    }
  }
  .friend-list {
    .title {
      height: 0.4rem;
      font-size: 0.21rem;
      background-color: #f7f7f7;
      text-align: center;
      line-height: 0.4rem;
    }
    .scroll {
      height: 3.4rem;
      overflow: hidden;
      overflow-y: auto;
      .no-data {
        img {
          width: 1.28rem;
          margin: 1.2rem auto 0;
        }
      }
    }
  }
}
</style>
