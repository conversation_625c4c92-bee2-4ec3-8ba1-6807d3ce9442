<template>
  <div class="main">
    <img v-if="preview" :src="decorationInfo.pageType == '1' ? decorationInfo.answerPageBg : decorationInfo.answeringPageBg" alt="" class="bg" />
    <img v-else :src="pageType == '1' ? decorationInfo.answerPageBg : decorationInfo.answeringPageBg" alt="" class="bg" />
    <div class="content">
      <!-- 预览页面 -->
      <div v-if="preview" class="info" :style="{ color: decorationInfo?.otherTextColor }">
        <div
          class="member_btn"
          v-if="decorationInfo.pageType == '1'"
          :style="{
            color: decorationInfo?.challengeTextColor,
            backgroundColor: decorationInfo?.challengeBtnColor,
          }">
          开始答题
        </div>
        <div class="question" v-if="decorationInfo.pageType == '2'">
          <div class="questionTitle">{{ questionsList[currentQuestion - 1].questionName }}</div>
          <div class="answerBox">
            <div class="questionItem" v-for="(it, index) in questionsList[currentQuestion - 1].answerResponses" :key="index">
              <div
                class="radio"
                @click="chooseAnswer(index)"
                :style="{
                  backgroundColor: chooseIndex === index ? decorationInfo?.optionBtnSelectedColor : decorationInfo?.optionBtnColor,
                  color: chooseIndex === index ? decorationInfo?.optionTextSelectedColor : decorationInfo?.optionTextColor,
                }">
                {{ ['A', 'B', 'C', 'D'][index] }}
              </div>
              <div class="questionItemContent">
                {{ it.answerName }}
              </div>
            </div>
          </div>
          <div
            class="submit_btn"
            :style="{
              color: decorationInfo?.challengeTextColor,
              backgroundColor: decorationInfo?.challengeBtnColor,
            }">
            提交答案
          </div>
        </div>
        <div class="answerRight" v-if="decorationInfo.pageType == '3'">
          <div class="answerBox">
            <div>回答正确</div>
            <div>
              当前能量奖池已累计至
              <span
                :style="{
                  color: decorationInfo?.highlightTextColor,
                }"
              >XX能量</span
              >
            </div>

            <div class="btnList">
              <div class="btnBox">
                <div
                  class="submit_btn"
                  :style="{
                    color: decorationInfo?.continueTextColor,
                    backgroundColor: decorationInfo?.continueBtnColor,
                  }">
                  继续答题
                </div>
                <div>
                  继续答题，回答正确能量奖池将累计至
                  <span
                    :style="{
                      color: decorationInfo?.highlightTextColor,
                    }"
                  >xx</span
                  >。
                </div>
                <div>回答错误，当前奖池将清零。</div>
              </div>

              <div class="btnBox">
                <div
                  class="submit_btn"
                  :style="{
                    color: decorationInfo?.challengeTextColor,
                    backgroundColor: decorationInfo?.challengeBtnColor,
                  }">
                  领取奖励
                </div>
                <div>领走当前奖池全部奖励</div>
              </div>
            </div>
          </div>
        </div>
        <div class="answerErrors" v-if="decorationInfo.pageType == '4'">
          <div class="answerBox">
            <div>回答错误</div>
            <div>
              当前奖池能量
              <span
                :style="{
                  color: decorationInfo?.highlightTextColor,
                }"
              >已清零</span
              >
            </div>
            <div
              class="submit_btn"
              :style="{
                color: decorationInfo?.challengeTextColor,
                backgroundColor: decorationInfo?.challengeBtnColor,
              }">
              重新答题
            </div>
          </div>
        </div>
      </div>
      <!-- 答题页面 -->
      <div v-else class="info" :style="{ color: decorationInfo?.otherTextColor }">
        <div
          class="member_btn"
          v-if="pageType == '1'"
          :style="{
            color: decorationInfo?.challengeTextColor,
            backgroundColor: decorationInfo?.challengeBtnColor,
          }"
          @click="getQuestion(currentQuestion)">
          开始答题
        </div>
        <div class="question" v-if="pageType == '2'">
          <div class="questionTitle">{{ questionItem?.questionName }}</div>
          <div class="answerBox">
            <div class="questionItem" v-for="(it, index) in questionItem?.options" :key="index">
              <div
                class="radio"
                @click="chooseAnswer(index)"
                :style="{
                  backgroundColor: chooseIndex === index ? decorationInfo?.optionBtnSelectedColor : decorationInfo?.optionBtnColor,
                  color: chooseIndex === index ? decorationInfo?.optionTextSelectedColor : decorationInfo?.optionTextColor,
                }">
                {{ ['A', 'B', 'C', 'D'][index] }}
              </div>
              <div class="questionItemContent">
                {{ it.answerName }}
              </div>
            </div>
          </div>
          <div
            class="submit_btn"
            :style="{
              color: decorationInfo?.challengeTextColor,
              backgroundColor: decorationInfo?.challengeBtnColor,
            }"
            @click="submitAnswer">
            提交答案
          </div>
        </div>
        <div class="answerRight" v-if="pageType == '3'">
          <div class="answerBox">
            <div>回答正确</div>
            <div>
              当前能量奖池已累计至
              <span
                :style="{
                  color: decorationInfo?.highlightTextColor,
                }"
              >{{ energyInfo.thisSendEnergy }}能量</span
              >
            </div>

            <div
              class="btnList"
              :style="{
                justifyContent: energyInfo.isFinish ? 'center' : 'space-around',
              }">
              <div v-if="!energyInfo.isFinish" class="btnBox">
                <div
                  class="submit_btn"
                  :style="{
                    color: decorationInfo?.continueTextColor,
                    backgroundColor: decorationInfo?.continueBtnColor,
                  }"
                  @click="nextAnswer">
                  继续答题
                </div>
                <div>
                  继续答题，回答正确能量奖池将累计至
                  <span
                    :style="{
                      color: decorationInfo?.highlightTextColor,
                    }"
                  >{{ energyInfo.nextSendEnergy }}</span
                  >。
                </div>
                <div>回答错误，当前奖池将清零。</div>
              </div>

              <div class="btnBox">
                <div
                  class="submit_btn"
                  :style="{
                    color: decorationInfo?.challengeTextColor,
                    backgroundColor: decorationInfo?.challengeBtnColor,
                  }"
                  @click="getQuestionEnergy">
                  领取奖励
                </div>
                <div>领走当前奖池全部奖励</div>
              </div>
            </div>
          </div>
        </div>
        <div class="answerErrors" v-if="pageType == '4'">
          <div class="answerBox">
            <div>回答错误</div>
            <div>
              当前奖池能量
              <span
                :style="{
                  color: decorationInfo?.highlightTextColor,
                }"
              >已清零</span
              >
            </div>
            <div
              class="submit_btn"
              @click="getQuestion(currentQuestion)"
              :style="{
                color: decorationInfo?.challengeTextColor,
                backgroundColor: decorationInfo?.challengeBtnColor,
              }">
              重新答题
            </div>
          </div>
        </div>
      </div>
      <div class="btn_group">
        <div
          :style="{
            color: decorationInfo?.dialogTextColor,
            backgroundColor: decorationInfo?.dialogBtnColor,
          }"
          class="btn"
          @click="showPopup('questionRecord', { ...decorationInfo })"
          v-click-track="'dtjl'">
          答题记录
        </div>
        <div
          :style="{
            color: decorationInfo?.dialogTextColor,
            backgroundColor: decorationInfo?.dialogBtnColor,
          }"
          class="btn"
          @click="showPopup('answerRule', { ...decorationInfo })"
          v-click-track="'wfgz'">
          玩法规则
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { preview } from '../../Utils';
import { getEnergy, showPopup, upDataDecoration, checkThreshold } from '../DataHooks';
import { httpRequest } from '@/utils/service';
import { showToast } from 'vant';

const props = defineProps(['decorationInfo', 'moduleName']);
const decorationInfo = computed(() => props.decorationInfo);
const pageType = ref('1');
const currentQuestion = ref(1);
const chooseIndex = ref(99);
const questionsList = [
  {
    questionName: '1212',
    questionType: 1,
    answerResponses: [
      {
        id: 1747749725725,
        answerName: '1',
        rightFlag: 1,
      },
      {
        answerName: '2',
        rightFlag: 0,
        id: 1747749729669,
      },
      {
        answerName: '3',
        rightFlag: 0,
        id: 1747749730614,
      },
      {
        answerName: '4',
        rightFlag: 0,
        id: 1747749731453,
      },
    ],
  },
  {
    questionName: '4222',
    questionType: 1,
    answerResponses: [
      {
        id: 1747749739368,
        answerName: '21',
        rightFlag: 0,
      },
      {
        answerName: '22',
        rightFlag: 1,
        id: 1747749742643,
      },
      {
        answerName: '23',
        rightFlag: 0,
        id: 1747749743888,
      },
      {
        answerName: '24',
        rightFlag: 0,
        id: 1747749744084,
      },
    ],
  },
];

const questionItem = ref({}) as any;
const energyInfo = ref({}) as any;

// 获取答题题目
const getQuestion = async (value: number) => {
  if (!checkThreshold()) return;
  if (decorationInfo.value.hasAnswer) {
    showToast('您已领取过本期奖励，不可重复参与');
    return;
  }
  try {
    const { data } = await httpRequest.post('/99211/getQuestion', {
      num: value,
    });
    questionItem.value = data.nextQuestion;
    pageType.value = '2';
  } catch (err: any) {
    showToast(err.message);
  }
};
// 领取奖励
const getQuestionEnergy = async () => {
  try {
    const { data } = await httpRequest.post('/99211/getQuestionEnergy', {
      num: currentQuestion.value,
    });
    showPopup('answeringSuccessReceiveEnergy', data.sendEnergy);
    await upDataDecoration(props.moduleName);
    await getEnergy();
    pageType.value = '1';
  } catch (err: any) {
    showToast(err.message);
  }
};
// 回答问题
const submitAnswer = async () => {
  if (chooseIndex.value === 99) {
    showToast('请选择答案');
    return;
  }
  try {
    const { data } = await httpRequest.post('/99211/answer', {
      quesNum: currentQuestion.value,
      optionNum: chooseIndex.value + 1,
      id: questionItem.value.id,
    });
    chooseIndex.value = 99;
    energyInfo.value = data;
    pageType.value = data.isRight ? '3' : '4';
    if (!data.isRight) {
      currentQuestion.value = 1;
    }
  } catch (err: any) {
    showToast(err.message);
  }
};

const nextAnswer = () => {
  currentQuestion.value++;
  getQuestion(currentQuestion.value);
};

const chooseAnswer = (index: number) => {
  chooseIndex.value = index;
};
</script>

<style scoped lang="scss">
.main {
  position: relative;
  height: 100%;
  overflow: hidden;
  min-height: 5.2rem;

  .bg {
    width: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }

  .content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .member_btn {
        font-size: 0.3rem;
        margin: 4rem auto 0.4rem;
        width: 2.62rem;
        height: 0.73rem;
        border-radius: 0.37rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .question {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        margin-top: 1.5rem;

        .questionTitle {
          font-size: 0.3rem;
          margin-bottom: 0.2rem;
        }

        .answerBox {
          display: flex;
          flex-direction: column;
          justify-content: space-around;

          .questionItem {
            margin: 0 auto 0.1rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 5rem;
            font-size: 0.2rem;

            .radio {
              width: 0.4rem;
              height: 0.4rem;
              display: flex;
              justify-content: center;
              align-items: center;
              border-radius: 50%;
              margin-right: 0.2rem;
            }

            .questionItemContent {
              display: inline-block;
            }
          }
        }
      }

      .answerRight {
        margin-top: 2rem;

        .answerBox {
          .btnList {
            display: flex;
            justify-content: space-around;
            font-size: 0.18rem;
            width: 7rem;
            margin: 0 auto;

            .btnBox {
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              width: 3.62rem;

              .submit_btn {
                font-size: 0.3rem;
                margin: 0.2rem auto;
                width: 2.62rem;
                height: 0.73rem;
                border-radius: 0.37rem;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }
      }

      .answerErrors {
        margin-top: 2rem;

        .answerBox {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          height: 3rem;

          .submit_btn {
            font-size: 0.3rem;
            margin: 0.2rem auto;
            width: 2.62rem;
            height: 0.73rem;
            border-radius: 0.37rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .submit_btn {
        font-size: 0.3rem;
        margin: 0.2rem auto;
        width: 2.62rem;
        height: 0.73rem;
        border-radius: 0.37rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .btn_group {
      position: absolute;
      top: 0.3rem;
      right: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      border-radius: 10px;
      text-align: center;

      .btn {
        width: 1.4rem;
        height: 0.44rem;
        background-color: #ffffff;
        border-radius: 0.23rem 0 0 0.23rem;
        font-size: 0.24rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.1rem;
      }
    }
  }
}
</style>
