<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.dialogBg})` }">
    <div
      class="title"
      :style="{
        color: decoData?.titleTextColor,
      }">
      我的中奖记录
    </div>
    <div
      class="content"
      :style="{
        color: decoData?.otherTextColor,
      }">
      <div class="des">
        <div>时间</div>
        <div>奖品名称</div>
        <div>状态</div>
      </div>
      <div class="signRecordsBox" v-if="popupData.winningRecord.drawRecords.length > 0">
        <div class="item" v-for="(item, index) in popupData.winningRecord.drawRecords" :key="index">
          <div class="signTime">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
          <div class="energyValue">{{ item?.prizeName }}</div>
          <div>
            <div v-if="item?.prizeType == 3" class="btn_address" @click="fillInAddress(item)">{{ item?.realName ? '已填写' : '填写地址' }}</div>
            <div v-else class="btn_address">已发放</div>
          </div>
        </div>
      </div>
      <div v-else class="noData">暂无记录</div>
    </div>
  </div>
  <div class="close" @click="hidePopup('winningRecord')"></div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { hidePopup, popupData, showPopup } from '../DataHooks';
const props = defineProps(['decoData']);
console.log(popupData, '00');

const fillInAddress = (item: any) => {
  console.log(item, 'item');
  if (item.realName) return;

  hidePopup('winningRecord');
  showPopup('saveAddress', { ...item, moduleName: 'prizeDrawModule',pageType: '1' });
};
</script>

<style scoped lang="scss">
.main {
  width: 6rem;
  height: 6.78rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 0.2rem 0.4rem;
  .title {
    font-size: 0.4rem;
    margin-bottom: 0.2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    &:after,
    &:before {
      content: '';
      width: 0.04rem;
      height: 0.3rem;
      background-color: #f9c104;
      margin: 0 0.1rem;
    }
  }

  .content {
    width: 100%;
    height: 5.5rem;
    background: #7856c5;
    border-radius: 0.1rem;
    padding: 0.2rem;
    .des {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-bottom: 0.2rem;
      div {
        flex: 1 1 0;
        text-align: center;
      }
    }
    .signRecordsBox {
      height: 4rem;
      overflow-y: auto;
      scrollbar-width: none;
      .item {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-bottom: 0.1rem;
        font-size: 0.24rem;
        div {
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          .btn_address {
            cursor: pointer;
            height: 0.4rem;
            line-height: 0.4rem;
            background: #15005d;
            border-radius: 0.05rem;
            padding: 0 0.1rem;
            text-align: center;
            width: 1.2rem;
            margin: 0 auto;
          }
        }
      }
    }

    .noData {
      text-align: center;
      height: 5.5rem;
      line-height: 4rem;
    }
  }
}
.close {
  width: 0.6rem;
  height: 0.6rem;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/311774/5/3391/4556/68301533F5c43bf4b/ac7dac7b9c6e276e.png) no-repeat;
  background-size: 100% 100%;
  margin: 0.3rem auto 0;
}
</style>
