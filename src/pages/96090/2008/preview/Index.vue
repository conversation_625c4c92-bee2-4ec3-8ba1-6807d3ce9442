<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtnRules.value" @click="showRulePop"></div>
          <div class="header-btn" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop"></div>
        </div>
      </div>
    </div>
    <div class="hotZoneBox">
      <img class="hotZone" :src="furnish.prizeBg" alt="" />
      <div class="prizeBox">
        <div class="qualificationBg">
          <div class="title" :style="furnishStyles.isAccordColor.value">尊敬的伊利用户：</div>
          <div class="text" :style="furnishStyles.isAccordColor.value">
            {{ text }}
          </div>
          <div class="toBuyBtn" @click="buyNow" />
        </div>
        <div class="orderTipsText" :style="furnishStyles.orderLimitTextColor.value">
          <!--          <div>*{{dayjs(oldOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(oldOrderEndTime).format('YYYY年MM月DD日 HH:mm:ss')}}在伊利官方旗舰店有已完成的订单 </div>-->
          <div>*XXXX年XX月XX日-XXXX年XX月XX日在{{ shopName }}有已完成的订单</div>
          <div>
            *XXXX年XX月XX日-XXXX年XX月XX日{{ orderStrokeCount === 1 ? '单笔' : '多笔' }}订单购买全店商品，实付款满<span style="font-size: 0.26rem; font-weight: bolder">大于{{ orderRestrainAmount }}元</span>，订单完成24小时候更新进度。
          </div>
          <div>*订单可能存在延迟，请订单完成48h来领取权益</div>
        </div>
        <div class="choosePrizes">
          <div class="choosePrizesBox">
            <div class="list-view" v-for="(item, index) in multiplePrizeList" :key="index">
              <div class="itemBg" :style="furnishStyles.prizeItemBg.value">
                <div class="equity_name" :style="furnishStyles.prizeItemTitleColor.value">
                  {{ item.prizeName }}
                </div>
                <img class="equity_img" :src="item.prizeImg ? item.prizeImg : '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png'" alt="" />
                <div class="btm-info">
                  <div class="equity_num" :style="furnishStyles.priceColor.value">{{ item.remainCount }}</div>
                  <div v-if="item.status === 1" class="equity_btn" v-click-track="'ljlq-qy'" @click="getPrize(item)" :style="furnishStyles.getPrizeBtn.value">点击领取</div>
                  <div v-else class="equity_btn noJurisdiction" v-click-track="'ljlq-qy'" @click="getPrize(item)" :style="furnishStyles.getPrizeBtn.value">点击领取</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 品牌旗舰专区 -->
    <div class="branZone" @click="ShowToast">
      <img :src="furnish.branZone" class="branZone-img" />
    </div>
    <div ref="skuTitle" class="sku" v-if="isExposure === 1">
      <img :src="furnish.showSkuBg" class="sku-list-img" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="ShowToast">
          <img v-if="item.showSkuImage" :src="item?.showSkuImage" alt="" />
          <div class="sku-price" :style="furnishStyles.priceColor.value">
            {{ item.jdPrice }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="furnish.footerIsOpen === '1'" class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToTop.value" @click="goTop" />
    </div>
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize @close="showMyPrize = false" />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick, computed } from 'vue';
import dayjs from 'dayjs';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { defaultLadderPrizeList, defaultMultiplePrizeList, defaultStateList } from '../ts/default';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import html2canvas from 'html2canvas';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);
const total = ref(0);
// 订单笔数 1:单笔 2:多笔
const orderStrokeCount = ref(2);
// 具体笔数
const orderStrokeStatus = ref(1);
// 订单金额
const orderRestrainAmount = ref(0);
const isLoadingFinish = ref(false);

const text = '恭喜您获得特权资格，下单复购有机会领取视频月卡，数量有限，先到先得，快去选购吧！';
// 订单笔数
// const orderStrokeStatus = ref(1);
const receiveNum = ref(1);

type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
};

const multiplePrizeList = ref<Prize>([]);
multiplePrizeList.value = defaultMultiplePrizeList;
const itemTotalOrAmount = ref(1);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/272736/9/9304/27646/67e14167Fe0642c59/6fe5e844223cccd3.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/279008/30/9109/30281/67e14167F9ea235a2/c308da53be369355.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/279008/30/9109/30281/67e14167F9ea235a2/c308da53be369355.png',
  },
  {
    jdPrice: 99.99,
    showSkuImage: '//img10.360buyimg.com/imgzone/jfs/t1/272736/9/9304/27646/67e14167Fe0642c59/6fe5e844223cccd3.png',
  },
]);
const orderSkuListPreview = ref<Sku[]>([]);
const nextStateAmount = ref(0);
const shopName = ref('');

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref('');
const showGoods = ref(false);
const orderRestrainStartTime = ref(new Date().getTime());
const days = ref(180);
const formerOrderStartTime = ref();

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const award = ref<any>({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userReceiveRecordId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');

// const toSaveAddress = (id: string) => {
//   addressId.value = id;
//   showSaveAddress.value = true;
// };

const orderSkuisExposure = ref(1);
const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

const skuTitle = ref();
const buyNow = () => {
  skuTitle.value.scrollIntoView({ behavior: 'smooth' });
};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  endTime.value = dayjs(data.endTime).valueOf();
  multiplePrizeList.value = data.multiplePrizeList;
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  // orderStrokeStatus.value = data.orderStrokeStatus.toString();
  receiveNum.value = data.receiveNum;
  ruleTest.value = data.rules;
  orderSkuisExposure.value = data.orderSkuisExposure;
  isExposure.value = data.isExposure;
  orderRestrainStartTime.value = dayjs(data.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
  days.value = data.days;
  formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
  // 订单笔数 单笔多笔
  orderStrokeCount.value = data.orderStrokeCount;
  // 具体笔数
  orderStrokeStatus.value = data.orderStrokeStatus;
  // 订单金额
  orderRestrainAmount.value = data.orderRestrainAmount;
  // 订单限制 1件数 2 金额
  itemTotalOrAmount.value = data.itemTotalOrAmount;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler('screen', () => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    console.log('onMounted', activityData);
    multiplePrizeList.value = activityData.multiplePrizeList;
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
    isExposure.value = activityData.isExposure;
    orderRestrainStartTime.value = dayjs(activityData.orderRestrainStartTime).subtract(1, 'seconds').valueOf();
    days.value = activityData.days;
    formerOrderStartTime.value = dayjs(orderRestrainStartTime.value).subtract(days.value, 'days').valueOf();
    // 订单笔数
    orderStrokeCount.value = activityData.orderStrokeCount;
    // 订单金额
    orderRestrainAmount.value = activityData.orderRestrainAmount;
    // 订单限制 1件数 2 金额
    itemTotalOrAmount.value = activityData.itemTotalOrAmount;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};

const goTop = () => {
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
};
</script>
<style lang="scss" scoped>
@font-face {
  font-family: 'FZZZHJTFont';
  src: url('../style/fzzzhjt.ttf') format('truetype');
}
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  height: 5rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.28rem;
    height: 0.45rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.hotZoneBox {
  width: 7.5rem;
  margin: 0 auto;
  position: relative;
  top: 0.2rem;
  .hotZone {
    width: 7.5rem;
  }
  .prizeBox {
    position: absolute;
    top: 1rem;
    .qualificationBg {
      width: 7.5rem;
      height: 1.3rem;
      margin: 0 auto;
      background-repeat: no-repeat;
      background-size: 100%;
      padding: 0.1rem 0 0 0.48rem;
      font-weight: bolder;
      .title {
        font-size: 0.24rem;
        font-weight: 600;
        width: 4rem;
        margin: 0 0 0 1.5rem;
        text-align: center;
      }
      .text {
        font-size: 0.17rem;
        font-weight: 400;
        font-stretch: normal;
        letter-spacing: 0;
        width: 4rem;
        height: 0.89rem;
        margin: 0 0 0 1.5rem;
        text-align: center;
        // padding: 0.15rem;
      }
      .toBuyBtn {
        width: 0.95rem;
        height: 0.94rem;
        border-radius: 100%;
        margin: 0 auto;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        top: 0.4rem;
        right: 0.5rem;
        //background-color: #fff;
      }
    }
    .orderTipsText {
      width: 6.5rem;
      margin: 0 auto;
      font-size: 0.18rem;
      //div {
      //  line-height: 0.3rem;
      //}
    }
    .choosePrizes {
      width: 6.7rem;
      height: 3.5rem;
      background-repeat: no-repeat;
      background-size: 100%;
      margin: 0.4rem auto 0;
      .prizeLimit {
        text-align: center;
        height: 1rem;
        line-height: 1rem;
        margin-top: 1.3rem;
        color: #f0f8ff;
      }
      .choosePrizesBox {
        width: 6.9rem;
        height: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
        grid-gap: 0.1rem;
        box-sizing: border-box;
        overflow: hidden;
        margin: 1.5rem auto 0;
        .list-view {
          width: 100%;
          height: auto;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
          grid-gap: 0.1rem;
          box-sizing: border-box;
          overflow: hidden;
          .itemBg {
            width: 2.05rem;
            height: 2.5rem;
            border-radius: 0.25rem;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: #fff;
            padding: 0.13rem;
            text-align: center;
            border: 1px solid hwb(0deg 100% 0/27%);
            position: relative;
            margin-bottom: 0.1rem;
            .equity_img {
              margin: 0.15rem auto;
              width: 1.2rem;
              height: 1.2rem;
            }
            .equity_name {
              font-size: 0.22rem;
              line-height: 0.22rem;
              // 单行超出展示...
              word-break: break-all;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
            .btm-info {
              display: flex;
              align-items: center;
              justify-content: space-between;
              /* background-color: #ab7f7f59; */
              height: 0.35rem;
              font-size: 0.25rem;
              margin: 0.4rem 0 0 0.09rem;
            }
            .equity_num {
            }
            .equity_btn {
              width: 0.8rem;
              height: 0.28rem;
              border-radius: 0.22rem;
              background-repeat: no-repeat;
              background-size: 100%;
              font-size: 0.16rem;
              line-height: 0.28rem;
            }
            .noJurisdiction {
              filter: grayscale(1);
            }
            .no-stock {
              position: absolute;
              top: 0.6rem;
              left: 0.5rem;
              width: 1.3rem;
            }
          }
          // .equity_btn::after {
          //   content: '';
          //   position: absolute;
          //   top: 0;
          //   left: 0;
          //   width: 100%;
          //   height: 100%;
          //   background-color: rgba(0, 0, 0, 0.5); /* 半透明灰色 */
          //   border-radius: 0.22rem; /* 与按钮的圆角保持一致 */
          //   pointer-events: none; /* 确保遮罩层不会阻止点击事件 */
          // }
        }
      }
    }
  }
}
.branZone {
  width: 7.5rem;
  // height: 4.24rem;
  margin: 0 auto;
  margin-top: 0.3rem;
  position: relative;
  .branZone-img {
    width: 7.5rem;
    // height: 4.24rem;
  }
}
.sku {
  width: 7.5rem;
  padding: 0.2rem 0;
  margin: 0rem auto;
  position: relative;
  .sku-list-img {
    width: 7.5rem;
    height: auto;
  }
  .sku-list {
    width: 6.7rem;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 1.35rem;
    // background: #ff6420;
    //opacity: 0.8;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .sku-item {
    width: 3.26rem;
    overflow: hidden;
    position: relative;
    margin-bottom: 0.12rem;
    .sku-price {
      font-size: 0.4rem;
      //color: #7d4513;
      text-align: center;
      font-weight: 600;
      position: absolute;
      left: .53rem;
      bottom: 0;
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share {
  margin: 0 auto;
  .to-shop {
    margin: 0 auto;
    height: 2.5rem;
    width: 7.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
