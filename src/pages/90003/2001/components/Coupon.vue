<template>
  <div class="coupon-bk">
    <div class="info">
      <div>
        <span class="denomination">{{ prizeInfo.getResPrizeCouponResp.couponDiscount }}</span>
        <span class="full">满{{ prizeInfo.getResPrizeCouponResp.couponQuota }}元可用</span>
      </div>
      <div>仅限美赞臣部分商品可用</div>
      <div>有效期：{{ dayjs(prizeInfo.getResPrizeCouponResp.couponBeginTime).format('YYYY-MM-DD') }}-{{ dayjs(prizeInfo.getResPrizeCouponResp.couponEndTime).format('YYYY-MM-DD') }}</div>
      <div class="num">
        <div class="blue">
          限量<span>{{ prizeInfo.sendTotalCount }}份</span>
        </div>
        <div class="red">
          <b></b>剩余<span>{{ prizeInfo.surplus }}份</span>
        </div>
      </div>
    </div>
    <div class="btn">
      <img v-if="!prizeInfo.status" @click="getPrize" src="../assets/getBtn.png" alt="" />
      <img v-else @click="getPrize" src="../assets/hasGetBtn.png" alt="" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { emit } from 'process';
import { showLoadingToast, showToast } from 'vant';
import { inject, ref } from 'vue';

const props = defineProps(['prizeInfo']);

const decoData = inject('decoData') as any;

const emits = defineEmits(['reload']);

// 领取状态
const hasGet = ref(false);

const getPrize = async () => {
  if (hasGet.value) {
    showToast('您已领取过该权益');
    return;
  }
  try {
    showLoadingToast({
      message: '领取中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90003/distributePrize', {
      prizeId: props.prizeInfo.id,
    });
    if (data.status === 1) {
      hasGet.value = true;
      showToast('领取成功');
      emits('reload');
    } else {
      showToast(data.reason);
    }
  } catch (error: any) {
    showToast(error.message);
  }
};
</script>

<style scoped lang="scss">
.coupon-bk {
  width: 7rem;
  height: 2rem;
  background: url('../assets/coupon-bk.png') no-repeat;
  background-size: 100%;
  padding-left: 0.45rem;
  position: relative;
  margin-bottom: 0.4rem;
  display: flex;
  align-items: center;
}
.info {
  font-size: 0.19rem;
  line-height: 0.28rem;
  color: #004197;
  font-family: 'FZLTZHK';
  width: 4.6rem;
  .denomination {
    font-size: 0.65rem;
    line-height: 0.7rem;
    background: linear-gradient(180deg, #ff3434, #cd0202);
    -webkit-background-clip: text;
    color: transparent;
    margin-right: 0.1rem;
  }
  .full {
    font-size: 0.4rem;
    line-height: 0.45rem;
    background: linear-gradient(180deg, #ff3434, #cd0202);
    -webkit-background-clip: text;
    color: transparent;
  }
  .num {
    font-size: 0.2rem;
    color: #004197;
    line-height: 0.33rem;
    border-radius: 0.165rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    color: #fff;
    width: fit-content;
    .red {
      position: relative;
      padding: 0 0.13rem 0 0.05rem;
      background-color: #cd0202;
      z-index: 3;
    }
    .blue {
      position: relative;
      padding: 0 0.13rem;
      background-color: #00329f;
      z-index: 1;
    }
    b {
      position: absolute;
      top: 0;
      left: -0.05rem;
      transform: rotate(20deg);
      display: block;
      width: 0.1rem;
      height: 0.38rem;
      background-color: #cd0202;
      z-index: -1;
    }
  }
}
.btn {
  flex: 1;
  img {
    margin: 0 auto;
    width: 1.5rem;
  }
}
</style>
