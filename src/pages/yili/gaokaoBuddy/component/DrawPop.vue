<template>
  <div class="pop-success-bk1">
    <div class="prizeImg">
      <img :src="exchangePrize.prizeImg" alt="" />
    </div>
    <div class="drawDiv" @click="drawClick()">参与抽奖</div>
    <div class="closeDiv" @click="closeClick()"></div>
  </div>
</template>

<script lang="ts" setup>
import { gotoSkuPage } from "@/utils/platforms/jump";
import { httpRequest } from "@/utils/service";
import { showToast, showLoadingToast, closeToast } from "vant";
import { computed, ref } from "vue";

const emits = defineEmits(["close", 'drawResultEmit']);

const props = defineProps({
  exchangePrize: {
    type: Object,
    default: null,
  },
});
const closeClick = () => {
  emits("close");
};
// 抽奖
const drawClick = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post("/92011/lotteryDraw");
    console.log(data, "抽奖数据");
    closeToast();
    emits('drawResultEmit', data);
  } catch (error: any) {
    showToast(error.message);
  }
};
</script>

<style scoped lang="scss">
.pop-success-bk1 {
  position: relative;
  // background-image: url('../assets/failBk1.png');
  background: {
    image: url("../assets/drawPopBg1.png");
    repeat: no-repeat;
    size: 100% 100%;
  }
  width: 6.8rem;
  height: 8rem;
}
.drawDiv {
  width: 2.4rem;
  height: 0.68rem;
  // background-color: greenyellow;
  position: absolute;
  bottom: 2.2rem;
  font-size: 0;
  left: 50%;
  transform: translateX(-50%);
}

.closeDiv {
  width: 0.8rem;
  height: 0.8rem;
  position: absolute;
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  // background-color: red;
}
</style>
