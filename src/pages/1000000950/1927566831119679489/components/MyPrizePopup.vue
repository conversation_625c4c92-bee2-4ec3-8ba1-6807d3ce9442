<template>
  <VanPopup v-model:show="isShow" @open="getMyPrizeList">
    <div class="popup-bg">
      <div class="prize-card">
        <div class="prize-title">
          <div class="cell">时间</div>
          <div class="cell">奖品名称</div>
          <div class="cell">状态</div>
        </div>
        <div class="prize-table">
          <template v-if="prizeList.length > 0">
            <div class="prize-row" v-for="item in prizeList" :key="item.id">
              <div class="cell">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
              <div class="cell">{{ item.rightsName }}</div>
              <div class="cell">
                <span v-if="item.rightsType === 2 || item.rightsType === 4">已发放</span>
                <div v-if="item.rightsType === 3 && item.isAddress" class="fill-btn" @click="fillAddress(item)">查看地址</div>
                <div v-if="item.rightsType === 3 && !item.isAddress" class="fill-btn" @click="fillAddress(item)">填写地址</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="prize-row" style="background: none">
              <div class="cell" colspan="3">暂无数据</div>
            </div>
          </template>
        </div>
      </div>
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/288245/31/9579/1772/6836db46Fb2c9f682/860212cf7162771c.png" @click="close" />
    </div>
  </VanPopup>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { PrizeRecord } from '../script/type';
import { getMyPrize } from '../script/ajax';
import dayjs from 'dayjs';

const props = defineProps({
  showPrize: {
    type: Boolean,
    default: false,
  },
});
const prizeList = ref<PrizeRecord[]>([]);
const isShow = computed(() => props.showPrize);
const emit = defineEmits(['close', 'fillAddress']);
const getMyPrizeList = async () => {
  prizeList.value = await getMyPrize();
};
const fillAddress = (item: PrizeRecord) => {
  emit('fillAddress', item);
};
const close = () => {
  emit('close');
};
</script>
<style lang="scss" scoped>
.popup-bg {
  width: 6.5rem;
  height: 8.5rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/307347/39/4932/51727/6836d758Fe044f707/94c58735857d0017.png') no-repeat;
  background-size: 100%;
  padding-top: 1.4rem;
  .close {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    top: 0.1rem;
    right: 0.2rem;
  }
  .prize-card {
    width: 5.6rem;
    height: 6.5rem;
    margin: 0 auto;
    overflow-y: scroll;
  }
  .prize-table {
    width: 100%;
    font-family: sans-serif;
  }
  .prize-title {
    height: 0.5rem;
    display: flex;
    align-items: center;
    margin-bottom: 0.2rem;
    .cell {
      flex: 1;
      text-align: center;
      font-size: 0.24rem;
    }
  }
  .prize-row {
    height: 0.5rem;
    display: flex;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/287886/31/3108/9730/6836dee9F4a3cd07b/bd796d6a6f62a6df.png'); /* 👉 用你的背景图 */
    background-size: 100%;
    background-repeat: no-repeat;
    align-items: center;
    margin-bottom: 0.2rem;

    .cell {
      flex: 1;
      text-align: center;
      font-size: 0.18rem;
      .fill-btn {
        width: 1.2rem;
        height: 0.45rem;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/305591/18/5965/2806/6836cae3F28e0a87d/8503c71ffeca8fe5.png') no-repeat;
        background-size: 100%;
        margin: 0 auto;
        line-height: 0.45rem;
      }
    }
  }
}
</style>
