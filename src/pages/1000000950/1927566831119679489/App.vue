<template>
  <div class="kv">
    <img class="head-btn rule-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/306227/30/5926/2957/6836a851Fd136c9dc/b331155e45ff74be.png" alt="" @click="showPopup.showRule = true" />
    <img class="head-btn prize-btn" src="//img10.360buyimg.com/imgzone/jfs/t1/314657/33/4669/3106/6836a851Fffb4c82e/77936cdcf9bd8857.png" alt="" @click="showPopup.showMyPrize = true" />
    <div class="prize-list swiper-container" ref="swiperRef">
      <div class="swiper-wrapper">
        <div class="swiper-slide" v-for="item in prizeList.filter((it) => it.poolId === 1)[0]?.prizes" :key="item?.id">
          <div class="prize-card">
            <img class="prize-img" :src="item.rightsImg1" alt="" />
            <div class="prize-name">{{ item.rightsName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="tips">每消耗{{ actInfo.pool1Score }}积分，可参与1次抽奖</div>
    <div class="remaining-points" @click="showPopup.showMyPoints = true">
      剩余积分:<span>{{ actInfo.availableScore }}</span>
    </div>
    <img class="draw-btn1" src="//img10.360buyimg.com/imgzone/jfs/t1/309116/21/4759/7631/6836bd1fFb0ac60d0/5adebdcd975db2dc.png" alt="" @click="draw('1')" />
    <div class="draw-bg" v-if="actInfo.activityScore >= actInfo.pool2BlockScore">
      <img class="draw-btn2" src="//img10.360buyimg.com/imgzone/jfs/t1/314195/8/4718/7253/6836bdeaF31716690/1ad81fcf29a08ed4.png" alt="" @click="draw('2')" />
    </div>
    <div class="draw-bg draw-bg-false" v-else>
      <div class="text">
        当前实时总积分：<span>{{ actInfo.activityScore }}</span>
      </div>
      <div class="progress-container">
        <span class="label">0</span>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: fillPercent + '%' }"></div>
        </div>
        <span class="label">{{ actInfo.pool2BlockScore }}</span>
      </div>
    </div>
    <div class="task-list">
      <div class="task-info" v-for="(item, index) in actInfo.taskList" :key="index" :style="{ backgroundImage: `url(${JSON.parse(item.taskDescription).imgUrl})` }">
        <div class="tips">{{ JSON.parse(item.taskDescription).title1 }}</div>
        <div class="btn" v-if="item.status === 1" @click="doTask(item)">{{ JSON.parse(item.taskDescription).actText }}</div>
        <div class="btn gray" v-else @click="doTask(item)">{{ item.status === 0 ? JSON.parse(item.taskDescription).actText : JSON.parse(item.taskDescription).garyText }}</div>
      </div>
    </div>
  </div>
  <!-- 活动规则 -->
  <RulePopup :showRule="showPopup.showRule" @close="showPopup.showRule = false" :rule="rule" />
  <!-- 我的奖品 -->
  <MyPrizePopup :showPrize="showPopup.showMyPrize" @close="showPopup.showMyPrize = false" @fillAddress="fillAddress" />
  <!-- 我的积分 -->
  <MyPoints :showMyPoints="showPopup.showMyPoints" @close="showPopup.showMyPoints = false" />
  <!-- 浏览商品 -->
  <BrowseProducts
    :showBrowseProducts="showPopup.showBrowseProducts"
    @close="
      showPopup.showBrowseProducts = false;
      getMainActivity();
    " />
  <!-- 加购商品   -->
  <PurchaseMore
    :showPurchaseMore="showPopup.showPurchaseMore"
    @close="
      showPopup.showPurchaseMore = false;
      getMainActivity();
    " />
  <!-- 下单 -->
  <PlaceAnOrder :showPlaceAnOrder="showPopup.showPlaceAnOrder" @close="showPopup.showPlaceAnOrder = false" />
  <!-- 中奖弹窗 -->
  <WinPrize :showWinPrize="showPopup.showWinPrize" @close="showPopup.showWinPrize = false" :drawInfo="drawInfo" @showAddress="showAddress" />
  <!-- 未中奖弹窗 -->
  <FailWin :showFailWin="showPopup.showFailWin" @close="showPopup.showFailWin = false" />
  <!-- 地址弹窗 -->
  <AddressPopup :showAddress="showPopup.showAddress" @close="showPopup.showAddress = false" :id="addressId" :addressInfo="addressInfo" />
</template>
<script setup lang="ts">
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { computed, inject, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import RulePopup from './components/RulePopup.vue';
import MyPrizePopup from './components/MyPrizePopup.vue';
import MyPoints from './components/MyPoints.vue';
import BrowseProducts from './components/BrowseProducts.vue';
import PurchaseMore from './components/PurchaseMore.vue';
import PlaceAnOrder from './components/PlaceAnOrder.vue';
import WinPrize from './components/WinPrize.vue';
import FailWin from './components/FailWin.vue';
import AddressPopup from './components/AddressPopup.vue';
import { ActivityInfo, LotteryPrizeList } from './script/type';
import { mainActivity, lotteryPrizeList, getRule, followShop, sign, toLive, drawGift } from './script/ajax';
import { callShare } from '@/utils/platforms/share';
import { showToast } from 'vant';

const pathParams: any = inject('pathParams');
const helpUuid = ref('');
const rule = ref('');
const actInfo = ref<ActivityInfo>({} as ActivityInfo);
const prizeList = ref<LotteryPrizeList[]>([] as LotteryPrizeList[]);
const drawInfo = ref<any>({});
const addressId = ref('');
const addressInfo = ref({});
Swiper.use([Autoplay]);
const showPopup = reactive({
  showRule: false,
  showMyPrize: false,
  showMyPoints: false,
  showBrowseProducts: false,
  showPurchaseMore: false,
  showPlaceAnOrder: false,
  showWinPrize: false,
  showFailWin: false,
  showAddress: false,
});
const swiperRef = ref(null);
// 主接口数据
const getMainActivity = async () => {
  helpUuid.value = pathParams.shareUuid;
  actInfo.value = await mainActivity(helpUuid.value);
  if (actInfo.value.inviteStatus === 1) {
    showToast('助力成功');
  } else if (actInfo.value.inviteStatus === 2) {
    showToast('已助力过当前用户');
  } else if (actInfo.value.inviteStatus === 3) {
    showToast('您已助力过其他人');
  } else if (actInfo.value.inviteStatus === 4) {
    showToast('助力人数已达上限');
  }
};
getMainActivity();
// 奖品列表
const getPrizeList = async () => {
  prizeList.value = await lotteryPrizeList();
};
// 分享
const handleShare = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
    shareUrl: `${process.env.VUE_APP_HOST}${pathParams?.shopId}/${pathParams?.activityMainId}/?shareUuid=${actInfo.value.uuid}&debug=1`,
    afterShare: () => {},
  });
};
// 抽奖
const draw = async (poolId: string) => {
  if (poolId === '1') {
    if (actInfo.value.availableScore < actInfo.value.pool1Score) {
      showToast('积分不足');
      return;
    }
  } else if (poolId === '2') {
    if (actInfo.value.availableScore < actInfo.value.pool2Score) {
      showToast('积分不足');
      return;
    }
  }
  const res = await drawGift(poolId);
  if (res.status) {
    if (res.status === 1) {
      showPopup.showWinPrize = true;
      drawInfo.value = res;
      getMainActivity();
    }
  } else if (res.status === 0) {
    showPopup.showFailWin = true;
    getMainActivity();
  }
};
const showAddress = () => {
  showPopup.showAddress = true;
  showPopup.showWinPrize = false;
  addressId.value = drawInfo.value.userPrizeId;
};
const fillAddress = (item: any) => {
  showPopup.showAddress = true;
  showPopup.showMyPrize = false;
  addressId.value = item.id;
  addressInfo.value = item;
};
// 做任务
const doTask = async (item: any) => {
  switch (item.taskType) {
    case 1:
      await followShop();
      getMainActivity();
      setTimeout(() => {
        showToast('关注成功');
      }, 1000);
      break;
    case 2:
      await sign();
      getMainActivity();
      setTimeout(() => {
        showToast('签到成功');
      }, 1000);
      break;
    case 3:
      showPopup.showBrowseProducts = true;
      break;
    case 4:
      showPopup.showPurchaseMore = true;
      break;
    case 5:
      window.location.href = item.taskLinkUrl; // 直播间id
      await toLive(item.id);
      getMainActivity();
      break;
    case 8:
      window.location.href = item.taskLinkUrl; // 直播间id
      await toLive(item.id);
      getMainActivity();
      break;
    case 6:
      showPopup.showPlaceAnOrder = true;
      break;
    case 7:
      handleShare();
      getMainActivity();
      break;
    default:
      break;
  }
};
const fillPercent = computed(() => Math.min(100, Math.max(0, (actInfo.value.activityScore / actInfo.value.pool2BlockScore) * 100)));
onMounted(async () => {
  getPrizeList();

  rule.value = await getRule();
  nextTick(() => {
    new Swiper(swiperRef.value, {
      slidesPerView: 2.5, // 同时显示的slide数量
      observer: true,
      observeParents: true,
      autoplay: {
        delay: 2000,
      },
    });
  });
});
</script>
<style lang="scss" scoped>
.kv {
  width: 100%;
  height: 36rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/313126/37/4959/244452/683918ccF645ce6f0/8ad730bed843b0ac.jpg') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 9.6rem;
  .head-btn {
    width: 1.4rem;
    position: absolute;
    right: 0;
  }
  .rule-btn {
    top: 2.2rem;
  }
  .prize-btn {
    top: 3rem;
  }
  .prize-list {
    width: 6rem;
    height: 3.71rem;
    margin: 0 auto;
    overflow: hidden;
    .swiper-slide {
      // margin-right: 0.2rem;
      .prize-card {
        width: 2.3rem;
        height: 2.9rem;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/319546/5/4641/9901/6836a9e0F6011434b/960099e02dc5a6ad.png') no-repeat;
        background-size: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: 0.4rem;
        .prize-img {
          width: auto;
          max-height: 1.6rem;
          margin: 0.2rem auto 0.1rem;
          object-fit: contain;
        }
        .prize-name {
          width: 90%;
          font-size: 0.22rem;
          color: #3c6e2b;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .tips {
    text-align: center;
    font-size: 0.24rem;
    color: #feffeb;
    margin: 0 auto;
  }
  .remaining-points {
    position: absolute;
    top: 13.5rem;
    left: 0.2rem;
    font-style: oblique;
    color: #fff;
    font-size: 0.2rem;
    transform: rotate(-7.5deg);
    span {
      color: #ffcc00;
      font-size: 0.3rem;
    }
  }
  .draw-btn1 {
    width: 2.84rem;
    margin: 0.1rem auto 0;
  }
  .draw-bg {
    width: 7rem;
    height: 5.3rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/288453/19/11119/152840/68391933F309f770a/a6b7547ee5293f85.png') no-repeat;
    background-size: 100%;
    margin: 4.5rem auto 0;
  }
  .draw-bg-false {
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/300535/6/10262/124029/68391933Fd87c6726/0b421cf9c502242e.png') no-repeat;
    background-size: 100%;
    padding-top: 2rem;
    .text {
      width: 100%;
      font-size: 0.24rem;
      text-align: center;
      span {
        font-size: 0.3rem;
        color: #f75658;
      }
    }
    .progress-container {
      width: 96%;
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 0.2rem -0.08rem;
    }

    .label {
      font-size: 14px;
      width: 50px;
      text-align: center;
    }

    .progress-bar {
      position: relative;
      flex: 1;
      height: 24px;
      background-color: #3a8426; // 深绿色背景
      border-radius: 12px;
      overflow: hidden;
      border: 2px solid #226c18;
    }

    .progress-fill {
      height: 100%;
      background: repeating-linear-gradient(45deg, #ffeb3b, #ffeb3b 10px, #fdd835 10px, #fdd835 20px);
      transition: width 0.3s ease;
      border-radius: 12px 0 0 12px;
    }
  }
  .draw-btn2 {
    width: 2.84rem;
    position: absolute;
    top: 22.7rem;
    right: 0.98rem;
  }
  .task-list {
    width: 7rem;
    margin: 0.2rem auto;
    .task-info {
      width: 100%;
      height: 1.1rem;
      background-size: 100%;
      margin-bottom: 0.15rem;
      position: relative;
      .tips {
        position: absolute;
        top: 0.5rem;
        left: 1.2rem;
        width: 3.8rem;
        font-size: 0.18rem;
        color: #434343;
        text-align: left;
      }
      .btn {
        width: 1.63rem;
        height: 0.62rem;
        background: url('//img10.360buyimg.com/imgzone/jfs/t1/305591/18/5965/2806/6836cae3F28e0a87d/8503c71ffeca8fe5.png') no-repeat;
        background-size: 100%;
        position: absolute;
        top: 0.25rem;
        right: 0.1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.24rem;
        color: #3c6e2b;
      }
    }
  }
}
</style>
<style>
::-webkit-scrollbar {
  display: none;
}
.gray {
  filter: grayscale(1);
}
</style>
