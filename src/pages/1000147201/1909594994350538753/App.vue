<!--
 * @actName:
 * @author: <PERSON><PERSON><PERSON> lin
-->
<template>
  <!-- background -->
  <div class='background'>
    <div class='prize-btn' @click="openDialog('prizeDialog')"></div>
    <div class='rule-btn' @click="openDialog('ruleDialog')"></div>
    <div class='order-btn' @click="openDialog('orderDialog')"></div>

    <div class='flow-view'>
      <div class='flow-scroll'>
        <div class='flow-item' v-for='(item,index) in activityData.progress' :key='index'>
          <div style='font-size: .31rem' v-html='item.topName'></div>
          <img class='flow-icon' :class='{gray:!item.isReach}' style='width: 1.4rem' :src='item.topImage' alt=''>
          <div style='font-size: .22rem' v-html='item.topTips'></div>
        </div>
      </div>
    </div>

    <img src='./assets/img/title-img.png' style='width:3.84rem;margin: .1rem auto' alt=''>

    <div class='gift-view'>
      <div ref='swiperRef' class='swiper-container'>
        <div class='swiper-wrapper'>
          <div class='swiper-slide' v-for='(item, index) in activityData.actions' :key='index'>
            <div class='step-view'>
              <!-- STEP-1 -->
              <img class='step-title' :src='item.step1Topic' alt=''>
              <img v-if='item.goods?.length===1' :src='item.goods[0].image' @click='gotoSkuPage(item.goods[0].skuId)' style='width: 5.7rem;margin: 0 auto' alt=''>
              <div class='sku-show-line' v-else-if='item.goods?.length===2'>
                <img :src='item.image' style='width: 2.67rem' alt='' @click='gotoSkuPage(item.skuId)' v-for='(item,index) in item.goods' :key='index'>
              </div>
              <SkuSwiper v-else :skuList='item.goods'></SkuSwiper>

              <!-- STEP-2 -->
              <img class='step-title' style='margin-top: .3rem;height: .52rem' :src='item.step2Topic' alt=''>
              <div class='step-bg' v-if='item.combinedGifts?.length===1' :style='{backgroundImage:`url(${item.combinedGifts[0].image})`}'>
                <img src='./assets/img/has-draw-btn.png' v-if="activityData.bottoms[currentEquityIndex].buttons[0]?.status==='1'" class='step-btn gray' alt=''>
                <img src='./assets/img/draw-btn.png' v-else @click='drawGift(activityData.bottoms[currentEquityIndex].buttons[0])' class='step-btn' :class="{gray:activityData.bottoms[currentEquityIndex].buttons[0]?.status!=='0'}" alt=''>
                <span class='order-tip' v-if='item.repurchaseOrder!==-1'>目前累计复购订单：{{ item.repurchaseOrder }}单</span>
              </div>
              <div class='' v-else>
                <div class='step-bg-two' v-for='(_item,_index) in item.combinedGifts' :key='_index' :style='{backgroundImage:`url(${_item.image})`}'>
                  <img :src='getDrawBtn(activityData.bottoms[currentEquityIndex].buttons[_index]?.status)' @click='drawGift(activityData.bottoms[currentEquityIndex].buttons[_index])' class='step-btn' :class="{gray:activityData.bottoms[currentEquityIndex].buttons[_index]?.status!=='0'}" alt=''>
                  <span class='order-tip' v-if='item.repurchaseOrder!==-1'>目前累计复购订单：{{ item.repurchaseOrder }}单</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <EquitySwiper :equityList='activityData.bottoms' :activeActionSubId='activeActionSubId' :configData='configData' @slideChange='equitySwiperChange'></EquitySwiper>
  </div>

  <Popup class='popup' v-model:show='dialogShow' :close-on-click-overlay='false'>
    <SuccessDialog v-if="dialogName === 'successDialog'" :giftInfo='currentDrawGift' @fillAddress='fillAddress' @checkKeyWord='checkKeyWord'></SuccessDialog>
    <RuleDialog :rule='activityData.rules' v-if="dialogName === 'ruleDialog'"></RuleDialog>
    <KeywordDialog :keyword='currentKeyWord' v-if="dialogName === 'keywordDialog'"></KeywordDialog>
    <PrizeDialog :prizeList='activityData.myPrizes' @fillAddress='fillAddress' @checkKeyWord='checkKeyWord' v-if="dialogName === 'prizeDialog'"></PrizeDialog>
    <AddressDialog :addressInfo='currentAddress' v-if="dialogName === 'addressDialog'" @commitAddress='commitAddress'></AddressDialog>
    <OrderDialog :orderList='activityData.myOrders' v-if="dialogName === 'orderDialog'"></OrderDialog>
  </Popup>

</template>

<script lang='ts' setup>
import { ref, inject, Ref, onMounted, reactive, nextTick } from 'vue';
import { Toast, Popup, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import type { IConfigData, IActivityData } from './ts/type';
import { gotoSkuPage, gotoShopPage } from '@/utils/platforms/jump';
/* ---------------------------------  弹窗  ------------------------------ */
import SkuSwiper from './components/SkuSwiper.vue';
import EquitySwiper from './components/EquitySwiper.vue';

import RuleDialog from './components/RuleDialog.vue';
import PrizeDialog from './components/PrizeDialog.vue';
import OrderDialog from './components/OrderDialog.vue';
import KeywordDialog from './components/KeywordDialog.vue';
import AddressDialog from './components/AddressDialog.vue';
import SuccessDialog from './components/SuccessDialog.vue';
import { openDialog, closeDialog, dialogName, dialogShow } from './ts/dialog';
/* ---------------------------------  接口  ------------------------------- */
import { getDataInterface, setBaseInfo } from './ts/port';
import Swiper from 'swiper';
import 'swiper/swiper.min.css';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
setBaseInfo(baseInfo);
/* ---------------------------------  开卡  ------------------------------- */
const isOpenCard: boolean = inject('isOpenCard') as boolean;
/* -------------------------------------------------------------------------- */
const configData = reactive({}) as IConfigData;
const activityData = reactive({}) as IActivityData;

const currentAddress = ref({});
const currentKeyWord = ref('');
const stepSwiper = ref();
const activeActionSubId = ref(0);

const getDrawBtn = (status: string) => configData[`drawBtnStatus_${status}`];

const initStepSwiper = () => {
  stepSwiper.value = new Swiper('.swiper-container', {
    on: {
      slideChange() {
        activeActionSubId.value = activityData.actions[this.activeIndex].subId;
      },
    },
  });
};

// 主接口
const activityContent = async () => {
  const res = await getDataInterface('activityContent');
  if (res.result) {
    Object.assign(activityData, res.data);
    if (!stepSwiper.value) {
      console.log(stepSwiper.value, !stepSwiper.value);
      await nextTick(initStepSwiper);
    }
  }
};

const currentDrawGift = ref({});
const drawGift = async (gift: any) => {
  if (gift.status === '0') {
    const res = await getDataInterface('drawGift', { combinedId: gift.combinedId, subSort: gift.subSort });
    if (res.result) {
      currentDrawGift.value = res.data;
      openDialog('successDialog');

      setTimeout(() => {
        activityContent();
      }, 500);
    }
  } else if (gift.status === '2') {
    showToast('奖品已领光');
  } else if (gift.status === '3') {
    showToast('抱歉，您暂不符合领取条件，快去逛逛吧~');
  }
};

// 填写地址接口
const commitAddress = async (form: any) => {
  const res = await getDataInterface('editAddress', { ...form });
  if (res.result) {
    showToast('保存地址成功');
    closeDialog();
    setTimeout(() => {
      activityContent();
    }, 500);
  }
};

const fillAddress = (info: any) => {
  currentAddress.value = info;
  openDialog('addressDialog');
};

const checkKeyWord = (keyword: string) => {
  currentKeyWord.value = keyword;
  openDialog('keywordDialog');
};

// 通用配置接口
const getActivityConfig = async () => {
  const res = await getDataInterface('getActivityConfig');
  if (res.result) {
    Object.assign(configData, JSON.parse(res.data));
    console.log(configData);
  }
};

const currentEquityIndex = ref(0);
const equitySwiperChange = (swiperIndex: number) => {
  stepSwiper.value.slideTo(swiperIndex, 500, false);
  setTimeout(() => {
    currentEquityIndex.value = swiperIndex;
  }, 100);
};

activityContent();

getActivityConfig();

</script>

<style lang='scss'>
.gray {
  /*grayscale(val):val值越大灰度就越深*/
  -webkit-filter: grayscale(100%) brightness(1);
  -moz-filter: grayscale(100%) brightness(1);
  -ms-filter: grayscale(100%) brightness(1);
  -o-filter: grayscale(100%) brightness(1);
  filter: grayscale(100%) brightness(1);
  filter: gray brightness;
}

#app .van-popup {
  background-color: transparent;
}
</style>
