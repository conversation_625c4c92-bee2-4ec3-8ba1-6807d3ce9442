<template>
  <Popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false" @closed="handleClose">
    <div class="box">
      <div class="title">中奖了!</div>
      <div class="ruleText">恭喜您，中奖了！</div>
      <img class="prize-img" :src="prizeInfo.prizeImg" alt="" />
      <div class="prize-name">恭喜您成功抽中<br />【{{ prizeInfo.prizeName }}】</div>
      <div class="prize-btn" @click="handleSave">填写收货地址</div>
      <Icon name="close" size="40" class="close-icon" @click="handleClose"></Icon>
    </div>
  </Popup>
</template>
<script setup lang="ts">
import { computed, defineEmits, defineProps, PropType } from 'vue';
import { Popup, Icon } from 'vant';
import type { Prize } from '../scripts/type';

const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  prizeInfo: {
    type: Object as PropType<Prize>,
    required: true,
    default: () => ({}),
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog', 'handleSaveAddress']);
const handleClose = () => {
  emits('closeDialog');
};
const handleSave = () => {
  console.log('保存收货地址');
  emits('handleSaveAddress');
};
</script>
<style scoped lang="scss">
.box {
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/266602/26/24474/159566/67bdae74F8185e740/1b6f1542f76ae360.png) no-repeat;
  background-size: contain;
  width: 6.5rem;
  height: 10rem;
  padding: 0.13rem 0.4rem;
  box-sizing: border-box;
  position: relative;
  text-align: center;
  .title {
    margin-left: 0.91rem;
    font-size: 0.48rem;
    color: #000;
    font-weight: bold;
    text-align: left;
    font-style: italic;
  }
  .ruleText {
    font-size: 0.48rem;
    color: #ffffff;
    margin-top: 0.6rem;
  }
  .prize-img {
    width: 4.2rem;
    height: 3.2rem;
    object-fit: contain;
    margin: 0.2rem auto;
    display: block;
  }
  .prize-name {
    font-size: 0.36rem;
    color: #fff;
  }
  .prize-btn {
    background: url(../assets/btn-bg.png) no-repeat;
    background-size: 100% 100%;
    width: 3.34rem;
    height: 0.8rem;
    text-align: center;
    line-height: 0.8rem;
    font-weight: bold;
    font-size: 0.37rem;
    color: #000;
    margin: 0.2rem auto 0;
  }
  .close-icon {
    position: absolute;
    width: 1rem;
    height: 1rem;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
  }
}
</style>
