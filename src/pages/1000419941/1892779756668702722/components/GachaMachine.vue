<template>
  <div class="gacha-machine">
    <canvas ref="canvasRef"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineExpose } from 'vue';

interface Position {
  x: number;
  y: number;
}

interface BallConfig {
  url: string;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  speedFactor?: number;
}

interface Ball extends BallConfig {
  pos: Position;
  velocity: Position;
  radius: number;
  image: HTMLImageElement;
}

const props = defineProps({
  backgroundImage: {
    type: String,
    default: '',
  },
  ballConfigs: {
    type: Array as () => BallConfig[],
    required: true,
    validator: (value: BallConfig[]) => value.every((config) => 'url' in config),
  },
  canvasWidth: {
    type: Number,
    required: true,
  },
  canvasHeight: {
    type: Number,
    required: true,
  },
  moveArea: {
    type: Object as () => { x: number; y: number; width: number; height: number },
    default: () => ({ x: 0, y: 0, width: 400, height: 600 }),
    validator: (value) => value.x >= 0 && value.y >= 0 && value.width > 0 && value.height > 0 && value.x + value.width <= props.canvasWidth && value.y + value.height <= props.canvasHeight,
  },
  maxSpeed: {
    type: Number,
    default: 4,
  },
  speedMultiplier: {
    type: Number,
    default: 1,
  },
  stopThreshold: {
    type: Number,
    default: 0.1,
  },
});

const emit = defineEmits(['animation-end']);

const canvasRef = ref<HTMLCanvasElement | null>(null);
const ctx = ref<CanvasRenderingContext2D | null>(null);
const balls = ref<Ball[]>([]);
const animationFrameId = ref<number | null>(null);
const bgImage = ref<HTMLImageElement | null>(null);

// 初始化画布
const initCanvas = () => {
  if (!canvasRef.value) return;
  canvasRef.value.width = props.canvasWidth;
  canvasRef.value.height = props.canvasHeight;
  ctx.value = canvasRef.value.getContext('2d', { willReadFrequently: true });
};

// 加载图片
const loadImage = (url: string): Promise<HTMLImageElement> => {
  console.log(url);
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = url;
    img.onload = () => resolve(img);
    img.onerror = (err) => reject(new Error(`图片加载失败: ${url}, ${err}`));
  });
};

// 创建小球
const createBalls = async (initial = true) => {
  const loadedBalls = await Promise.all(
    props.ballConfigs.map(async (config: any) => {
      const image = await loadImage(config.url);
      const width = config.width || image.width;
      const height = config.height || image.height;
      const speedFactor = config.speedFactor || 1;

      // 确保初始位置在移动区域内
      const clamp = (value: number, min: number, max: number) => Math.max(min, Math.min(value, max));

      return {
        ...config,
        image,
        width,
        height,
        radius: Math.max(width, height) / 2,
        pos: {
          x: config.x !== undefined ? clamp(config.x, props.moveArea.x + width / 2, props.moveArea.x + props.moveArea.width - width / 2) : props.moveArea.x + Math.random() * props.moveArea.width,
          y: config.y !== undefined ? clamp(config.y, props.moveArea.y + height / 2, props.moveArea.y + props.moveArea.height - height / 2) : props.moveArea.y + Math.random() * props.moveArea.height,
        },
        velocity: {
          x: initial ? 0 : (Math.random() - 0.5) * props.maxSpeed * speedFactor * props.speedMultiplier,
          y: initial ? 0 : (Math.random() - 0.5) * props.maxSpeed * speedFactor * props.speedMultiplier,
        },
      } as Ball;
    }),
  );
  balls.value = loadedBalls;
};

// 边界碰撞检测
const checkBoundaryCollision = (ball: Ball) => {
  const left = props.moveArea.x + ball.radius;
  const right = props.moveArea.x + props.moveArea.width - ball.radius;
  const top = props.moveArea.y + ball.radius;
  const bottom = props.moveArea.y + props.moveArea.height - ball.radius;

  if (ball.pos.x < left) {
    ball.pos.x = left;
    ball.velocity.x *= -0.8;
  }
  if (ball.pos.x > right) {
    ball.pos.x = right;
    ball.velocity.x *= -0.8;
  }
  if (ball.pos.y < top) {
    ball.pos.y = top;
    ball.velocity.y *= -0.8;
  }
  if (ball.pos.y > bottom) {
    ball.pos.y = bottom;
    ball.velocity.y *= -0.8;
  }
};

// 小球碰撞检测
const checkBallCollision = () => {
  for (let i = 0; i < balls.value.length; i++) {
    for (let j = i + 1; j < balls.value.length; j++) {
      const ball1 = balls.value[i];
      const ball2 = balls.value[j];
      const dx = ball2.pos.x - ball1.pos.x;
      const dy = ball2.pos.y - ball1.pos.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < ball1.radius + ball2.radius) {
        const angle = Math.atan2(dy, dx);
        const speed1 = Math.sqrt(ball1.velocity.x ** 2 + ball1.velocity.y ** 2);
        const speed2 = Math.sqrt(ball2.velocity.x ** 2 + ball2.velocity.y ** 2);
        const direction1 = Math.atan2(ball1.velocity.y, ball1.velocity.x);
        const direction2 = Math.atan2(ball2.velocity.y, ball2.velocity.x);

        ball1.velocity.x = Math.cos(direction2) * speed2;
        ball1.velocity.y = Math.sin(direction2) * speed2;
        ball2.velocity.x = Math.cos(direction1) * speed1;
        ball2.velocity.y = Math.sin(direction1) * speed1;
      }
    }
  }
};

// 更新小球位置
const updateBalls = () => {
  balls.value.forEach((ball) => {
    ball.velocity.x *= props.speedMultiplier;
    ball.velocity.y *= props.speedMultiplier;
    ball.pos.x += ball.velocity.x;
    ball.pos.y += ball.velocity.y;
    checkBoundaryCollision(ball);
  });
  checkBallCollision();
};

// 绘制
const draw = () => {
  if (!ctx.value) return;

  // 清空画布
  ctx.value.clearRect(0, 0, props.canvasWidth, props.canvasHeight);

  // 绘制背景图（如果有）
  if (bgImage.value) {
    ctx.value.drawImage(bgImage.value, 0, 0, props.canvasWidth, props.canvasHeight);
  }

  // 绘制小球
  balls.value.forEach((ball) => {
    ctx.value?.drawImage(ball.image, ball.pos.x - ball.width! / 2, ball.pos.y - ball.height! / 2, ball.width!, ball.height!);
  });
};

// 检查动画结束条件
const checkAnimationEnd = (): boolean => {
  const totalSpeed = balls.value.reduce((sum, ball) => sum + Math.abs(ball.velocity.x) + Math.abs(ball.velocity.y), 0);
  return totalSpeed < props.stopThreshold;
};

// 停止动画
const stopAnimation = () => {
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }
};
// 动画循环
const animate = () => {
  updateBalls();
  draw();

  if (checkAnimationEnd()) {
    stopAnimation();
    emit('animation-end');
  } else {
    animationFrameId.value = requestAnimationFrame(animate);
  }
};

// 暴露的方法
const startAnimation = async () => {
  stopAnimation();
  await createBalls(false);
  animate();
};

// 初始化
onMounted(async () => {
  try {
    initCanvas();
    if (props.backgroundImage) {
      bgImage.value = await loadImage(props.backgroundImage);
    }
    await createBalls();
    draw();
  } catch (error: any) {
    console.error('初始化失败:', error);
  }
});

defineExpose({
  startAnimation,
  stopAnimation,
});
</script>

<style scoped>
.gacha-machine {
  border: 2px solid #333;
  border-radius: 8px;
  overflow: hidden;
  background: transparent;
}
canvas {
  display: block;
}
</style>
