<!--
 * @actName:
 * @author: <PERSON><PERSON><PERSON> lin
-->
<template>
  <!-- background -->
  <div class="home pageBg" :style="furnishStyles.pageBg.value" >
    <div class="head-btn">
      <!-- 活动规则按钮 -->
      <div class="handle-side-btn" v-click-track="'hdgz'" @click="showRule = true"></div>
      <!-- 我的奖品按钮 -->
      <div class="handle-side-btn" v-click-track="'wdjp'" style="top: 6.5rem" @click="showMyPrize = true"></div>
    </div>
    <!-- 手机号验证码信息收集 -->
<!--    <div class="phone-info-view" @click="test()">-->
    <div class="phone-info-view">
      <div class="phone-input">
        <input v-model="mobile" type="text" maxlength="11" placeholder="请输入您的手机号码">
      </div>
      <div class="phone-input">
        <input v-model="verificationCode" type="text" maxlength="6" placeholder="请输入短信验证码">
        <div class="send-verification-btn" :class="{gray:isCounting}" v-click-track="'hqyzm'" @click="getVerificationCode()">
          {{ buttonText }}
        </div>
      </div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/255901/2/29262/25125/67c7f149F079238d6/88d90b2ddd0eb96e.png"
           @click="commitCheck()" v-click-track="'ljtjcxzg'" class="commit-check-btn" alt="">
    </div>

    <div class="step-view">
      <img :src="furnish.stepImg" style="width: 7.18rem;margin: 0 auto" alt="">
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showOpenCard" :close-on-click-overlay="false">
    <ToJoinDialog @closeDialog="showOpenCard = false"/>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showRule" :close-on-click-overlay="false">
    <RuleDialog @closeDialog="showRule = false" :rule="rule"/>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showMyPrize" :close-on-click-overlay="false">
    <MyRecordDialog @closeDialog="showMyPrize = false"/>
  </VanPopup>
</template>

<script lang='ts' setup>
import { ref, inject, onUnmounted, computed, watch, watchEffect } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

/* ---------------------------------  弹窗  ------------------------------ */
import ToJoinDialog from '../components/ToJoinDialog.vue';
import RuleDialog from '../components/RuleDialog.vue';
import MyRecordDialog from '../components/MyRecordDialog.vue';

/* ---------------------------------  接口  ------------------------------- */
import { getDataInterface, setBaseInfo } from '../ts/port';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { DecoData } from '@/types/DecoData';
import dayjs from 'dayjs';
import { httpRequest } from '@/utils/service';

const router = useRouter();
const store = useStore();

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
setBaseInfo(baseInfo);

const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};

const checkActTime = () => {
  if (!isStart.value && !isEnd.value) {
    showToast('活动未开始');
    return false;
  }
  if (isEnd.value) {
    showToast('活动已结束');
    return false;
  }
  return true;
};

/* -------------------------------------------------------------------------- */
const showOpenCard = ref(false);
const showRule = ref(false);
const rule = ref('');
const showMyPrize = ref(false);

const getRule = async () => {
  const res = await getDataInterface('getRuleInfo', 'get');
  if (res.result) {
    rule.value = res.data;
  }
};

// 定时器对象
const timer = ref();
const countdown = ref(0);
// 计算属性：是否正在倒计时
const isCounting = computed(() => countdown.value > 0);
// 计算属性：按钮文字
const buttonText = computed(() => (isCounting.value ? `重新获取(${countdown.value}s)` : '获取验证码'));

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  timer.value = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(timer.value);
    }
  }, 1000);
};

// 电话号码
const mobile = ref();
// 监听 mobile 的变化
watch(mobile, (newVal) => {
  // 过滤非数字字符
  mobile.value = newVal.replace(/\D/g, '');
});

// 验证码
const verificationCode = ref();
// 监听 verificationCode 的变化
watch(verificationCode, (newVal) => {
  // 过滤非数字字符
  verificationCode.value = newVal.replace(/\D/g, '');
});

// 发送验证码请求
const getVerificationCode = async () => {
  if (!checkActTime()) {
    return;
  }
  // 校验电话号码
  console.log('校验号码');
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!mobile.value) {
    showToast('请输入您的手机号码');
    return;
  } if (!checkPhone.test(mobile.value)) {
    showToast('请输入正确的手机号码');
    return;
  }
  // 检查是否正在倒计时
  if (isCounting.value) return;
  // 发送验证码
  const res = await getDataInterface('sendCode', 'post', { mobile: mobile.value });
  if (res.data) {
    // 开始倒计时
    startCountdown();
  } else {
    showToast('发送失败');
  }
};

// 上传类型 1-出生证明 2-宝宝照片
const actType = ref(0);
// 上传名称
const uploadName = ref('');
// 领取成功的号码
const successPhone = computed(() => store.getters.getSuccessPhone);
const successMobile = ref('');

// 立即提交查询资格
const commitCheck = async () => {
  // router.replace({ path: '/uploadImage' });
  if (!checkActTime()) {
    return;
  }
  // 未输入手机号
  if (!mobile.value) {
    showToast('请输入您的手机号码');
    return;
  }
  // 未输入验证码
  if (!verificationCode.value) {
    showToast('请输入短信验证码');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90019/getUserQualification', { mobile: mobile.value, smsCode: verificationCode.value });
    closeToast();
    console.log('立即提交查询资格', res.data);
    // 满足领取条件按 userIdentity 1 满足资格 2 不满足
    if (res.data?.userIdentity === 1) {
      // 拿到成功后返回的手机号
      store.commit('setSuccessPhone', mobile.value);
      router.replace({ path: '/uploadImage' });
    } else {
      router.replace({ path: '/result' });
    }
  } catch (error) {
    if (error.message === '手机号验证码未通过') {
      showToast(error.message);
    } else {
      router.replace({ path: '/result' });
    }
  }
};

// 主接口
const activityContent = async () => {
  const res = await getDataInterface('activityContent', 'post');
  if (res.result) {
    // console.log('主接口', res.data);
    actType.value = res.data.photoType; // 上传照片类型1-出生证 2-其他
    uploadName.value = res.data.certificateName ? res.data.certificateName : '其他照片';
    store.commit('setActType', actType.value);
    store.commit('setMutualExclusion', res.data.mutualExclusion); // 是否与0元试喝
    if (actType.value === 2) {
      store.commit('setUploadName', uploadName.value);
    }
  }
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  // 检查是否开卡
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
  }
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  getTime();
  await Promise.all([getRule(), activityContent()]);
  checkActTime();
};

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer.value) clearInterval(timer.value);
});

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>

<style lang='scss'>
.home {
  padding-top: 11.86rem;
  padding-bottom: 0.8rem;
  width: 7.5rem;
  min-height:100vh;
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  .head-btn{
    position: absolute;
    top: 4rem;
    //background-color: #ffffffa1;
    width: 1rem;
    height: 3.7rem;
    right: 0;
  }

  .handle-side-btn {
    width: 0.6rem;
    height: 1.8rem;
    /* position: absolute; */
    right: 0;
    top: 4.4rem;
    margin: 0 0 0.2rem 0.35rem;
  }

  .phone-info-view {
    width: 7.18rem;
    height: 2.9rem;
    margin: 0rem auto 0;
    padding: .35rem 0;
    text-align: center;
    background: {
      image: url("../assets/img/phone-info-view.png");
      repeat: no-repeat;
      size: contain;
    };

    .phone-input {
      width: 4rem;
      height: .6rem;
      line-height: .2rem;
      padding-left: .15rem;
      margin: 0 auto .2rem;
      border: 1px solid #1d2e81;
      border-radius: .5rem;
      position: relative;

      input {
        width: 3.8rem;
        font-size: .28rem;
        line-height: .56rem;
        color: #434343;
        letter-spacing: .01rem;
        background-color: transparent !important;
        border: transparent !important;
      }

      input::placeholder {
        color: #999; /* 占位文字颜色 */
        font-size: .24rem; /* 字体大小 */
        letter-spacing: -.01rem;
      }

      .send-verification-btn {
        width: 1.7rem;
        height: .587rem;
        line-height: .61rem;
        text-align: center;
        font-size: .25rem;
        color: #FFFFFF;
        position: absolute;
        right: -.01rem;
        top: -.01rem;
        background: {
          image: url("//img10.360buyimg.com/imgzone/jfs/t1/254993/26/29046/1831/67c7eb98F7e1c6b00/f7a7f44f7518d57f.png");
          size: contain;
          repeat: no-repeat;
        };
      }
    }

    .commit-check-btn {
      width: 3.04rem;
      margin: .1rem auto 0;
    }
  }

  .step-view {
    text-align: center;
    margin-top: .3rem;
  }
}

</style>
