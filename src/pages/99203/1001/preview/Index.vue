<template>
  <div class='bg' :style='furnishStyles.pageBg.value'>
    <div v-show='isShowHome'>
      <div class='header-kv select-hover'>
        <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/246398/33/23985/2706/6731cd5aF3e1b3336/8e2b294dbd0f9ac8.png'" alt='' class='kv-img' />
        <div class='header-content'>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click='rulePopup=true'>
            <div>活动规则</div>
          </div>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click='myPrizePopup=true'>
            <div>我的奖品</div>
          </div>
          <div class='header-btn' :style='furnishStyles.headerBtn.value' @click='taskPopup=true'>
            <div>补签任务</div>
          </div>
        </div>
      </div>

      <div class='calendar-bg' :style='furnishStyles.calendarBg.value'>

        <div class='sign-in-area'>
          <img :src='furnish.signInBeforeIcon' alt='' class='icon' />
          <div class='text'>
            <p class='title title-before'>今日未签到</p>
            <p class='tip'>赶紧点击按钮签到哦~</p>
          </div>
          <div class='btn'>
            <img :src='furnish.signInBeforeBt' alt='' style='width: 1.5rem;cursor: pointer;' @click='toSign()' />
            <div class='sign-days'>累计签{{ signDays }}天</div>
          </div>
        </div>
        <div class='info'> {{ dayjs().format('MM月') }}</div>
        <Calendar></Calendar>
      </div>

      <div class='bg-view'>
        <img class='view-title' src='//img10.360buyimg.com/imgzone/jfs/t1/210303/13/47555/3433/6732f5a4F4fd8e92e/eb513be6960e6004.png' alt=''>
        <div class='gift-view' v-for='(item,index) in dayPrize' :key='index'>
          <template v-if='item.prizeName'>
            <img :src='item.prizeImg' style='width: 1rem' alt=''>
            <div class='gift-info'>
              <div>每日签到可得{{ item.prizeName }}</div>
              <div class='tip'>*积分自动发放，无需领取</div>
            </div>
          </template>
        </div>
      </div>

      <div class='bg-view'>
        <img class='view-title' src='//img10.360buyimg.com/imgzone/jfs/t1/237781/29/27265/4373/6732f5a3F78e7f9bf/9d0a560d0630be3e.png' alt=''>
        <div class='gift-view' v-for='(item,index) in signPrize' :key='index'>
          <template v-if='item.prizeName'>
            <img :src='item.prizeImg' style='width: 1rem' alt=''>
            <div class='gift-info'>
              <div>累计签到{{ item.signDays }}天可得</div>
              <div class='gift-name'>({{ item.prizeName }})</div>
              <div class='gift-stock'>剩余{{ item.remainNum }}份</div>
            </div>
            <div class='draw-btn' @click='clickShowTosat()'>立即领取</div>
          </template>
        </div>
      </div>

      <div class='bg-view'>
        <div class='gift-view' style='margin-top: 0'>
          <img src='//img10.360buyimg.com/imgzone/jfs/t1/242075/22/23135/3218/67330a81F3c2bfd87/62b61c0df6442b3c.png' style='width: 1rem' alt=''>
          <div class='gift-info'>
            <div>做任务得补签卡</div>
            <div class='tip'>当前可用补签卡张数：0 张</div>
          </div>
          <div class='draw-btn' @click='taskPopup=true'>立即参与 ></div>
        </div>
      </div>
    </div>

    <!-- 规则弹窗 -->
    <Popup teleport='body' v-model:show='rulePopup' :close-on-click-overlay='false'>
      <div class='box'>
        <div class='close' @click='rulePopup=false'></div>
        <div class='rule-bk'>
          <div class='content' v-html='ruleText' v-if='ruleText'></div>
          <div class='none-data-tip' v-else>暂无数据</div>
        </div>
      </div>
    </Popup>

    <!-- 我的奖品弹窗 -->
    <Popup teleport='body' v-model:show='myPrizePopup' :close-on-click-overlay='false'>
      <div class='box'>
        <div class='close' @click='myPrizePopup=false'></div>
        <div class='myPrize-bk'>
          <div class='content'>
            <ul>
              <li>日期</li>
              <li>奖品</li>
              <li>状态</li>
            </ul>
            <div class='none-data-tip'>暂无获奖记录哦~</div>
          </div>
        </div>
      </div>
    </Popup>

    <Popup teleport='body' v-model:show='taskPopup' position='bottom'>
      <TaskPopup :taskList='taskList'></TaskPopup>
    </Popup>
  </div>
</template>

<script lang='ts' setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import useSendMessage from '@/hooks/useSendMessage';
import TaskPopup from '../components/TaskPopup.vue';

import { showToast, Popup } from 'vant';
import Calendar from '../components/Calendar.vue';
import dayjs from 'dayjs';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isShowHome = ref(true);

const continuousSignDays = ref(0); // 连续签到天数
const signDays = ref(0); // 签到总天数
const ruleText = ref('');
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const prizeTipPopup = ref(false);
const taskPopup = ref(false);
const dayPrize = ref([]);
const signPrize = ref([]);
const taskList = ref([]);

const clickShowTosat = () => {
  showToast('活动预览，仅供查看');
};

const toSign = () => {
  showToast('活动预览，仅供查看');
};

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  dayPrize.value = data.prizeDay;
  signPrize.value = data.prizeList;
  taskList.value = data.taskList;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    showToast('活动预览，仅供查看');
  }
  console.log(activityData, decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
});
</script>

<style scoped lang='scss'>
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.gray {
  color: #8b7c7c;
  filter: grayscale(100%);
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.no-member-mask {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 2rem;
    right: 0;

    .header-btn {
      height: .41rem;
      line-height: .45rem;
      border-radius: .2rem 0 0 0.2rem;
      background-color: #CA8970;
      color: #FFFFFF;
      cursor: pointer;
      padding: 0 0.2rem;
      font-size: 0.19rem;
      text-align: center;
      margin-bottom: .15rem;
    }
  }
}

.calendar-bg {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 6.9rem;
  height: 9.7rem;
  margin: 0.3rem auto 0;
  padding: 0.15rem;

  .sign-in-area {
    height: 2rem;
    display: flex;
    align-items: center;
    padding: 0 0.3rem;

    .icon {
      width: 1rem;
    }

    .text {
      flex: 1;
      padding-left: 0.38rem;

      .title {
        font-size: 0.38rem;
        font-weight: bold;
        color: #CA8970;
      }

      .title-before {
        color: #CA8970;
      }

      .tip {
        font-size: 0.22rem;
        color: #3C3C3C;
      }
    }

    .sign-days {
      font-size: .18rem;
      color: #CA8970;
      text-align: center;
    }
  }

  .info {
    width: .67rem;
    text-align: center;
    margin: 0.8rem 0 0 1.5rem;
    padding-bottom: .2rem;
    border-bottom: 4px solid #CA8970;
    color: #000000;
    font-size: 0.3rem;
  }
}

.bg-view {
  width: 6.73rem;
  margin: .35rem auto 0;
  border-radius: .2rem;
  background-color: #FFFFFF;
  padding: .45rem .36rem;

  .view-title {
    height: .38rem;
  }

  .gift-view {
    margin-top: .45rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .gift-info {
      padding-left: .35rem;
      color: #000000;
      font-size: .29rem;
      flex: 1;

      .tip {
        font-size: .22rem;
        color: #3C3C3C;
        margin-top: .13rem;
      }

      .gift-name {
        font-size: .22rem;
        color: #3C3C3C;
        margin-top: .13rem;
      }

      .gift-stock {
        font-size: .18rem;
        color: #3C3C3C;
        margin-top: .08rem;
      }
    }

    .draw-btn {
      width: 1.51rem;
      height: .45rem;
      cursor: pointer;
      line-height: .48rem;
      background-color: #CA8970;
      color: #FFFFFF;
      text-align: center;
      font-size: .2rem;
      border-radius: .22rem;
    }
  }
}

.box {
  width: 6rem;
  height: 8rem;
  position: relative;

  .rule-bk {
    width: 6rem;
    height: 7rem;
    position: absolute;
    top: 1rem;
    left: 0;
    padding-top: 1.4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/120850/5/50017/5670/672b0d55F63162d80/4765ec08d6753c76.png");
      repeat: no-repeat;
      size: contain;
    };
  }

  .content {
    height: 5rem;
    padding: 0 .1rem;
    border: 0.3rem solid transparent;
    overflow-y: auto;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
  }

  .myPrize-bk {
    width: 6rem;
    height: 7rem;
    position: absolute;
    top: 1rem;
    left: 0;
    padding-top: 1.4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/162595/29/51512/5779/672c5d73F6e5a3099/d4f85cb79a13db81.png");
      repeat: no-repeat;
      size: contain;
    };

    ul {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      margin-bottom: .15rem;
      line-height: .5rem;

      li {
        font-size: .22rem;
        color: #7C7C7C;
        text-align: center;
        width: 33%;
      }
    }
  }

  .close {
    width: .4rem;
    height: .4rem;
    background: {
      image: url("//img10.360buyimg.com/imgzone/jfs/t1/156491/25/50723/319/672acd28Fbda234d2/2910e6697479f05f.png");
      repeat: no-repeat;
      size: contain;
    };
    position: absolute;
    top: .3rem;
    right: 0;
  }

  .none-data-tip {
    color: #8c8c8c;
    font-size: .3rem;
    text-align: center;
    line-height: 1.5rem;
  }
}
</style>
