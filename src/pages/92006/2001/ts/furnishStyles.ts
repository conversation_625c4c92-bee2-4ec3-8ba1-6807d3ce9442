import { computed, reactive } from 'vue';

export const furnish = reactive<{ [x: string]: string }>({
  pageBg: '',
  actBgColor: '',
  ruleBtn: '',
  orderRecordBtn: '',
  actRecordBtn: '',
  drawRecordBtn: '',
  skuBg: '',
  skuCheckedBg: '',
  skuUnCheckedBg: '',
  skuNameColor: '',
  alertColor: '',
  step1Title: '',
  step2Title: '',
  step3Title: '',
  step4Title: '',
  step1Btn: '',
  step2Btn: '',
  step3Btn: '',
  step4Btn: '',
  checkedTabBg: '',
  unCheckedTabBg: '',
  checkedTabColor: '',
  UNcheckedTabColor: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  ruleBk: '',
  ruleTextColor: '',
  myPrizeNewBtnBg: '',
  myPrizeNewPopBg: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));

const skuBg = computed(() => ({
  backgroundImage: furnish.skuBg ? `url("${furnish.skuBg}")` : '',
}));

const skuColor = computed(() => ({
  color: furnish.skuNameColor ?? '',
}));

const alertColor = computed(() => ({
  color: furnish.alertColor ?? '',
}));

const ruleBk = computed(() => ({
  backgroundImage: furnish.ruleBk ? `url("${furnish.ruleBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/272093/16/27957/9489/6811cb14F468e3d4d/481eeee4a9d6f474.png")',
}));

const ruleTextColor = computed(() => ({
  color: furnish.ruleTextColor ?? '#000',
}));

const addCartAgainBk = computed(() => ({
  backgroundImage: furnish.addCartAgainBk ? `url("${furnish.addCartAgainBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/283526/26/27614/36971/6811cb10F6b44f9f4/7ecbc5278a1e385a.png")',
}));

const receiveSuccessBk = computed(() => ({
  backgroundImage: furnish.receiveSuccessBk ? `url("${furnish.receiveSuccessBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/294440/40/4383/125036/681cb316F992a3188/3b1d969a281c0a3d.png")',
}));

const receiveSuccessOthBk = computed(() => ({
  backgroundImage: furnish.receiveSuccessOthBk ? `url("${furnish.receiveSuccessOthBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/302995/23/3594/25508/681b4382Fa6be10bb/64c2e0dfd1909443.png")',
}));

const receiveSuccessGiftBk = computed(() => ({
  backgroundImage: furnish.receiveSuccessGiftBk ? `url("${furnish.receiveSuccessGiftBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/236070/5/24038/29166/68198998F06a25cee/f5b2ac6f74ffe0aa.png")',
}));

const joinMemberBk = computed(() => ({
  backgroundImage: furnish.joinMemberBk ? `url("${furnish.joinMemberBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/275875/14/28286/34559/6811cb11F68303d12/f4a3b2fe120c963b.png")',
}));

const saveAddressBk = computed(() => ({
  backgroundImage: furnish.saveAddressBk ? `url("${furnish.saveAddressBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/151396/36/50329/12811/681ad9ddF336684a0/5908e0525e8bbf8a.png")',
}));

const myPrizeBk = computed(() => ({
  backgroundImage: furnish.myPrizeBk ? `url("${furnish.myPrizeBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/290136/7/1439/13592/6811cb10Fce64a46e/61cdffa3d86ce24e.png")',
}));

const myActRecordBk = computed(() => ({
  backgroundImage: furnish.myActRecordBk ? `url("${furnish.myActRecordBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/287158/32/1573/9496/6811cb13Fb1c2ed5a/9963ad53c106e59a.png")',
}));

const myActDrawBk = computed(() => ({
  backgroundImage: furnish.myActDrawBk ? `url("${furnish.myActDrawBk}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/302474/34/3980/9648/681c5f7fF48cb964b/4a6953ff2a04a4e9.png")',
}));

const myPrizeColor = computed(() => ({
  color: furnish.myPrizeColor ?? '#005c11',
}));
const myPrizBorder = computed(() => ({
  border: `1px dashed ${furnish.myPrizBorderColor ?? '#005c11'}`,
}));

const myPrizePopBg = computed(() => ({
  backgroundImage: furnish.myPrizeNewPopBg ? `url("${furnish.myPrizeNewPopBg}")` : 'url("//img10.360buyimg.com/imgzone/jfs/t1/303476/22/5418/9709/6821e386Fc96e59cc/ba617edd8c68b13f.png")',
  // backgroundImage: 'url("//img10.360buyimg.com/imgzone/jfs/t1/303476/22/5418/9709/6821e386Fc96e59cc/ba617edd8c68b13f.png")',
}));
export default {
  pageBg,
  skuBg,
  skuColor,
  alertColor,
  receiveSuccessBk,
  receiveSuccessOthBk,
  receiveSuccessGiftBk,
  joinMemberBk,
  saveAddressBk,
  myPrizeBk,
  myPrizeColor,
  myPrizBorder,
  myActRecordBk,
  myActDrawBk,
  ruleBk,
  addCartAgainBk,
  ruleTextColor,
  myPrizePopBg,
};
