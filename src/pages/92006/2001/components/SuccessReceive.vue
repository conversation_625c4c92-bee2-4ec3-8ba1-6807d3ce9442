<template>
  <div class="rule-bk" :style="prize.prizeType == 3 ? furnishStyles.receiveSuccessGiftBk.value : prize.prizeType == 7 ? furnishStyles.receiveSuccessBk.value : furnishStyles.receiveSuccessOthBk.value">
    <img class="prizeImg" :src='prize.prizeImg'/>
    <div class="prizeName">{{prize.prizeName}}</div>
    <div v-if='prize.prizeType == 3' class="btn" @click="getPrize(prize)"></div>
<!--    <div v-else>-->
<!--      <div class="p3" v-if="prize.prizeType === 2">京豆已放到您的账户中 京东-我的-京豆 中查看</div>-->
<!--      <div class="p3" v-if="prize.prizeType === 4">积分已发放到您的账户中 店铺会员页 中查看</div>-->
<!--    </div>-->
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
import furnishStyles from '../ts/furnishStyles';

const emits = defineEmits(['close', 'showCardNum', 'getGift']);

const props = defineProps({
  prize: {
    type: Object,
    default: '',
  },
});

const getPrize = (prize) => {
  emits('getGift', props.prize);
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100% 100%;
  width: 6.03rem;
  height: 9.4rem;
  background-repeat: no-repeat;
  position: relative;

  .prizeImg{
    position: absolute;
    top: 3.7rem;
    left: 50%;
    transform: translate(-50%);
    width: auto;
    height: 1.6rem;
    cursor: pointer;
  }

  .prizeName{
    position: absolute;
    top: 5.7rem;
    left: 50%;
    transform: translate(-50%);
    width: 5rem;
    text-align: center;
    font-size: .27rem;
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
  }

  .btn {
    position: absolute;
    bottom: 2rem;
    left: 1.2rem;
    width: 3.6rem;
    height: 1rem;
    cursor: pointer;
  }
  .p3 {
    font-size: 0.24rem;
    text-align: center;
    position: absolute;
    bottom: 2rem;
    width: 100%;
  }

}
</style>
