<template>
  <div class="rule-img">
    <img :src="ruleImg" alt="" />
  </div>
</template>

<script lang="ts" setup>
import { computed, effect, ref } from 'vue';

const props = defineProps(['decoData']);

const actTab = computed(() => props.decoData.actTab);

const ruleImg = computed(() => {
  if (actTab.value === '1') {
    return props.decoData.ruleImg;
  }
  if (actTab.value === '2') {
    return props.decoData.blackRuleImg;
  }
  return props.decoData.blackRuleImg;
});
</script>

<style scoped lang="scss">
.rule-img {
  width: 6.3rem;
  img {
    width: 100%;
  }
}
</style>
