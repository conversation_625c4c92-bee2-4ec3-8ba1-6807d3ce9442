<template>
  <div class="bg" :style="furnishStyles.actBgColor.value" :class="{ select: showSelect }" v-if="isLoadingFinish">
    <!-- 奖品主图区域 -->
    <div class="kv-box">
      <img alt="" :src="furnishStyles.actBgURL.value.backgroundImage" :style="{ width: '7.5rem', height: furnishStyles.actBgURL.value.backgroundImage ? '' : '2.4rem' }" />
      <div class="shop-name-text" :style="furnishStyles.shopNameColor.value" >
        <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
      </div>
      <!-- <div class="user-points">
        <div class="item" v-for="item in userPoints" :key="item">{{ item }}</div>
      </div> -->
    </div>
    <!-- 兑换产品信息区域 -->
    <div class="content" :style="furnishStyles.prizeInfoBg.value">
      <div class="block">
        <div class="goods-info">
          <img v-if="furnish.showPrizeImg" :src="exchangeGoodsImg" alt="" class="goods-img" />
          <div class="info" :style="furnishStyles.prizeNameTextColor.value">
            <div class="name">{{ exchangeGoodsName }}</div>
            <div class="points" v-show="exchangeGoodsName">
              消耗<span :style="furnishStyles.integralTextColor.value">{{ exchangePoint }}</span
              >积分
            </div>
            <div class="points" v-show="exchangeGoodsName">
              当前剩余<span :style="furnishStyles.integralTextColor.value">{{ userPoints }}</span
              >积分
            </div>
          </div>
        </div>
        <div>
<!--          <div class="goods-num" v-if="exchangeLimit === 1">-->
<!--            活动期内限剩余兑换次数：<span :style="furnishStyles.exchangeNumTextColor.value">{{ exchangeNum }}次</span>-->
<!--          </div>-->
<!--          <div class="goods-num" v-if="exchangeLimit === 2">-->
<!--            当日剩余兑换次数：<span :style="furnishStyles.exchangeNumTextColor.value">{{ exchangeNum }}次</span>-->
<!--          </div>-->
          <div class="goods-num">
            奖品总剩余：<span :style="furnishStyles.exchangeNumTextColor.value">{{ exchangeQuantityRemain ? exchangeQuantityRemain : 0 }}份</span>
          </div>
        </div>
        <div class="exchange-limit">
          <span class="red-bk">{{ exchangeLimitTips }}</span
          ><span v-if="sameTermOnce">且同期内所有活动仅限参加{{ sameTermTimes }}个</span>
        </div>
        <div class="exchange-address-big-box" v-show="prizeType === 3">
          <div class="choose-address-title">送至</div>
          <div class="choose-address-btn" @click="toast">
            <span>点击新建收货地址</span>
          </div>
          <van-icon name="arrow" color="#a7a7a7" />
        </div>
      </div>
    </div>
    <!-- 指定商品区域 -->
    <div class="sku" v-if="orderSkuisExposure !== 0">
      <div :style="furnishStyles.ruleTitleBox.value">指定商品:</div>
        <div class="sku-list">
          <div class="sku-item" v-for="(item,index) in orderSkuListPreview" :key="index">
            <img :src="item.skuMainPicture" alt="">
            <div class="sku-text">{{item.skuName}}</div>
            <div class="sku-btns" @click="gotoSkuPage(item.skuId)">
            </div>
          </div>
          <div class="more-btn-all">
            <div class="more-btn" v-if="orderSkuListPreview.length && orderSkuListPreview.length !== total" @click="toast">点我加载更多</div>
          </div>
        </div>
    </div>
    <!-- 兑换规则区域 -->
    <div class="rules-box">
      <div class="title-box title-box-rule" :style="furnishStyles.ruleTitleBox.value">兑换规则:</div>
      <div class="rules-line" :style="furnishStyles.ruleContentBox.value">
        {{ exchangeRule }}
      </div>
    </div>
    <!-- 兑奖按钮区域 -->
    <div class="exchange-btn-box">
      <img class="exchange-record-btn" :src="furnish.recordImg" alt="" @click="toast" />
      <div class="exchange-btn" :style="furnishStyles.exchangeImg.value" @click="toast">立即兑换</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import { gotoSkuPage } from '@/utils/platforms/jump';

const { registerHandler } = usePostMessage();
const activityData = inject('activityData') as any;
const isLoadingFinish = ref(false);
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const exchangeNum = ref(1); // 兑换份数
const shopName = ref('xxx旗舰店');
const userPoints = ref('1024');
const showRule = ref(false);
const exchangeGoodsName = ref('XXX积分兑换商品'); // 兑换商品名称
const exchangeGoodsImg = ref('https://img14.360buyimg.com/n0/jfs/t1/159566/35/8370/457630/6034cec1E91052ee4/fd66abd6e516b034.jpg'); // 兑换商品图片
const exchangeConditions = ref(['一星会员', '二星会员', '三星会员', '四星会员', '五星会员', '关注店铺用户']); // 兑换条件
const exchangeQuantityRemain = ref(0); // 兑换剩余数量
const unitCount = ref(1); // 单位个数
const exchangePoint = ref(1); // 兑换积分
const exchangePointTotal = ref(1); // 兑换总积分
const exchangePrice = ref(' xx'); // 兑换价格
const exchangeLimit = ref(1); // 兑换限制 0不限制 1限制次数 2限制每日次数
const exchangeLimitTips = ref('活动期内限领取1次');
const sameTermTimes = ref(1);
const sameTermOnce = ref(false); // 同期内所有奖品是否限兑换1个
const exchangeRule = ref(`1.活动时间：xxxx-xx-xx xx:xx:xx至xxxx-xx-xx xx:xx:xx。
2.活动对象：店铺会员（一星会员，二星会员，三星会员，四星会员，五星会员）、关注店铺用户。
3.参与规则：仅活动对象可参与活动。
4.兑换说明：xx京豆，共xxx份，每日可兑最大份数：不限；可兑换数量不代表实际兑换数量，实际兑换数量以兑换成功数量为准
5.兑换所需积分：每次兑换单份兑换品需消耗xxx积分
6.兑换限制：活动期内不限制
7.【活动参与主体资格】
（1）每位自然人用户仅能使用一个京东账号参与活动，WX号、QQ、京东账号、手机号码等任一信息一致或指向同一用户的，视为同一个用户，则第一个参与本活动的账号参与结果有效，其余账号参与结果均视为无效。
（2）若发现同一位用户使用不同账号重复参与活动，承办方有权取消其参与资格。
8.【注意事项】
（1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利。
（2）如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的)，商家有权取消、修改或暂停本活动。
（3）是否获得优惠以活动发布者后台统计结果为准。
（4）因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新。
（5）法律允许范围内，本活动最终解释权归商家所有。
（6）活动商品数量有限，先到先得。
（7）兑换品兑换成功后，积分不予退回。`); // 兑换规则
const prizeType = ref(3); // 奖品类型 默认京豆
const orderSkuisExposure = ref(0);
const orderSkuListPreview = ref([]);
const total = ref(0);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  window.top?.postMessage({
    from: 'C',
    type: 'deco',
    event: 'changeSelect',
    data: id,
  });
  selectedId.value = id;
};

const toast = () => {
  showToast('活动预览，仅供查看');
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};
const setDataInfo = (data: any) => {
  // 数据赋值
  exchangeConditions.value = data.gradeLabel;
  const newExchangePoint = data.exchangePoint[0];
  exchangeGoodsName.value = data.prizeInfo?.prizeName ?? 'XXX积分兑换商品';
  exchangeGoodsImg.value = data.prizeInfo?.prizeImg ?? 'https://img14.360buyimg.com/n0/jfs/t1/159566/35/8370/457630/6034cec1E91052ee4/fd66abd6e516b034.jpg';
  exchangePoint.value = newExchangePoint || 'x';
  exchangeQuantityRemain.value = data.prizeInfo?.sendTotalCount;
  unitCount.value = data.prizeInfo?.unitCount;
  prizeType.value = data.prizeInfo?.prizeType;
  sameTermOnce.value = data.sameTermOnce;
  sameTermTimes.value = data.sameTermTimes;
  exchangeLimit.value = data.exchangeLimit;
  exchangeNum.value = data.exchangeNum;
  exchangeRule.value = data.rules;
  orderSkuisExposure.value = data.orderSkuisExposure;
  orderSkuListPreview.value = data.orderSkuListPreview;
  if (data.exchangeLimit === 0) {
    exchangeLimitTips.value = '活动期内不限制';
  }
  if (data.exchangeLimit === 1) {
    exchangeLimitTips.value = `活动期限内可兑换${data.exchangeNum ?? ''}次`;
  }
  if (data.exchangeLimit === 2) {
    exchangeLimitTips.value = `活动期内每日可兑换${data.exchangeNum ?? ''}次`;
  }
};
// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    setDataInfo(data);
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'task') {
    showRule.value = false;
  } else if (type === 'shop') {
    shopName.value = data;
  }
};
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage({
    from: 'C',
    type: 'mounted',
    event: 'sendMounted',
    data: true,
  });
  if (activityData) {
    setDataInfo(activityData);
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
img[src=''],
img:not([src]) {
  opacity: 0;
}
.bg {
  padding-bottom: 1rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}
.kv-box {
  width: 7.5rem;
  position: relative;
  .shop-name-text {
    position: absolute;
    top: 0.26rem;
    left: 0.3rem;
    font-family: PingFang-SC-Bold;
    font-size: 0.24rem;
  }
  .user-points {
    position: absolute;
    top: 4.85rem;
    left: 0;
    right: 0;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 0 1.9rem;
    .item {
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/154332/24/29826/2044/66c40543Ff63f56ca/e484d216d4f568ec.png') no-repeat;
      background-size: 100%;
      background-position-y: center;
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 0.71rem;
      height: 1rem;
      color: #f7e7d1;
      width: 0.7rem;
      font-size: 0.7rem;
    }
  }
}
.content {
  width: 6.9rem;
  padding: 1rem 0.22rem 0.35rem;
  margin: 0 auto 0;
  background-repeat: no-repeat;
  background-size: 100% 1rem, 100% 0.5rem, 100% calc(100% - 1.5rem);
  background-position-y: top, bottom, 1rem;
  .block {
    position: relative;
    width: 100%;
    background-color: #ffffff;
    border-radius: 0.2rem;
    padding: 0.7rem 0.33rem 0.2rem;
    overflow: hidden;
    .goods-info {
      display: flex;
      align-items: center;
      .goods-img {
        width: 1.98rem;
        height: 1.98rem;
        object-fit: cover;
        border-radius: 50%;
      }
      .info {
        padding-left: 0.25rem;
        .name {
          font-size: 0.36rem;
        }
        .points {
          font-size: 0.3rem;
        }
      }
    }
    .goods-num {
      text-align: right;
      font-size: 0.24rem;
      span {
        font-size: 0.3rem;
      }
    }
    .exchange-address-big-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.2rem;
      height: 0.6rem;
      border-top: solid 0.03rem #dcdcdc;
      .choose-address-title {
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #333333;
      }
      .choose-address-btn {
        flex: 1;
        overflow: hidden;
        text-align: right;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-family: PingFang-SC-Medium;
        font-size: 0.24rem;
        color: #a7a7a7;
      }
    }
    .exchange-limit {
      position: absolute;
      top: 0;
      left: 0;
      border-bottom-right-radius: 0.2rem;
      height: 0.45rem;
      line-height: 0.45rem;
      background-color: #48341f;
      color: #fff;
      font-size: 0.2rem;
      span {
        padding: 0 0.1rem;
      }
      .red-bk {
        display: inline-block;
        height: 0.45rem;
        line-height: 0.45rem;
        border-bottom-right-radius: 0.2rem;
        background-color: #d73b35;
      }
    }
  }
}
.rules-box {
  margin-bottom: 0.2rem;
  padding: 0.3rem;
  width: 7.5rem;
  min-height: 2.4rem;
  .rules-line {
    margin-top: 0.3rem;
    font-family: PingFang-SC-Medium;
    font-size: 0.24rem;
    color: #666666;
    white-space: pre-wrap;
  }
}
.sku{
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.5rem 0.3rem 0.2rem;
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    //margin: 0.2rem auto 0.1rem auto;
    width: 6.9rem;
    margin: 0.2rem auto 0;
    place-content: flex-start space-between;
    padding: 0.2rem;
    background: rgb(254, 231, 201);
    border-radius: 0.4rem 0 0.05rem 0.05rem;
    max-height: 11rem;
    position: relative;
    overflow-y: scroll;
  }
  .sku-item{
    width: 3.15rem;
    margin-bottom: 0.1rem;
    background: rgb(255, 255, 255);
    border-radius: 0.2rem;
    overflow: hidden;
    img{
      display: block;
      width: 3.4rem;
      height: 3.4rem;
    }
    .sku-text{
      width: 3.2rem;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-height: 0.4rem;
      overflow: hidden;
      font-size: 0.27rem;
      color: #262626;
      height: 0.8rem;
      padding: 0 0.2rem;
      margin: 0.2rem 0 0.2rem 0;
      box-sizing: border-box;
    }
    .sku-btns{
      width: 2.56rem;
      height: 0.6rem;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 0.14rem auto;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/32236/20/17598/3026/6307406bE3b87a669/355ec23696288825.png);
    }
  }
}
.exchange-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  width: 100vw;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  font-family: PingFang-SC-Medium;
  font-size: 0.3rem;
  cursor: pointer;
  .exchange-btn {
    width: 5.1rem;
    height: 1rem;
    background-size: 100% 100%;
    font-size: 0.52rem;
    font-weight: bold;
    font-style: italic;
  }
  .exchange-record-btn {
    width: 2.4rem;
    height: 1rem;
  }
}
.more-btn-all {
  width:6.9rem;
  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0.3rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
