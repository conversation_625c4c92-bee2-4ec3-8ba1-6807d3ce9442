import { computed, reactive } from 'vue';

export const furnish = reactive({
  // 奖品背景图
  actBg: '',
  // 页面背景颜色
  actBgColor: '',
  showPrizeImg: 1,
  // 奖品信息背景图
  prizeInfo1: '',
  prizeInfo2: '',
  prizeInfo3: '',
  // 兑换品名称文字是否显示
  prizeNameShow: true,
  // 兑换品名称文字颜色
  prizeNameTextColor: '',
  // 积分文字颜色
  integralTextColor: '',
  // 兑换次数文字颜色
  exchangeNumTextColor: '',
  // 兑换条件背景颜色
  exchangeConditionsBgColor: '',
  // 兑换条件文字颜色
  exchangeConditionsTextColor: '',
  // 兑换记录图片
  recordImg: '',
  // 兑换按钮图片
  exchangeImg: '',
  // 兑换按钮文字颜色
  exchangeBtnTextColor: '',
  ruleTitleBoxColor: '',
  ruleContentBoxColor: '',
  // 店铺名称颜色
  shopNameColor: '',
  disableShopName: 0,
});
// 页面背景颜色
const actBgColor = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));
// 奖品背景图
const actBg = computed(() => ({
  backgroundImage: furnish.actBg ? `url(${furnish.actBg})` : '',
  width: '6.9rem',
  height: '2.4rem',
}));
// 奖品背景图
const actBgURL = computed(() => ({
  backgroundImage: furnish.actBg ? furnish.actBg : '',
}));
// 奖品信息背景图
const prizeInfoBg = computed(() => ({
  backgroundImage: furnish.prizeInfo1 ? `url(${furnish.prizeInfo1}), url(${furnish.prizeInfo3}), url(${furnish.prizeInfo2})` : '',
}));
const exchangeConsumeBg = computed(() => ({
  display: furnish.prizeNameShow ? '' : 'none',
  backgroundColor: furnish.prizeNameShow ? furnish.prizeNameTextColor : '',
}));
const exchangeConsumeBox = computed(() => ({
  backgroundImage: furnish.actBg ? `url(${furnish.actBg})` : '',
}));
// 兑换品名称文字颜色
const prizeNameTextColor = computed(() => ({
  display: furnish.prizeNameShow ? '' : 'none',
  color: furnish.prizeNameShow ? furnish.prizeNameTextColor : '',
}));
// 积分文字颜色
const integralTextColor = computed(() => ({
  color: furnish.integralTextColor ?? '',
}));
const exchangeNumTextColor = computed(() => ({
  color: furnish.exchangeNumTextColor ?? '',
}));
// 兑换条件颜色
const exchangeConditions = computed(() => ({
  backgroundColor: furnish.exchangeConditionsBgColor ?? '',
  color: furnish.exchangeConditionsTextColor ?? '',
}));
const ruleTitleBox = computed(() => ({
  color: furnish.ruleTitleBoxColor ?? '',
}));

const ruleContentBox = computed(() => ({
  color: furnish.ruleContentBoxColor ?? '',
}));

const exchangeImg = computed(() => ({
  backgroundImage: furnish.exchangeImg ? `url(${furnish.exchangeImg})` : '',
  color: furnish.exchangeBtnTextColor ?? '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));
export default {
  actBgColor,
  actBg,
  actBgURL,
  prizeInfoBg,
  prizeNameTextColor,
  integralTextColor,
  exchangeNumTextColor,
  exchangeConditions,
  exchangeConsumeBox,
  exchangeConsumeBg,
  ruleTitleBox,
  ruleContentBox,
  exchangeImg,
  shopNameColor,
};
