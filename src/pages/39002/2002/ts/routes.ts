import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/', // 主页
    name: 'index',
    component: () => import('../views/index.vue'),
  },
  {
    path: '/record', // 兑换记录
    name: 'record',
    component: () => import('../views/ExchangeRecord.vue'),
  },
  {
    path: '/detail', // 实物地址详情
    name: 'detail',
    component: () => import('../views/ExchangeDetail.vue'),
  },
  {
    path: '/address/list', // 实物选择地址列表
    name: 'addressList',
    component: () => import('../views/AddressList.vue'),
  },
  {
    path: '/edit/address', // 编辑地址
    name: 'editAddress',
    component: () => import('../views/EditAddress.vue'),
  },
  {
    path: '/send/detail', // 实物发货详情
    name: 'sendDetail',
    component: () => import('../views/SendDetail.vue'),
  },
  {
    path: '/gift/card/detail', // 礼品卡详情
    name: 'giftCardDetail',
    component: () => import('../views/GiftCardDetail.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
    name: 'notFound',
    hidden: true,
  },
];

const paramsObj: any = {};
// 路由
function initializeRouter() {
  // 从链接中移出shopId和activityId
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search || url.hash.split('?')[1] || '');
  // 删除字段
  params.delete('mockCode');
  params.delete('token');
  params.delete('source');
  params.delete('fromType');
  // params转object
  params.forEach((value, key) => {
    paramsObj[key] = value;
  });
  window.history.replaceState(null, '', `${url.pathname}${url.hash.split('?')[0]}?${params.toString()}`);

  const router = createRouter({
    history: createWebHashHistory(`${process.env.VUE_APP_PATH_PREFIX_NO_CDN}39002/2002/`),
    routes,
  });
  router.beforeEach((to, from, next) => {
    if (!to.query.shopId || !to.query.activityId) {
      const query = {
        ...to.query,
        ...paramsObj,
      };
      next({ ...to, query });
    } else {
      next();
    }
  });
  return router;
}

export default initializeRouter;
