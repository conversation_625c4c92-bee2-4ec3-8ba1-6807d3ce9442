<template>
  <div class="count-down-time" :style="furnishStyles.cutDownColor.value">
    <span class="title" v-if="props.isStart">距活动结束剩余：</span>
    <span class="title" v-else>距活动开始剩余：</span>
    <van-count-down :time="props.isStart ? props.endTime - new Date().getTime() : props.startTime - new Date().getTime()" format="DD:HH:mm:ss">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle">{{ timeData.days }}</div>
          <span>天</span>
          <div class="acblockStyleStyle">{{ timeData.hours }}</div>
          <span>时</span>
          <div class="acblockStyleStyle">{{ timeData.minutes }}</div>
          <span>分</span>
          <div class="acblockStyleStyle">{{ timeData.seconds }}</div>
          <span>秒</span>
        </div>
      </template>
    </van-count-down>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';

const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
});
</script>

<style scoped lang="scss">
.count-down-time {
  width: 6.9rem;
  height: 0.78rem;
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  align-items: center;
  padding-left: 0.74rem;
  //padding-top:0.1rem;
  //color: #f2270c;
  .title {
    font-size: 0.22rem;
    line-height: 0.22rem;
  }
  .contentSpan {
    margin-left: 0.89rem;
    display: flex;
    color: #ffffff;
    .acblockStyleStyle {
      width: 0.4rem;
      height: 0.44rem;
      color: #f2270c;
      border-radius: 0.05rem;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
      border: 0.02rem solid #fff;
      background-color: #ffffff;
    }
    span {
      width: 0.4rem;
      height: 0.44rem;
      color: #fff;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
