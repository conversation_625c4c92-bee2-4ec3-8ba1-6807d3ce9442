<template>
  <div class="rule-bk">
    <div class="content" v-html="rule"></div>
    <div class="close" @click="close"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('../assets/ruleBk.png');
  background-size: 100% 100%;
  width: 6.15rem;
  height: 7.79rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.1rem;
  padding-left: 0.3rem;
  padding-right: 0.3rem;

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
  }

  .content {
    height: 5.6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #814534;
    padding: 0 0.15rem;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
