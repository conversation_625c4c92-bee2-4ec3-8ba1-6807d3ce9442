<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="kv">
      <div class="btn-list">
        <img :src="furnish.ruleBtn" v-click-track="'hdgz'" alt="" @click="showRulePopup" />
        <img :src="furnish.myPrizeBtn" alt="" @click="myPrizePopup = true" />
        <img :src="furnish.orderBtn" alt="" @click="orderRecordPopup = true" />
      </div>
    </div>
    <div class="series-list" v-for="(item, index) in seriesList" :key="index">
      <!--      <div class="series-title" :style="furnishStyles.seriesTitleColor.value">{{ item.seriesName }}满额礼</div>-->
      <div class="user-info" :style="furnishStyles.userInfoBg.value">
        <div class="user-name">{{ userInfo.nickname }}</div>
        <img alt="" :src="userInfo.avatar || 'https://img10.360buyimg.com/imgzone/jfs/t20782/85/568972117/2758/78eafc28/5b0fd507Ne2b074cc.png'" />
        <div class="buy-amount" :style="furnishStyles.bugAmountColor.value">
          您当前拥有<span style="color: red">{{ item.tankNum || 0 }}</span
          >罐
        </div>
      </div>
      <div class="thresholdBg" :style="furnishStyles.thresholdBg.value">
        <div class="threshold-co">
          <div class="threshold-list">
            <div class="step" :style="getIsAct(actStep[index] ?? 0, idx) ? furnishStyles.stepBtnSelectBg.value : furnishStyles.stepBtnBg.value" @click="changeActStep(index, idx)" v-for="(step, idx) in item.stepList" :key="idx">{{ step.tankNum }}罐可兑</div>
          </div>
          <div class="step-pagination">
            <div v-for="(step, idx) in item.stepList" :key="idx" class="pagination-item" :class="{ 'pagination-act': getIsAct(actStep[index] ?? 0, idx) }"></div>
          </div>
        </div>
      </div>
      <div class="prize-list-bg" :style="furnishStyles.prizeListBg.value">
        <div class="prize-list-title">满{{ item.stepList[actStep[index] ?? 0].tankNum }}罐可兑{{ item.perReceiveCount > 1 ? '礼品任选其一' : '' }}</div>
        <div class="prize-content">
          <div class="prize-list swiper-container">
            <div class="swiper-wrapper">
              <div class="swiper-slide" v-for="(it, idx) in item.stepList[actStep[index] ?? 0].prizeList" :key="`${it.prizeKey}${idx}`">
                <div class="prize-item" :style="furnishStyles.prizeBg.value">
                  <img :src="it.prizeImg" alt="" class="prize-img" />
                  <div class="prize-name">{{ it.prizeName }}</div>
                  <div class="prize-surplus">剩余{{ getSurplus(it.sendTotalCount, it.sendCount) }}份</div>
                  <img :src="furnish.exchangeBtn" alt="" class="exchange-btn" :class="{ gray: it.status !== 1 }" @click="exchangePrize(it)" />
                </div>
              </div>
            </div>
            <div class="swiper-pagination"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="sku-content" :style="furnishStyles.skuListBg.value" v-if="baseInfo.activityMainId !== '1899350131974918146'">
      <!-- <div class="step-sc">
        <div class="step-list">
          <div class="step" :style="getSkuIsAct(skuActStep, idx) ? furnishStyles.stepBtnSelectBg.value : furnishStyles.stepBtnBg.value" @click="changeSkuActStep(idx)" v-for="(item, idx) in seriesList" :key="idx">
            <svg class="step-svg" width="1.84rem" height="0.75rem">
              <text x="50%" y="55%" text-anchor="middle" :style="furnishStyles.stepText.value">
                {{ item.seriesName.replace(/-/g, '') }}
              </text>
            </svg>
          </div>
        </div>
      </div> -->
      <div class="sku-sc">
        <div class="sku-list">
          <div class="sku-item" v-for="(item, index) in showSkuList" :key="index" :style="furnishStyles.skuBg.value">
            <img :src="item.skuMainPicture" alt="" class="sku-img" />
            <div class="sku-text" :style="furnishStyles.skuTitleBg.value">{{ item.skuName }}</div>
            <img :src="furnish.goSkuBtn" alt="" class="btn" @click="gotoSkuPage(item.skuId)" />
          </div>
          <div class="more-btn" v-if="showSkuList.length < pageInfo.pagesAll" @click="loadMore">点我加载更多</div>
        </div>
      </div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="rulePopup">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="myPrizePopup">
    <MyPrize v-if="myPrizePopup" @close="myPrizePopup = false" @showCardNum="showCardNum"></MyPrize>
  </VanPopup>
  <!--我的订单弹窗-->
  <VanPopup teleport="body" v-model:show="orderRecordPopup">
    <OrderRecordPopup v-if="orderRecordPopup" @close="orderRecordPopup = false"></OrderRecordPopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="saveAddressPopup" :closeOnClickOverlay="false">
    <SaveAddress v-if="saveAddressPopup" @close="saveAddressPopup = false" :userPrizeId="awardPrize.userPrizeId"></SaveAddress>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard @close="copyCardPopup = false" :detail="cardDetail"></CopyCard>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="awardPopup">
    <Award v-if="awardPopup" @close="awardPopup = false" :prize="awardPrize" @saveAddress="toSaveAddress" @showCardNum="showCardNum"></Award>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="confirmPopup">
    <ConfirmPopup @close="confirmPopup = false" :prizeInfo="confirmPrizeInfo" @confirm="confirmExchangePrize"></ConfirmPopup>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="joinPopup" :closeOnClickOverlay="false">
    <OpenCard @close="joinPopup = false"></OpenCard>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import furnishStyles, { furnish, taskRequestInfo } from './ts/furnishStyles';
import Swiper, { Pagination } from 'swiper';
import 'swiper/swiper.min.css';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';
import MyPrize from './components/MyPrize.vue';
import Rule from './components/Rule.vue';
import Award from './components/AwardPopup.vue';
import SaveAddress from './components/SaveAddress.vue';
import OrderRecordPopup from './components/OrderRecordPopup.vue';
import OpenCard from './components/OpenCard.vue';
import CopyCard from './components/CopyCard.vue';
import ConfirmPopup from './components/ConfirmPopup.vue';
import { UserInfo } from '@/utils/products/types/UserInfo';

Swiper.use([Pagination]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const userInfo = inject('userInfo') as UserInfo;

const isLoadingFinish = ref(false);
const rulePopup = ref(false);
const myPrizePopup = ref(false);
const orderRecordPopup = ref(false);
const saveAddressPopup = ref(false);
const awardPopup = ref(false);
const joinPopup = ref(false);

// 展示卡号卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  prizeImg: '',
});

// 展示活动规则，首次获取规则
const ruleText = ref('');
const showRulePopup = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleText.value = data;
    }
    rulePopup.value = true;
  } catch (error: any) {
    console.error();
  }
};

const seriesList = ref<any[]>([]);
const showSkuList = ref<any[]>([]);
const pageInfo = reactive({
  pageNum: 1,
  pageSize: 10,
  pagesAll: 0,
});

const actStep = ref<number[]>([]);

// 获取剩余份数
const getSurplus = (sendTotalCount: number, sendCount: number) => {
  const surplus = (sendTotalCount ?? 0) - (sendCount ?? 0);
  if (surplus <= 0) return 0;
  return surplus;
};

const swiperList: Swiper[] = [];
const initSwiper = () => {
  swiperList.forEach((item) => {
    item.destroy();
  });
  swiperList.length = 0;
  nextTick(() => {
    const prizeList = document.querySelectorAll('.prize-list');
    prizeList.forEach((item) => {
      // 取item的子元素swiper-pagination
      const swiperPagination = item.querySelector('.swiper-pagination');
      swiperList.push(
        new Swiper(item, {
          observeSlideChildren: true,
          observer: true,
          pagination: {
            el: swiperPagination,
          },
        }),
      );
    });
  });
};

const getIsAct = (actStepIndex: number, idx: number) => actStepIndex === idx;

const changeActStep = (index: number, idx: number) => {
  actStep.value[index] = idx;
};

const skuActStep = ref(0);
const getSkuIsAct = (skuAct: number, idx: number) => skuAct === idx;

const checkMember = () => {
  if (baseInfo.status === 1) {
    showToast('活动未开始');
    return false;
  }
  if (baseInfo.status === 3) {
    showToast('活动已结束');
    return false;
  }
  if (baseInfo.memberLevel <= 0) {
    joinPopup.value = true;
    return false;
  }
  return true;
};

// 获取曝光商品
const getExposureSku = async (seriesId: string) => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90202/getSkuListPage', {
      seriesId,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    });
    closeToast();
    showSkuList.value.push(...res.data.records);
    pageInfo.pagesAll = res.data.total;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
const loadMore = async () => {
  pageInfo.pageNum++;
  const { seriesId } = seriesList.value[skuActStep.value];
  await getExposureSku(seriesId);
};

const changeSkuActStep = async (idx: number) => {
  if (skuActStep.value === idx) return;
  showSkuList.value = [];
  pageInfo.pageNum = 1;
  const { seriesId } = seriesList.value[idx];
  getExposureSku(seriesId);
  skuActStep.value = idx;
};

const getSeriesPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/90202/getSeriesPrizes');
    if (actStep.value.length !== data.length) {
      actStep.value.length = data.length;
      actStep.value.fill(0);
    }
    seriesList.value = data;
  } catch (error: any) {
    console.error(error);
  }
};

const awardPrize = ref({
  prizeType: 3,
  prizeName: '11',
  prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/231005/7/25170/16188/66c6b459F39423e29/4f23f94c0f41fc7f.png',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const confirmPopup = ref(false);
const confirmPrizeInfo = ref<any>({});
// 兑换奖品
const exchangePrize = async (item: any) => {
  if (!checkMember()) return;
  // 状态 1 可以领取 2 已领取  3 不可领取 4 领取上线
  if (item.status === 2) {
    showToast('您已兑换过该奖品，请到兑换记录查看您的好礼');
    return;
  }
  if (item.status === 3) {
    showToast('你未达到兑换条件');
    return;
  }
  if (item.status === 4) {
    showToast('你的领取已达上限，请到兑换记录查看您的好礼');
    return;
  }
  confirmPrizeInfo.value = item;
  confirmPopup.value = true;
};

// 确认兑换奖品
const confirmExchangePrize = async () => {
  try {
    showLoadingToast({
      message: '兑换中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/90202/receivePrize', {
      prizeId: confirmPrizeInfo.value.prizeId,
    });
    confirmPopup.value = false;
    awardPrize.value = data;
    awardPopup.value = true;
    closeToast();
    getSeriesPrizes();
  } catch (error: any) {
    console.error(error);
    showToast(error.message);
  }
};

const toSaveAddress = () => {
  saveAddressPopup.value = true;
  awardPopup.value = false;
};

const showCardNum = (result: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  awardPopup.value = false;
  copyCardPopup.value = true;
};

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await getSeriesPrizes();
    await getExposureSku(seriesList.value[0].seriesId);
    isLoadingFinish.value = true;
    initSwiper();
    closeToast();
    checkMember();
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};
init();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding-bottom: 0.35rem;
  .kv {
    height: 6.2rem;
    display: flex;
    justify-content: flex-end;
    padding-top: 0.2rem;
    .btn-list img {
      width: 1.1rem;
    }
  }
  .series-list {
    position: relative;
    width: 6.8rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    .series-title {
      font-size: 0.4rem;
      text-align: center;
      padding-top: 0.1rem;
      line-height: 0.9rem;
      font-weight: bold;
      margin-bottom: 0.4rem;
    }
    //.user-info {
    //  display: flex;
    //  align-items: center;
    //  padding: 0 0.81rem;
    //  height: 1.52rem;
    //  margin-bottom: 0.25rem;
    //  img {
    //    width: 1.13rem;
    //    height: 1.13rem;
    //    border-radius: 50%;
    //    background-color: #fff;
    //    margin-left: 0.2rem;
    //  }
    //  .buy-amount {
    //    font-size: 0.285rem;
    //    color: #fff;
    //    margin-left: 0.2rem;
    //  }
    //}
    .user-info {
      //background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/252918/37/22664/42952/67b6c869F855546ad/54e323eb5bf6c8d2.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 6.9rem;
      height: 1.8rem;
      margin: 0 auto;
      position: relative;
      color: #97653c;
      font-weight: 600;
      .user-name {
        font-size: 0.3rem;
        position: absolute;
        left: 1.5rem;
        top: 0.25rem;
      }
      img {
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background-color: #fff;
        position: absolute;
        left: 0.2rem;
        top: 0.2rem;
      }
      .buy-amount {
        font-size: 0.285rem;
        position: absolute;
        left: 1.5rem;
        top: 0.7rem;
      }
    }
    .thresholdBg {
      //background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/263056/17/22232/95667/67b6c86bF8e156c3b/05b13ed2f6e4eeac.png');
      background-size: 100%;
      background-repeat: no-repeat;
      width: 7.06rem;
      height: 2.6rem;
      .threshold-co {
        width: 6.06rem;
        height: 2.6rem;
        margin: 0 auto;
        overflow-x: auto;
        overflow-y: hidden;
      }
      .threshold-list {
        min-width: 6.06rem;
        width: fit-content;
        padding-top: 1.1rem;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        gap: 0.3rem;
      }
      .step-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 0.1rem;
        .pagination-item {
          width: 0.1rem;
          height: 0.1rem;
          border-radius: 50%;
          background: #eeca71;
          margin: 0 0.1rem;
        }
        .pagination-act {
          background: #e4aa1e;
        }
      }
    }
    .prize-list-bg {
      width: 7.08rem;
      height: 4.64rem;
      //background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/259850/19/21830/135972/67b6c86bFe24ddd6d/73efdd3021a0e2a8.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      padding-top: 1.2rem;
      position: relative;
      .prize-list-title {
        width: 5rem;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 0.2rem;
        color: #784129;
        font-size: 0.3rem;
      }
      .prize-content {
        width: 6.4rem;
        overflow: hidden;
        margin: 0 auto;
        .prize-list {
          width: 5.18rem;
          margin: 0 auto;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          .prize-item {
            position: relative;
            width: 5.18rem;
            height: 2.66rem;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            .prize-img {
              width: 1.6rem;
              height: 1.6rem;
              border-radius: 0.2rem;
              position: absolute;
              left: 0.5rem;
              top: 0.7rem;
            }
            .prize-name {
              font-size: 0.3rem;
              color: #7f3d01;
              font-weight: bold;
              position: absolute;
              left: 2.6rem;
              top: 0.7rem;
            }
            .prize-surplus {
              color: #fff;
              position: absolute;
              left: 0.1rem;
              top: 0.15rem;
              font-size: 0.26rem;
              width: 1.96rem;
              height: 0.45rem;
              line-height: 0.45rem;
              text-align: center;
            }
            .exchange-btn {
              position: absolute;
              bottom: 0.4rem;
              left: 2.8rem;
              width: 2.06rem;
              height: 0.48rem;
            }
          }
          .swiper-slide {
            transform: scale(0.9);
          }
          .swiper-slide-active {
            transform: scale(1);
          }
        }
      }
    }
    //.prize-list {
    //  width: 5.42rem;
    //  margin: 0 auto;
    //  overflow: hidden;
    //  background-size: 100%;
    //  background-repeat: no-repeat;
    //}
    //.prize-item {
    //  position: relative;
    //  width: 5.42rem;
    //  height: 2.95rem;
    //  background-size: 100%;
    //  background-repeat: no-repeat;
    //  display: flex;
    //  align-items: center;
    //  padding: 0 0.2rem 0.37rem;
    //  .prize-img {
    //    width: 2rem;
    //    height: 2rem;
    //    border-radius: 0.2rem;
    //  }
    //  .prize-info {
    //    flex: 1;
    //    padding-left: 0.2rem;
    //    .name {
    //      font-size: 0.27rem;
    //      color: #7f3d01;
    //      margin-bottom: 0.15rem;
    //      word-break: break-all;
    //      font-weight: bold;
    //    }
    //    .now-price {
    //      font-size: 0.24rem;
    //      color: #bd0401;
    //      font-weight: bold;
    //      margin-bottom: 0.1rem;
    //    }
    //    .price {
    //      font-size: 0.21rem;
    //      color: #7f3d01;
    //      // 中间线
    //      text-decoration: line-through;
    //    }
    //    .surplus {
    //      font-size: 0.15rem;
    //      color: #d3a54f;
    //    }
    //  }
    //  .exchange-btn {
    //    position: absolute;
    //    bottom: 0;
    //    // 左右居中
    //    left: 50%;
    //    transform: translateX(-50%);
    //    width: 2.25rem;
    //  }
    //}
    //.pre {
    //  position: absolute;
    //  top: 5.5rem;
    //  left: 0;
    //  width: 0.3rem;
    //  height: 0.5rem;
    //}
  }
  .sku-content {
    position: relative;
    width: 6.8rem;
    height: 13.45rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0 auto;
    padding-top: 2rem;
    .step-sc {
      width: 6.2rem;
      margin: 0 auto;
      overflow-x: auto;
      overflow-y: hidden;
      .step-list {
        width: fit-content;
        min-width: 6.2rem;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        height: 1.15rem;
      }
    }
    .sku-sc {
      overflow-y: scroll;
      height: 11.1rem;
    }
    .sku-list {
      width: 6.2rem;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .sku-item {
        position: relative;
        width: 3rem;
        height: 3rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-bottom: 0.3rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .sku-img {
        width: 2.9rem;
        height: 2.9rem;
        border-radius: 0.3rem;
      }
      .sku-text {
        position: absolute;
        left: 0;
        top: 0;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 2.06rem;
        height: 0.48rem;
        white-space: nowrap;
        font-size: 0.18rem;
        line-height: 0.48rem;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-left: 0.05rem;
        padding-right: 0.25rem;
      }
      .btn {
        position: absolute;
        bottom: 0.15rem;
        left: 50%;
        transform: translateX(-50%);
        width: 2rem;
      }
      .more-btn {
        text-align: center;
        font-size: 0.2rem;
        color: #a86117;
        width: 100%;
      }
    }
  }
}
.step {
  width: 2.1rem;
  height: 0.6rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  text-align: center;
  line-height: 0.6rem;
  font-size: 0.32rem;
  font-weight: bold;
  .step-svg {
    width: 100%;
    height: 100%;
    stroke-linejoin: round;
    font-weight: bold;
    vertical-align: middle;
  }
}
.samlFont {
  font-size: 0.22rem;
}
.gray {
  filter: grayscale(1);
}
</style>
<style lang="scss">
.swiper-pagination {
  width: 100%;
  margin-top: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  .swiper-pagination-bullet {
    display: block;
    width: 0.1rem;
    height: 0.1rem;
    border-radius: 50%;
    background: #eeca71;
    margin: 0 0.05rem;
  }
  .swiper-pagination-bullet-active {
    background: #e4aa1e;
  }
}
</style>
