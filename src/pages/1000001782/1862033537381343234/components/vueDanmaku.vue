<template>
    <div class="winnerAllClass">
        <vueDanmaku v-if="winnerList.length !== 0" ref="danmaku" v-model:danmus="winnerList"
            :is-running="true" useSlot loop :channels="1" :speeds="30" class="danmaku">
            <template v-slot:dm="{ danmu }">
                <div class="danmaku-item" :style="{ transform: `translateY(${someOffsetFunction(danmu)})` }">
                    <span>{{ danmu.nickName }}中奖{{ danmu.prizeName }}</span>
                </div>
            </template>
        </vueDanmaku>
<!--        <div v-else-if="winnerList.length !== 0 && winnerList.length <= 2" class="danmaku1">-->
<!--            <div class="danmaku-item1" v-for="(danmu, index) in winnerList" :key="index">-->
<!--                <div class="danmaku-item" :style="{ transform: `translateY(${someOffsetFunction(danmu)})` }">-->
<!--                    <span>{{ danmu.nickName }}中奖{{ danmu.prizeName }}</span>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
    </div>
</template>

<script lang="ts" setup>
import { reactive, nextTick } from 'vue';
import vueDanmaku from 'vue3-danmaku';
import { configData } from '../common';

interface Winner {
  nickName: string;
  prizeName: string;
}
const winnerList = reactive([] as Winner[]);
const moduleName = 'allUserPrizes';
nextTick(() => {
  winnerList.splice(0);
  winnerList.push(...configData.value[moduleName]);
});
// 获取随机数
const getRandomInt = (min: number, max: number) => {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
};
// 获取弹幕的Y轴位置
const someOffsetFunction = (danmu: any) => {
  const num = `${getRandomInt(0, 15)}px`;
  return num;
};
</script>

<style lang="scss" scoped>
.winnerAllClass {
    padding-top: 0.92rem;
    height: 0.9rem;
    .danmaku {
        width: 5.8rem;
        height: 0.9rem;
        margin: 0 auto;

        .danmaku-item {
            display: flex;
            align-items: center;
            height: 0.36rem;
            background-color: #ffa5a7;
            border-radius: 0.471rem;
            padding-left: 0.12rem;
            padding-right: 0.12rem;
            color: #000000;
            font-size: 0.2rem;
            white-space: nowrap;
            /* 其他样式 */
            /* 使用 transform 实现错位 */
            transform: translateY(
                    /* 根据需要设置不同的值 */
                );
        }
    }
    .danmaku1{
        height: 1.70rem;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        .danmaku-item1{
            margin: auto 0.12rem;
        }
        .danmaku-item {
            display: flex;
            align-items: center;
            height: 0.36rem;
            background-color: #000000;
            border-radius: 0.471rem;
            padding-left: 0.12rem;
            padding-right: 0.12rem;
            color: #ffffff;
            font-size: 0.2rem;
            white-space: nowrap;
            // margin: 0 0.12rem;
            /* 其他样式 */
            /* 使用 transform 实现错位 */
            transform: translateY(
                    /* 根据需要设置不同的值 */
                );
        }
    }
}
</style>
