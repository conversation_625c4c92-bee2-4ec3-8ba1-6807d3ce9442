<template>
  <div class="rule-bk">
    <div class="title">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/118384/36/39281/7615/65016dfdFd53387b4/002c74c698bed156.png" alt="" class="text" />
      <div class="close" @click="close"></div>
    </div>
    <div class="content">
      <div class="form">
        <VanField v-model="phone" required label="电话：" maxlength="11" type="number" input-align="right"></VanField>
      </div>
      <div class="tip">
        <p class="tip-title">重要充值说明：</p>
        <div class="tip-text">{{ planDesc }}</div>
      </div>

      <img class="submit" src="//img10.360buyimg.com/imgzone/jfs/t1/163969/4/37763/12433/65016dfdFe093a5a5/919be000943e2c9b.png" alt="" @click="checkForm" />
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="confirmPopup">
    <div class="affiem-inner" @click.stop>
      <div class="affiem-text">{{ phone }}</div>
      <div class="affiem-hint">请再次确定号码填写无误</div>
      <div class="affiem-word">
        <div class="affiem-balck">重要提示：</div>
        <p>① 充值成功后 无法退换；</p>
        <p>② 切勿写错手机号，如充错，责任自行承担；</p>
        <p>③ 点击【确认提交】后，权益领取手机号会无法修改，请 确认无误后再点击确认。</p>
      </div>
      <div class="flex">
        <div class="affiem-btn" @click="confirmPopup = false"></div>
        <div class="affiem-btn" @click="submit"></div>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { showToast, showLoadingToast, closeToast } from 'vant';
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';

const props = defineProps({
  userReceiveRecordId: {
    type: String,
    required: true,
  },
  planDesc: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const phone = ref('');

// 二次确认弹窗
const confirmPopup = ref(false);

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    console.log(props.userReceiveRecordId, 'props.userReceiveRecordId');
    const res = await httpRequest.post('/80077/fuLuReceive', {
      userReceiveRecordId: props.userReceiveRecordId,
      phone: phone.value,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
      confirmPopup.value = false;
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入电话');
  } else if (!checkPhone.test(phone.value)) {
    showToast('请输入正确的电话');
  } else {
    // submit();
    confirmPopup.value = true;
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/222600/40/35224/219814/65016dfeF9234602d/d99de4f864849a24.png) no-repeat;
  background-size: 100%;
  width: 100vw;

  .title {
    position: relative;
    height: 0.86rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 0.33rem;
    .text {
      height: 0.6rem;
    }
  }

  .close {
    width: 0.55rem;
    height: 0.55rem;
  }

  .content {
    padding: 0 0.3rem 0.3rem 0.3rem;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .form {
      padding-top: 0.32rem;
      .van-cell {
        color: #262626;
        height: 0.94rem;
        align-items: center;
        font-size: 0.24rem;
        border-radius: 0.15rem;
        margin-bottom: 0.15rem;

        &::after {
          display: none;
        }
      }
    }
    .tip {
      margin-top: 0.17rem;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/91768/35/29697/7789/65017669F9162e0cd/574859d7bfe7c57b.png) no-repeat;
      background-size: 100%;
      padding: 0.35rem 0.3rem;
    }
    .tip-title {
      font-size: 0.25rem;
      color: #262626;
      margin-bottom: 0.18rem;
    }
    .tip-text {
      height: 1.9rem;
      overflow-y: scroll;
      font-size: 0.2rem;
      color: #b3b3b3;
    }

    .submit {
      width: 5.3rem;
      margin: 0.35rem auto 0;
    }
  }
}

// 二次确认弹窗
.affirm-box {
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiem-inner {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/171987/20/37000/88573/65017809F845cc209/d2ea61bb896bf621.png) no-repeat;
  background-size: 100%;
  width: 6rem;
  height: 7rem;
  min-height: 2rem;
  padding: 1.7rem 0.25rem 0;
}
.affiem-text {
  margin-top: 0.09rem;
  font-size: 0.44rem;
  text-align: center;
  color: #262626;
}
.affiem-hint {
  color: #262626;
  font-weight: 400;
  font-size: 0.26rem;
  text-align: center;
  line-height: 0.54rem;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.3rem;
  margin-top: 0.65rem;
}
.affiem-btn {
  width: 2.2rem;
  height: 0.6rem;
}
.m-r-37 {
  margin-right: 0.37rem;
}
.affiem-word {
  color: #262626;
  font-size: 0.24rem;
  margin-top: 0.2rem;
  padding: 0 0.2rem;
  line-height: 0.4rem;
}
.affiem-balck {
  color: #262626;
  line-height: 0.43rem;
}
</style>
