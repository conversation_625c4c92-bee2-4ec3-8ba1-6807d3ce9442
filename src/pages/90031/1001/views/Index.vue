<template>
  <div class="bg" :style="furnishStyles.pageBg.value" :class="{ select: showSelect }">
    <!-- {{furnish.privacyRule}} -->
    <div class="header-kv select-hover" :class="{ 'on-select': selectedId === 1 }">
      <div class="img-box">
        <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      </div>
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <!-- <div class="shop-name" :style="furnishStyles.shopNameColor.value">{{ shopName }}</div> -->
        <div>
          <div class="header-btn" @click="showRule = true"><div>活动<br>规则</div></div>
          <div class="header-btn" @click="showMyPrize = true"><div>我的<br>奖品</div></div>
        </div>
      </div>
    <!-- <div class="select-hover" :class="{ 'on-select': selectedId === 2 }" @click="onSelected(2)"> -->
      <div class="draws-num">还有{{chanceNum}}次抽奖机会</div>
      <div class="rollBall">
        <div class="machine">
        </div>
        <div class="machine2" v-if="endStatus">
          <img style="width: 100%;height: 100%;" :src="Gif" alt="">
        </div>
        <div class="prizes">
          <div class="topprizebg">
            <div class="swiper-container2" ref="swiperRef">
              <div class="swiper-wrapper">
                <div class="swiper-slide prize-box" v-for="(item,index) in newPrizeInfo" :key="index">
                  <img :src="item.prizeImg" alt="">
                  <div class="prize-name">{{item.prizeName}}</div>
                </div>
                <div class="swiper-slide prize-box" v-for="(item,index) in newPrizeInfo" :key="index">
                  <img :src="item.prizeImg" alt="">
                  <div class="prize-name">{{item.prizeName}}</div>
                </div>
                <div class="swiper-slide prize-box" v-for="(item,index) in newPrizeInfo" :key="index">
                  <img :src="item.prizeImg" alt="">
                  <div class="prize-name">{{item.prizeName}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="award" v-if="endStatus">
          <img :src="furnishStyles.params.ballList[awardIndex].src" alt="" />
        </div> -->
        <div class="content">
          <img src="https://img10.360buyimg.com/imgzone/jfs/t1/243534/18/11261/17569/6668136eF9ee7ec82/f3a04afd21489a7a.png" alt="" class="start" @click="startCallback" />
          <img src="https://img10.360buyimg.com/imgzone/jfs/t1/240552/33/10854/22419/666810d3Fbb555b00/0e7b82962a722a2e.png" alt="" class="small-big" />
        </div>
      </div>
      <DoTask :times="times" :tasks="tasks" :isDisTask="baseInfo.thresholdResponseList.length > 0" @showLimit="showUseThreshold" @refreshTask="refreshTask"></DoTask>
    </div>
    <div class="rule-box">
      <div class="content" v-html="ruleTest.replace(/\n/g, '<br/>')"></div>
    </div>
    <div class="footer-btn">
      <img class="item" @click="backTop()" src="//img10.360buyimg.com/imgzone/jfs/t1/3965/11/22293/9992/668e84feFfb759bd3/3fa76f703bac4491.png"/>
      <!-- <img class="item" @click="gotoShopPage(baseInfo.shopId);" src="//img10.360buyimg.com/imgzone/jfs/t1/237107/24/20138/10015/668e84feFf9591a54/9a6291ad76f9b352.png"/> -->
    </div>
  </div>
  <!-- 活动门槛 -->
  <Threshold2 v-model:show="showLimit" :data="baseInfo?.thresholdResponseList" />
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone"></MyPrize>
  </VanPopup>
  <!-- 做任务弹窗  -->
  <VanPopup teleport="body" v-model:show="showTask" position="bottom">
    <DoTask :times="chanceNum" :tasks="tasks" @close="showTask = false" :shopId="baseInfo.shopId" @refreshTask="refreshTask"></DoTask>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" :tipsText="tipsText" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :needId="pageneedId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false"></CopyCard>
  </VanPopup>
  <!-- 领取京元宝权益 -->
  <VanPopup teleport="body" v-model:show="savePhonePopup" position="bottom">
    <SavePhone v-if="savePhonePopup" :userPrizeId="activityPrizeId" :planDesc="planDesc" @close="savePhonePopup = false"></SavePhone>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, inject, computed, onMounted } from 'vue';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import DoTask from '../components/DoTask.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { Task, CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import SavePhone from '../components/SavePhone.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import Threshold2 from '../components/Threshold2/index.vue';
import useThreshold from '../ts/useThreshold';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { scrollToPosition } from '../ts/validator';
import dayjs from 'dayjs';
import '../style/niudanStyle.scss';

import { Handler } from '@/utils/handle';
import LzLuckyWheel from '@/components/LzLuckyDraw/LzLuckyWheel.vue';
import LzLuckyGrid from '@/components/LzLuckyDraw/LzLuckyGrid.vue';
import LzLuckyGacha from '@/components/LzLuckyDraw/LzLuckyGacha.vue';
import LzLuckyCurettage from '@/components/LzLuckyDraw/LzLuckyCurettage.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;

const shopName = ref(baseInfo.shopName);

// 展示门槛显示弹框
const showLimit = ref(false);
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
  className: 'common-message-niudan',
  startTime: dayjs(baseInfo.startTime).format('YYYY-MM-DD HH:mm:ss'),
  endTime: dayjs(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss'),
});

const showRule = ref(false);
const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};

// 抽奖次数
const chanceNum = ref(0);

const showMyPrize = ref(false);

const tasks = reactive([] as Task[]);
const showTask = ref(false);

const showAward = ref(false);
const award = ref({
  prizeType: 7,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});
const tipsText = ref('');

// 保存实物地址相关
const showSaveAddress = ref(false);
const activityPrizeId = ref('');
const addressId = ref('');
const pageneedId = ref(0);
const toSaveAddress = (id: string, prizeId: string, needId: number) => {
  console.log(needId);
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
  pageneedId.value = needId;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  showImg: '',
  remark: '',
});
const showCardNum = (result: CardType) => {
  console.log(result);
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
  showAward.value = false;
  copyCardPopup.value = true;
};

// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');
const showSavePhone = (id: string, desc: string) => {
  activityPrizeId.value = id;
  planDesc.value = desc;
  showAward.value = false;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityGiftRecords = reactive([] as ActivityGiftRecord[]);

// 获取客抽奖次数
const getChanceNum = async () => {
  try {
    const { data } = await httpRequest.post('/90031/chanceNum');
    chanceNum.value = data;
  } catch (error: any) {
    console.error(error);
  }
};

// 奖品
const newPrizeInfo = computed(() => prizeInfo.filter((item) => item.prizeName !== '谢谢参与'));
// 奖品
const initSwiper = () => {
  console.log(123);
  const prizeSwiper = new Swiper('.swiper-container2', {
    loop: true,
    autoplay: {
      disableOnInteraction: false,
      delay: 2000,
    },
    loopAdditionalSlides: 3,
    speed: 500,
    centeredSlides: true, // 选中slide居中显示
    initialSlide: 0, // 默认选中项索引
    slidesPerView: 3, // 自动匹配每次显示的slide个数,loop='auto'模式下，还需要设置loopedSlides
  });
};
// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/90031/getPrizes');
    prizeInfo.splice(0);
    prizeInfo.push(...data);
    initSwiper();
  } catch (error: any) {
    console.error(error);
  }
};

// 曝光商品
interface Sku {
  jdPrice: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
}

const skuList = ref<Sku[]>([]);

const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90031/getExposureSku');
    skuList.value = res.data;
    skuList.value.forEach((item) => {
      item.jdPrice /= 1000;
    });
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

const endStatus = ref(false);
const awardIndex = ref(0);
const Gif = ref('');
// 抽奖接口
const lotteryDraw = async () => {
  try {
    showLoadingToast({
      message: '',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90031/lotteryDraw');
    closeToast();
    console.log(res.data.result);
    const temp = [furnish.tips0, furnish.tips1, furnish.tips2, furnish.tips3, furnish.tips4, furnish.tips5];
    let tipsList = temp.filter((item) => item !== '' && (item.replace(/\s*/g, '') !== ''));
    if (tipsList.length === 0) {
      tipsList = ['更「多」好运留给下次', '期待与你「多见一次」', '祝你下「次」惊喜发生'];
    }
    console.log(tipsList);
    const size = tipsList.length;
    const index = Math.floor(Math.random() * size);
    console.log(index);
    tipsText.value = tipsList[index];
    if (res.data.prizeType) {
      award.value = {
        prizeType: res.data.prizeType,
        prizeName: res.data.prizeName,
        showImg: res.data.prizeImg,
        result: res.data.result ? res.data.result : '',
        remark: res.data.remark ? res.data.remark : '',
        activityPrizeId: res.data.activityPrizeId ?? '',
        needId: res.data.needId ? res.data.needId : 0,
        userPrizeId: res.data.userPrizeId,
      };
      const index = prizeInfo.findIndex((item) => item.index === res.data.sortId);
      Gif.value = `https://img10.360buyimg.com/imgzone/jfs/t1/224564/8/21683/573502/004dcc01Fd49b39f9/07b3227e7663e91a.gif?+${Math.random()}`;
      awardIndex.value = index;
      endStatus.value = true;
    } else {
      award.value = {
        prizeType: 0,
        prizeName: '谢谢参与',
        showImg: '',
        result: '',
        activityPrizeId: '',
        userPrizeId: '',
      };

      const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
      Gif.value = `https://img10.360buyimg.com/imgzone/jfs/t1/239145/16/11091/573502/66695aabF202b6768/f94440572fd4ac70.gif?+${Math.random()}`;
      endStatus.value = true;
      awardIndex.value = index === -1 ? 0 : index;
    }
  } catch (error: any) {
    closeToast();
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: '',
      activityPrizeId: '',
      userPrizeId: '',
    };

    const index = prizeInfo.findIndex((item) => item.prizeName === '谢谢参与');
    endStatus.value = true;
    awardIndex.value = index === -1 ? 0 : index;
    console.error(error);
  }
  await getChanceNum();
  // await getPrizes();
  setTimeout(() => {
    endStatus.value = false;
    showAward.value = true;
  }, 2000);
};
const startCallback = async () => {
  lzReportClick('djcj');
  if (baseInfo.thresholdResponseList.length) {
    showLimit.value = useThreshold({
      thresholdList: baseInfo.thresholdResponseList,
      className: 'common-message-niudan',
      startTime: dayjs(baseInfo.startTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss'),
    });
    return;
  }
  if (chanceNum.value <= 0) {
    showToast('您的抽奖次数已用完');
    return;
  }
  if (endStatus.value) {
    showToast('正在抽奖中，请勿重复点击');
  } else {
    lotteryDraw();
  }
};
// 抽奖结束会触发end回调
const endCallback = (prize: any) => {
  endStatus.value = true;
  setTimeout(() => {
    showAward.value = true;
  }, 1000);
};

// 获取任务列表
const getTask = async () => {
  try {
    const { data } = await httpRequest.post('/90031/getTask');
    tasks.splice(0);
    tasks.push(...data);
  } catch (error: any) {
    console.error(error);
  }
};
// 判断任务完成情况 邀请助力和会员开卡
const checkTask = async () => {
  if (pathParams.isJoin) {
    await getTask();
    await getChanceNum();
    showToast('开卡成功');
    if (pathParams.isTask) {
      const val = { openId: pathParams.openId };
      lzReportClick(JSON.stringify(val));
    } else {
      lzReportClick('openCard');
    }
  }
  // lzReportClick('fellowShop');
  if (pathParams.shareId) {
    // 邀请助力
    try {
      console.log('123456789');
      const res = await httpRequest.post('/90031/helpExcute', { shareId: pathParams.shareId });
      console.log('456', res);
      showToast('助力成功');
      await getTask();
    } catch (error: any) {
      console.log('456', error);
      showToast(error.message);
    }
  }

};
// 获取中奖名单
const getWinners = async () => {
  try {
    const res = await httpRequest.post('/90031/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
    if (activityGiftRecords.length > 4) {
      nextTick(() => {
        const mySwiper = new Swiper('.swiper-container', {
          autoplay: {
            delay: 1000,
            stopOnLastSlide: false,
            disableOnInteraction: false,
          },
          direction: 'vertical',
          loop: true,
          slidesPerView: 5,
          loopedSlides: 8,
          centeredSlides: true,
        });
      });
    }
  } catch (error: any) {
    console.error(error);
  }
};

// 展示任务弹窗
const showUseThreshold = () => {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
    className: 'common-message-niudan',
    startTime: dayjs(baseInfo.startTime).format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss'),
  });
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getChanceNum(), getPrizes(), getWinners(), getSkuList()]);
    await getTask();
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
    closeToast();
    await checkTask();
  } catch (error: any) {
    closeToast();
  }
};
init();
const backTop = () => {
  const timer = setInterval(() => {
    const osTop = document.documentElement.scrollTop || document.body.scrollTop;
    const ispeed = Math.floor(-osTop / 5);
    document.documentElement.scrollTop = osTop + ispeed;
    document.body.scrollTop = osTop + ispeed;
    if (osTop === 0) {
      clearInterval(timer);
    }
  }, 30);
};

// 做任务后刷新信息
const refreshTask = async () => {
  getChanceNum();
  getTask();
};
</script>

<style>
.dialog-white {
  background-color: white!important;
}
</style>
<style scoped lang="scss">

.footer-btn {
  width: 7.5rem;
  height: 1.2rem;
  display: flex;
  justify-content: space-around;
  margin-top: 0.3rem;
  // margin-bottom: 0.5rem;
  .item {
    width: 2.59rem;
    height: 0.65rem;
  }
}
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  .img-box {
    width: 7.5rem;
    height: 3.45rem;
    background-image: url('//img20.360buyimg.com/imgzone/jfs/t1/234853/6/21949/55991/667d10bcFcee34f76/64cf8bd38eca597c.png');
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    box-sizing: border-box;
    padding-top: 0.7rem;
    padding-left: 0.74rem;
    padding-right: 0.74rem;
    margin-top: 0.25rem;
  }

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: end;
  }
  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    // width: 1.18rem;
    // padding: 0 0.1rem;
    background-color: #ffea69;
    color: #ff7800;
    box-shadow: inset 0px 8px 10px 0px rgba(255, 251, 251, 0.4);
    width: 0.74rem;
    height: 0.77rem;
    margin-bottom: 0.1rem;
    margin-right: -0.3rem;
    font-size: 0.2rem;
    text-align: center;
    border-bottom-left-radius: 0.23rem;
    border-top-left-radius: 0.23rem;
    border-right: unset;
    // border: 0.01rem;
    // border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.rule-box {
  width: 100vw;
  height: 8.69rem;
  margin-top: 0.3rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/241030/4/13237/23154/667d271aFd16126ac/f3d1db24f5bc43b3.png);
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: top;
  box-sizing: border-box;
  padding-top: 0.8rem;
  .content {
    height: 7.4rem;
    box-sizing: border-box;
    padding-left: 0.74rem;
    padding-right: 0.74rem;
    // border: 0.3rem solid transparent;
    overflow-y: scroll;
  }
}
.rollBall {
  // background: url('https://img10.360buyimg.com/imgzone/jfs/t1/246785/18/13133/185356/667d0f42F6dd16dc9/60f0a9c34a14b54e.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 7.5rem;
  height: 7.63rem;
  position: relative;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  .machine {
    width: 7.5rem;
    height: 7.63rem;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/234571/26/20450/32199/666810d2F503d61cb/3e89c63d8a6a492b.png');
    position: absolute;
    top: 0;
  }

  .machine2 {
    width: 7.5rem;
    height: 7.68rem;
    background-size: 100%;
    background-repeat: no-repeat;
    // background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/239145/16/11091/573502/66695aabF202b6768/f94440572fd4ac70.gif');
    // background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/110163/29/39910/795848/6447b5e9Fa69e72dc/75b9b71571eebdb3.gif?id=".rand(10000,1000000)"');
    position: absolute;
    top: 0;
  }
  .content {
    position: absolute;
    left: 4.1rem;
    top: 5.7rem;
    width: 3rem;
    .start {
      width: 2.69rem;
      height: .96rem;
      margin: 0 auto;
    }
    .small-big {
      width: 0.89rem;
      margin-left: 1.8rem;
      margin-top: -0.5rem;
      animation: smallBig 1s infinite;
      animation-timing-function: linear;
      animation-direction: alternate;
    }
  }
  .award {
    position: absolute;
    top: 6rem;
    left: 1.5rem;
    animation: moveTop 1s;
    img {
      width: 1.5rem;
    }
  }
  @keyframes moveTop {
    0% {
      margin-top: -50px;
      opacity: 0;
    }
    30% {
      margin-top: -30px;
      opacity: 0;
    }
    50% {
      margin-top: -10px;
      opacity: 0.8;
    }
    100% {
      margin-top: 0px;
      opacity: 1;
    }
  }
  @keyframes smallBig {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.3);
    }
  }
}
.topprizebg {
  width: 6rem;
  height: 1.7rem;
  // transform: scale(0.8);
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;

  .swiper-container2 {
    width: 6rem;
    height: 1.76rem;
    display: flex;
  }
  .swiper-wrapper {
    display: flex;
  }
  .swiper-slide {
    width: 1.46rem;
    height: 1.76rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }

  .swiper-slide img {
    width: 1.2rem;
  }
}
.draws-num {
  position: absolute;
  left: 0.2rem;
  top: 3.4rem;
  z-index: 2;
  width: 7.11rem;
  height: .75rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/151774/30/37562/7519/666810d2Fd3a265d6/555c28bc5f9c12c7.png');
  background-size: 100%;
  background-repeat: no-repeat;
  text-align: center;
  line-height: .75rem;
  font-size: .45rem;
  font-family: 'FZZZHONGHK';
  color: #ff7800;
}

.draw-btn {
  width: 4rem;
  margin: 0 auto;

  img {
    width: 100%;
  }
}
.prize-box{
      width: 1.46rem;
      height: 1.76rem;
      // margin: 0.2rem 0.32rem 0.2rem 0.2rem;
      padding: 0.1rem;
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/229170/39/23566/4390/6694c094F112fb37c/21e03165f43f1371.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
      overflow: hidden;
      box-sizing: border-box;
      img{
        width: 100%;
      }
      .prize-name{
        width: 1.2rem;
        // margin-left: 15%;
        // background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/138524/9/21064/8976/619cdd47E1819f3a9/140f4a58e373a32d.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        font-size: 0.24rem;
        line-height: 0.3rem;
        color: #ff5400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
.prizes{
  width: 6rem;
  margin: 3.2rem auto 0 auto;
}

.sku{
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .title-img{
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background: url("https://img10.360buyimg.com/imgzone/jfs/t1/198727/40/18336/5096/619b80b9Eafb4de86/6e8012f498dd80d1.png") no-repeat;
    background-size: 100%;
  }
  .sku-list{
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    .sku-item{
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.4rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img{
        display: block;
        width: 3.4rem;
        height: 3.4rem;
      }
      .sku-text{
        width: 3.4rem;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        font-size: 0.3rem;
        color: #262626;
        height: 0.8rem;
        padding: 0 0.2rem;
        margin: 0.2rem 0 0.43rem;
        box-sizing: border-box;
      }
      .sku-btns{
        width: 3rem;
        height: 0.6rem;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/159308/25/21187/7832/619b8d96Ee8b26a4f/a4dd481902c8e6e6.png);
        background-size: 100%;
        margin: 0 auto 0.2rem;
        .price{
          width: 2.05rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #fff;
          text-align: left;
          padding-left: 0.2rem;
          box-sizing: border-box;
        }
        .to-bug{
          width: 0.95rem;
          height: 0.6rem;
          line-height: 0.6rem;
          font-size: 0.3rem;
          color: #df006e;
          text-align: center;
        }
      }
    }
  }
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;

  .winners-content {
    width: 6.6rem;
    height: 4.7rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0 0.3rem;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem 0;
  border-bottom: 1px dashed rgb(230, 230, 230);

  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
@font-face {
  font-family: 'FZZZHONGHK';
  font-style: normal;
  font-weight: normal;
  src: url(https://lzcdn.dianpusoft.cn/fonts/FZZZHONGJW/FZZZHONGJW.TTF) format('TTF');
  font-display: swap;
}
</style>
