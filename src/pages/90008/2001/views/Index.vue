<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <!-- <img :src="furnish.actBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/240844/30/1201/31837/659e7517F8ee215da/d35a7f6fda303986.jpg'" alt="" class="kv-img" /> -->
      <div class="btn-list">
        <img :src="furnish.ruleBtnImg" alt="" @click="showRule = true" />
      </div>
    </div>
    <div class="promotion-box" :style="furnishStyles.promotionImg.value">
      <!--即刻前往-->
      <div class="promotion-go-btn" @click="handleGoBtn"></div>
    </div>
    <div class="product-box">
      <div class="sku-item" :style="furnishStyles.shelfProduct01.value" @click="goSku01"></div>
      <div class="sku-item" :style="furnishStyles.shelfProduct02.value" @click="goSku02"></div>
      <div class="sku-item" :style="furnishStyles.shelfProduct03.value" @click="goSku03"></div>
      <div class="sku-item" :style="furnishStyles.shelfProduct04.value" @click="goSku04"></div>
    </div>
    <!-- 进店逛逛 -->
    <div class="go-shop-btn" :style="furnishStyles.bottomBtn.value" @click="handleGoShop"></div>
  </div>
  <!--活动规则-->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 等级不足 -->
  <VanPopup teleport="body" v-model:show="GradePopupShow">
    <GradePopup @close="GradePopupShow = false"></GradePopup>
  </VanPopup>
  <!-- 非会员弹窗 -->
  <VanPopup teleport="body" v-model:show="showNonMember">
    <NonMember @close="showNonMember = false"></NonMember>
  </VanPopup>
  <!-- 领取成功弹窗 -->
  <VanPopup teleport="body" v-model:show="showSuccess">
    <ReceivedSussessPopup @close="showSuccess = false"></ReceivedSussessPopup>
  </VanPopup>
  <!-- 已领取过弹窗 -->
  <VanPopup teleport="body" v-model:show="showReceived">
    <ReceivedPopup @close="showReceived = false"></ReceivedPopup>
  </VanPopup>
  <!-- 其他渠道已领取弹窗 -->
  <VanPopup teleport="body" v-model:show="showReceivedOther">
    <ReceivedOtherPopup @close="showReceivedOther = false"></ReceivedOtherPopup>
  </VanPopup>
</template>
<script setup lang="ts">
import { inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import RulePopup from '../components/RulePopup.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';
import dayjs from 'dayjs';
import GradePopup from '../components/GradePopup.vue';
import NonMember from '../components/NonMember.vue';
import ReceivedSussessPopup from '../components/ReceivedSussessPopup.vue';
import ReceivedPopup from '../components/ReceivedPopup.vue';
import ReceivedOtherPopup from '../components/ReceivedOtherPopup.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;

// 店铺名称
const shopName = ref(baseInfo.shopName);

// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 等级不足
const GradePopupShow = ref(false);
// 非会员
const showNonMember = ref(false);
// 领取成功
const showSuccess = ref(false);
// 已领取过
const showReceived = ref(false);
// 其他渠道已领取
const showReceivedOther = ref(false);

// 即刻前往
const handleGoBtn = () => {
  console.log('即刻前往');
  window.jmfe.toAny(furnish.promotionLink);
};

// 跳转商品详情
const goSku01 = () => {
  window.jmfe.toAny(furnish.shelfProduct01Link);
};
const goSku02 = () => {
  window.jmfe.toAny(furnish.shelfProduct02Link);
};
const goSku03 = () => {
  window.jmfe.toAny(furnish.shelfProduct03Link);
};
const goSku04 = () => {
  window.jmfe.toAny(furnish.shelfProduct04Link);
};
// 进店逛逛
const handleGoShop = () => {
  window.jmfe.toAny(furnish.bottomBtnLink);
};

// 活动规则相关
const showRulePopup = async () => {
  try {
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};

const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    ruleTest.value = data;
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 活动主接口
const getActInfo = async () => {
  try {
    const { data } = await httpRequest.post('/90008/activity');
    if (data.status === 2) {
      // 等级不足
      GradePopupShow.value = true;
    } else if (data.status === 0) {
      // 非会员
      showNonMember.value = true;
    } else if (data.status === 4) {
      // 领取成功
      showSuccess.value = true;
    } else if (data.status === 1) {
      // 已领取过
      showReceived.value = true;
    } else if (data.status === 3) {
      // 其他渠道已领取
      showReceivedOther.value = true;
    }
  } catch (error: any) {
    throw new Error(error.message);
  }
};

// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getRule(), getActInfo()]);
    closeToast();
  } catch (error: any) {
    showToast({
      message: error.message,
      duration: 2000, // 停留 2 秒
    });
  }
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();
</script>
<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.1rem;
}
.header-kv {
  position: relative;
  height: 8rem;
  .kv-img {
    width: 100%;
  }
  .btn-list {
    position: absolute;
    top: 1rem;
    right: 0;
    img {
      width: 0.47rem;
      margin-bottom: 0.18rem;
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }
}
.promotion-box {
  width: 7.06rem;
  height: 2.88rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0 auto;
  position: relative;
  .promotion-go-btn {
    width: 2rem;
    height: 0.5rem;
    // background: green;
    position: absolute;
    border-radius: 1rem;
    bottom: 0.8rem;
    right: 1.4rem;
  }
}
.product-box {
  width: 7.5rem;
  // height: 9rem;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding-top: 1rem;
  .sku-item {
    width: 3.5rem;
    height: 4.3rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-bottom: 0.2rem;
  }
}
.go-shop-btn {
  width: 3.36rem;
  height: 0.6rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0.2rem auto 0;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
