<template>
  <div class="rule-bk">
    <div class="content">
      <!-- <img :src="detail.prizeImg" alt="" class="card-img" />
      <div class="prize-name">{{ detail.prizeName }}</div> -->
      <div class="item" v-show="detail.cardNumber">
        <div class="label">卡号：</div>
        <div class="text">{{ detail.cardNumber }}</div>
      </div>
      <div class="item" v-show="detail.cardPassword">
        <div class="label">卡密：</div>
        <div class="text">{{ detail.cardPassword }}</div>
      </div>
      <div class="tip">礼品卡说明：</div>
      <div class="tip" :class="{ 'tip-small': detail.cardNumber && detail.cardPassword }">{{ detail.cardDesc }}</div>
    </div>
    <div class="copy-btn"></div>
    <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { computed, PropType } from 'vue';
import { CardType } from '../ts/type';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const props = defineProps({
  detail: {
    type: Object as PropType<CardType>,
    required: true,
  },
});

const copyText = computed(() => {
  if (props.detail.cardNumber && props.detail.cardPassword) {
    return `卡号：${props.detail.cardNumber}\n卡密：${props.detail.cardPassword}`;
  }
  if (props.detail.cardNumber) {
    return `卡号：${props.detail.cardNumber}`;
  }
  if (props.detail.cardPassword) {
    return `卡密：${props.detail.cardPassword}`;
  }
  return '';
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const clipboard = new Clipboard('.copy-btn', {
  text() {
    return copyText.value;
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url('../assets/copyCard.png');
  background-size: 100%;
  width: 6.15rem;
  height: 7.79rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.1rem;
  // background-color: #f2f2f2;
  // border-radius: 0.2rem 0.2rem 0 0;
  // width: 100vw;

  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    width: 0.6rem;
    height: 0.6rem;
    cursor: pointer;
    background: url('../assets/closeBtn.png') no-repeat;
    background-size: 100%;
  }

  .content {
    padding: 0.6rem 0.7rem 0;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    width: 100%;
    .card-img {
      margin: 0 auto;
      width: 1.5rem;
    }
    .prize-name {
      color: #b25a08;
      width: 100%;
      text-align: center;
      font-weight: bolder;
      margin-top: 0.1rem;
      margin-bottom: 0.1rem;
      font-size: 0.3rem;
      line-height: 0.3rem;
    }
    .item {
      margin-bottom: 0.1rem;

      display: flex;
      align-items: center;
      .label {
        width: 1rem;
        color: #b25a08;
        font-size: 0.28rem;
      }
      .text {
        width: 3.65rem;
        height: 0.49rem;
        line-height: 0.49rem;
        font-size: 0.24rem;
        color: #b25a08;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-radius: 0.11rem;
        background-color: #fff;
        padding: 0 0.2rem;
        border: 0.01rem solid #c0672b;
      }
    }
    .tip {
      font-size: 0.2rem;
      color: #b25a08;
      white-space: pre-line;
      max-height: 3.1rem;
      overflow-y: scroll;
    }
    .tip-small {
      max-height: 2.5rem;
    }
  }
  .copy-btn {
    width: 3.6rem;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    bottom: 1.1rem;
    height: 0.6rem;
  }
}
</style>
<style>
.van-field__control {
  height: 0.65rem;
  background-color: #fff;
  border-radius: 0.15rem;
  border: 0.01rem solid #f7ca97;
  padding-left: 0.2rem;
}
.van-field__label {
  color: #b25a08;
  font-size: 0.26rem;
  font-weight: bold;
  line-height: 0.65rem;
  width: 1.4rem;
}
</style>
