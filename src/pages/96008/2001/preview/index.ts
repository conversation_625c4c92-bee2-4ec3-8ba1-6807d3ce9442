import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/320268/39/9707/350552/68511a74F2a8d0ae7/fccb7ae2a906d0ef.png',
  actBg: '',
  actBgColor: '#fdeab9',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/290275/29/13200/3754/68511a75F8731435e/bf96d3e71b42cada.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/320444/15/9770/3916/68511a74F336c6523/00689a5e39ad5f31.png',
  stepBg: '//img10.360buyimg.com/imgzone/jfs/t1/314659/32/10598/76787/68528ffaF5b7bca06/2c86c85fea5f4e16.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/291113/38/14583/13795/68538995F45ce22ae/d00603dbd38bf61b.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/279026/32/9061/41157/67e1284dF767f99ad/ecf68271ad90ef6d.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/279142/30/9023/5263/67e1284eF8311ac7b/713b3a7cfbacc716.jpg',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274190/1/9056/48480/67e1284eF7dfff0a8/0aa0802faa26ce5c.jpg'
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员转段赠好礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  // TODO
  app.provide('decoData', _decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
