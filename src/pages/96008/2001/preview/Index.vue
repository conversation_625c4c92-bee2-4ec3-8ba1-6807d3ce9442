<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="kv" >
      <img :src="furnish.pageBg" alt="" />
      <div class="btn-list">
        <img @click="toast" :src="furnish.ruleBtn" alt="" />
        <img @click="toast" :src="furnish.myPrizeBtn" alt="" />
      </div>
    </div>
    <div class="step">
      <img :src="furnish.stepBg" alt="" />
    </div>
    <div v-if="sectionList.length">
      <div class="sectionBg" v-for="(item, index) in sectionList" :key="index" :style="furnishStyles.prizeBg.value">
        <div class="title">
          <div class="line"></div>
          <div class="text">
            转{{item.sectionSort}}段拿{{item.sectionName}}
          </div>
          <div class="line"></div>
        </div>
        <div class="series-list">
          <div v-for="(prize, prizeIndex) in item.sectionPrizeList" :key="prizeIndex">
            <div class="prizeTitle">
              下单满{{prize.potNum}}罐且订单完成后无售后,即可领取奖品
            </div>
            <div class="prize-item">
              <div class="prize-img">
                <img :src="prize.prizeImg" alt="" />
              </div>
              <div class="prizeRight">
                <img v-if="prize.prizeImg" src="//img10.360buyimg.com/imgzone/jfs/t1/316325/25/10283/2185/68511a74F09fa7ecb/6e999cb3a0b16f98.png" alt="" class="exchange-btn" @click="toast" />
                <div class="countDownNum">{{prize.prizeName}}剩余{{prize.sendTotalCount}}个</div>
                <div class="potNumText">
                  <div>当前已购罐数：</div>
                  <div class="progress-bar">
                    <div class="progress-bar-inner" :style="{ width: progressWidth }"/>
                  </div>
                  <div>0/{{item.potNum}}罐</div>
                </div>
              </div>
            </div>
          </div>
          <div class="skuBox">
            <div class="skuItem" v-for="(itemSkuList, skuIndex) in item.skuList" :key="skuIndex">
              <img class="skuMainPictureBox" :src="itemSkuList.skuMainPicture" alt="">
              <div class="skuText">{{itemSkuList.skuName}}</div>
              <div class="skuBtn" @click="toast"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <img style=" margin: 0 auto;width: 7.4rem"  :src="furnish.ruleImg" alt="" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

// const sectionList = ref<any[]>([]);
const sectionList = ref<any[]>([
  {
    sectionType: '一段',
    sectionSort: 1,
    skuList: [
      {
        nameType: 1,
        potNum: 1,
        sectionSort: 1,
        sectionType: '一段',
        skuId: 10097246839573,
        skuMainPicture: 'http://img13.360buyimg.com/n0/jfs/t1/234504/18/13946/52081/65e199cdF58b837c0/45b10688b0976b07.png',
        skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
        sort: 1
      },
      {
        nameType: 1,
        potNum: 1,
        sectionSort: 1,
        sectionType: '一段',
        skuId: 10097246839573,
        skuMainPicture: 'http://img13.360buyimg.com/n0/jfs/t1/234504/18/13946/52081/65e199cdF58b837c0/45b10688b0976b07.png',
        skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
        sort: 1
      },
      {
        nameType: 1,
        potNum: 1,
        sectionSort: 1,
        sectionType: '一段',
        skuId: 10097246839573,
        skuMainPicture: 'http://img13.360buyimg.com/n0/jfs/t1/234504/18/13946/52081/65e199cdF58b837c0/45b10688b0976b07.png',
        skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
        sort: 1
      },
      {
        nameType: 1,
        potNum: 1,
        sectionSort: 1,
        sectionType: '一段',
        skuId: 10097246839573,
        skuMainPicture: 'http://img13.360buyimg.com/n0/jfs/t1/234504/18/13946/52081/65e199cdF58b837c0/45b10688b0976b07.png',
        skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
        sort: 1
      },
    ],
    previewSkuList: [

    ],
    beforeOptions: [
      1,
      2
    ],
    afterOptions: '',
    beforeSkuList: [
      {
        nameType: 1,
        potNum: 1,
        sectionSort: 1,
        sectionType: '一段',
        skuId: 10097246839573,
        skuMainPicture: 'http://img13.360buyimg.com/n0/jfs/t1/234504/18/13946/52081/65e199cdF58b837c0/45b10688b0976b07.png',
        skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
        sort: 1
      },
      {
        nameType: 1,
        potNum: 6,
        sectionSort: 1,
        sectionType: '一段',
        skuId: 10097246839579,
        skuMainPicture: null,
        skuName: null,
        sort: 1
      },
      {
        nameType: 1,
        potNum: 2,
        sectionSort: 2,
        sectionType: '二段',
        skuId: 10124918367321,
        skuMainPicture: 'http://img11.360buyimg.com/n0/jfs/t1/207637/6/48883/83020/6732f47eF34ff0bdc/e52ac6323d267311.jpg',
        skuName: '直液式走珠笔0.5mm黑色中性笔速干签字笔学生用考试专用笔水性笔 【0.5mm子弹头】24支',
        sort: 2
      }
    ],
    afterSkuList: [
      {
        nameType: 1,
        potNum: 3,
        sectionSort: 3,
        sectionType: '三段',
        skuId: 10097246839575,
        skuMainPicture: null,
        skuName: null,
        sort: 3
      }
    ],
    sectionName: '大额优惠券',
    sectionPrizeList: [
      {
        prizeKey: null,
        prizeType: 4,
        dayLimitType: 1,
        dayLimit: 1,
        prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/298851/5/16420/11583/68528757F2bfe3554/28698a6a5876e395.png',
        unitCount: 1,
        prizeName: '1积分',
        unitPrice: 1,
        sendTotalCount: 1
      }
    ]
  },
  {
    sectionType: '二段',
    sectionSort: 2,
    skuList: [
      {
        nameType: 1,
        potNum: 2,
        sectionSort: 2,
        sectionType: '二段',
        skuId: 10124918367321,
        skuMainPicture: 'http://img11.360buyimg.com/n0/jfs/t1/207637/6/48883/83020/6732f47eF34ff0bdc/e52ac6323d267311.jpg',
        skuName: '直液式走珠笔0.5mm黑色中性笔速干签字笔学生用考试专用笔水性笔 【0.5mm子弹头】24支',
        sort: 2
      }
    ],
    previewSkuList: [
      {
        jdPrice: '42.00',
        skuId: 10124918367321,
        skuMainPicture: 'http://img11.360buyimg.com/n0/jfs/t1/207637/6/48883/83020/6732f47eF34ff0bdc/e52ac6323d267311.jpg',
        skuName: '直液式走珠笔0.5mm黑色中性笔速干签字笔学生用考试专用笔水性笔 【0.5mm子弹头】24支'
      }
    ],
    sectionPrizeList: [
      {
        prizeKey: 's250508194133841554',
        prizeType: 3,
        dayLimitType: 1,
        dayLimit: 1,
        prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/280183/7/28512/8905/68108888F6cc3b0bf/015392217ab66bbb.png',
        activityIds: [

        ],
        createTime: 1746704493000,
        quantityAvailable: 29,
        quantityFreeze: 0,
        quantityPreDeliver: 1,
        quantityRemain: 30,
        quantityTotal: 30,
        shopId: null,
        skuCode: 's250508194133841554',
        skuDetails: null,
        skuMainPicture: 'https://img10.360buyimg.com/imgzone/jfs/t1/280183/7/28512/8905/68108888F6cc3b0bf/015392217ab66bbb.png',
        skuName: 'lyq-测试延期发奖',
        version: 1,
        wmsCode: null,
        prizeName: 'lyq-测试延期发奖',
        unitPrice: 1,
        unitCount: 1,
        sendTotalCount: 2
      }
    ],
    sectionName: '1111111'
  },
  {
    sectionType: '三段',
    sectionSort: 3,
    skuList: [
      {
        nameType: 1,
        potNum: 3,
        sectionSort: 3,
        sectionType: '三段',
        skuId: 10097246839575,
        skuMainPicture: null,
        skuName: null,
        sort: 3
      }
    ],
    previewSkuList: [

    ]
  },
  {
    sectionType: '四段',
    sectionSort: 4,
    skuList: [
      {
        nameType: 1,
        potNum: 4,
        sectionSort: 4,
        sectionType: '四段',
        skuId: 10124918367326,
        skuMainPicture: null,
        skuName: null,
        sort: 4
      }
    ],
    previewSkuList: [

    ]
  }
]);

const skuActStep = ref(0);
const changeSkuActStep = (idx: number) => {
  skuActStep.value = idx;
};

const isLoadingFinish = ref(false);
// 保存实物地址相关
const showSaveAddress = ref(false);

const toast = () => {
  showToast('活动预览，仅供查看');
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showSaveAddress.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    console.log('data-C',data)
    console.log(data.sectionList, 'data111');
    sectionList.value = data.sectionList;
  } else if (type === 'screen') {
    createImg();
  }
};

// 设置活动后的预览
onMounted(() => {
  console.log('onMounted装修实时数据修改');
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  console.log(activityData, 'activityData');
  if (activityData) {
    sectionList.value = activityData.sectionList;
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
@font-face {
  font-family: 'AlimamaFangYuanTi';
  src: url('../assets/AlimamaFangYuanTiVF-Thin.ttf') format('truetype');
}
</style>
<style scoped lang="scss">
.bg {
  min-height: 100vh;
  position: relative;
  padding-bottom: 0.35rem;
  .kv {
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .btn-list{
      position: absolute;
      top: 0.2rem;
      right: 0;
      img {
        display: flex;
        justify-content: flex-end;
        width: 1.1rem;
        margin: 0.1rem 0 0;
     }
    }
  }
  .step{
    width: 7.5rem;
    height: 4rem;
    img{
      width: 7.5rem;
    }
  }
  .sectionBg {
    padding-top: 0.4rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .title{
    display: flex;
    text-align: center;
    align-items: center;
    font-size: 0.41rem;
    color: #6a3111;
    justify-content: center;
    font-weight: bold;
    font-family: 'AlimamaFangYuanTi';
    .text{
      margin: 0 0.1rem;
    }
    .line {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/318865/6/10093/129/68528b2fF36bea253/51099a5b7f4b4acd.png) no-repeat;
      width: 0.41rem;
      height: 0.03rem;
    }
  }
  .series-list {
    width: 7.5rem;
    height: 7.5rem;
    margin: 0.2rem auto 0.5rem;
    padding: 0.3rem 0 0;
    .prizeTitle {
      padding: 0.2rem 0.4rem;
      font-size: 0.3rem;
      text-align: center;
      color: #4e220b;
    }
    .prize-item {
      /*height: 2rem;*/
      padding: 0.4rem 0.52rem 0;
      display: flex;
      align-items: center;
      justify-content: space-around;
      text-align: center;
      margin: 0 auto;
      .prize-img {
        flex: 1.3;
        img{
          width: 1.7rem;
          margin: 0 auto;
        }
      }
      .prizeRight{
        text-align: center;
        flex: 1;
        .exchange-btn {
          width: 1.19rem;
          height: 0.28rem;
          margin: 0 auto;
        }
        .countDownNum{
          background-color: #e0cbb1;
          border-radius: 0.13rem;
          font-size: 0.2rem;
          display: inline-block;
          padding: 0 0.2rem;
          color: #4e220b;
          text-align: center;
          margin-top: 0.2rem;
        }
        .potNumText{
          font-size: 0.14rem;
          color: #5c2a0e;
          display: flex;
          margin: 0.2rem auto;
          align-items: center;
          .progress-bar {
            width: 1.5rem;
            height: 0.11rem;
            /* margin: 0.62rem auto 0; */
            border: 0.01rem solid #5c2a0e;
            border-radius: 0.2rem;
            .progress-bar-inner {
              width: 80%;
              height: 0.1rem;
              border-radius: 0.2rem;
              background: #5c2a0e;
              position:relative;
            }
          }
        }
      }
    }
  }
  .skuBox {
    width: 6.9rem;
    height: 3rem;
    overflow: hidden;
    overflow-x: scroll;
    display: flex;
    margin: 0.3rem auto 0;
    .skuItem{
      width: 2.3rem;
      .skuMainPictureBox{
        width: 2rem;
        margin: 0 auto;
      }
      .skuText{
        font-size: 0.18rem;
        color: #4e220b;
      }
    }
  }
}
.step {
  width: 2.1rem;
  height: 0.6rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  text-align: center;
  padding-top: 0.05rem;
  line-height: 0.6rem;
  font-size: 0.22rem;
  .step-svg {
    width: 100%;
    height: 100%;
    stroke-linejoin: round;
    font-weight: bold;
    vertical-align: middle;
  }
}
.gray {
  filter: grayscale(1);
}
</style>
