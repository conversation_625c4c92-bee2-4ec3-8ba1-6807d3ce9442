<template>
  <div class="main" :style="{ background: `url(${decoData.bgColor})` }" v-if="isLoadingFinish">
    <div class="kv">
      <img :src="decoData.kvImage" alt="" />
      <div class="pg">
        <div
          class="rulesBtn"
          @click="
            rulePopup = true;
            lzReportClick('hdgz');
          "
          :style="{ backgroundImage: `url(${decoData.ruleBtnBg})` }"></div>
        <div
          class="rulesBtn"
          @click="
            prizePopup = true;
            lzReportClick('wdjp');
          "
          :style="{ backgroundImage: `url(${decoData.prizeBtnBg})` }"></div>
      </div>
    </div>
    <div class="prizeBox">
      <div class="header">
        <div class="item">累计获得总罐数 <br />{{ mainInfo.state === '2' ? '-' : mainInfo.total }}</div>
        <div class="item redItem">有效可使用罐数 <br />{{ mainInfo.state === '2' ? '-' : mainInfo.left }}</div>
        <div class="item">历史已兑换罐数<br />{{ mainInfo.state === '2' ? '-' : mainInfo.cost }}</div>
      </div>
      <div class="prize" v-for="item in mainInfo.gifts" :key="item.giftId">
        <img :src="item.mainImage" alt=""  @click="exChangePrize(item)"/>
<!--        <div class="count" v-if="item.bottle">-->
<!--          <img :src="`${decoData.bottleBg}`" alt="" />-->
<!--          <span> {{ item.bottle }}罐</span>-->
<!--        </div>-->
        <img v-if="item.state == 1" class="exchangeBtn" :src="`${decoData.exChangeBtn}`" alt="" @click="exChangePrize(item)" />
        <img v-else class="exchangeBtn" :src="`${decoData.exChangeBtnGray}`" alt="" @click="exChangePrize(item)" />
      </div>
      <img @click="gotoShopPage(baseInfo.shopId)" class="toShop" :src="`${decoData.toShopBtn}`"  alt="" />
    </div>
    <div class="footer">
      <img :src="decoData.notice" alt=""/>
      <div v-html="formatRules(decoData.noticeText)"></div>
    </div>
  </div>
  <!--规则弹窗-->
  <VanPopup teleport="body" v-model:show="rulePopup">
    <RulePopup @close="rulePopup = false"></RulePopup>
  </VanPopup>
  <!--奖品弹窗-->
  <VanPopup teleport="body" v-model:show="prizePopup">
    <PrizePopup @close="prizePopup = false" :prizeRecord="mainInfo.prizes"></PrizePopup>
  </VanPopup>
  <!--不满足弹窗-->
  <VanPopup teleport="body" v-model:show="notEnoughPopup">
    <NotEnoughPopup @close="notEnoughPopup = false"></NotEnoughPopup>
  </VanPopup>
  <!--兑换成功弹窗-->
  <VanPopup teleport="body" v-model:show="exChangeSuccessPopup" >
    <ExChangeSuccessPopup :prizeInfo="prizeInfo" @close="exChangeSuccessPopup = false"></ExChangeSuccessPopup>
  </VanPopup>
  <!--发放失败弹窗-->
  <VanPopup teleport="body" v-model:show="sendFailPopup" >
    <SendFailPopup @close="sendFailPopup = false" :prizeInfo="prizeInfo"></SendFailPopup>
  </VanPopup>
  <!--确认弹窗-->
  <VanPopup teleport="body" v-model:show="confirmPopup" >
    <ConfirmPopup :prizeInfo="prizeInfo" @close="confirmPopup = false" @success="exChangeSuccess" @fail="sendFailPopup = true"></ConfirmPopup>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { gotoShopPage, toCustomerService } from '@/utils/platforms/jump';
import RulePopup from './components/RulePopup.vue';
import PrizePopup from './components/PrizePopup.vue';
import NotEnoughPopup from './components/NotEnoughPopup.vue';
import ExChangeSuccessPopup from './components/ExChangeSuccessPopup.vue';
import SendFailPopup from './components/SendFailPopup.vue';
import ConfirmPopup from './components/ConfirmPopup.vue';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import useThreshold from '@/hooks/useThreshold';

const isLoadingFinish = ref(false);
const decoData = inject('decoData') as any;
const baseInfo = inject('baseInfo') as BaseInfo;
const rulePopup = ref(false);
const prizePopup = ref(false);
const notEnoughPopup = ref(false);
const exChangeSuccessPopup = ref(false);
const sendFailPopup = ref(false);
const confirmPopup = ref(false);

const showLimit = ref(false); // 展示门槛显示弹框
showLimit.value = useThreshold({
  thresholdList: baseInfo.thresholdResponseList,
});
const prizeInfo = ref({}) as any;
const mainInfo = ref({}) as any;

const formatRules = (text: string) => text.replace(/；/g, '；<br />');
const getMainInfo = async () => {
  try {
    const { data } = await httpRequest.post('/firmus/1899014730653335553/main');
    mainInfo.value = data;
    if (baseInfo.status === 2 && mainInfo.value.state === '2') {
      notEnoughPopup.value = true;
    }
  } catch (error: any) {
    showToast(error.message);
  }
};

const exChangePrize = async (item: any) => {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
  });
  if (baseInfo.thresholdResponseList.length) {
    return;
  }
  if (mainInfo.value.state === '2') {
    showToast('抱歉，您不符合参与条件');
    return;
  }
  prizeInfo.value = item;
  const statusMessages = {
    1: () => {
      confirmPopup.value = true;
    },
    2: () => showToast('抱歉，奖品已发完，稍后再来看看吧~'),
    3: () => showToast('抱歉，您的罐数不足，快去下单领好礼~'),
    4: () => showToast('抱歉,您暂时不满足领取条件,快去逛逛吧~'),
  };

  const messageFunction = statusMessages[item.state];
  if (messageFunction) {
    messageFunction();
  }
};

const exChangeSuccess = () => {
  exChangeSuccessPopup.value = true;
  getMainInfo().then();
};

const init = async () => {
  showLimit.value = useThreshold({
    thresholdList: baseInfo.thresholdResponseList,
  });
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([await getMainInfo()]);
    isLoadingFinish.value = true;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
init();
</script>
<style>
@font-face {
  font-family: 'FZZZHONGJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZZZHONGJW/FZZZHONGJW.TTF');
}
* {
  font-family: 'FZZZHONGJW';
}
</style>

<style scoped lang="scss">
.main {
  position: relative;
  width: 100%;
  background-repeat: repeat-y;
  background-size: 100%;
  min-height:100vh;
  .kv {
    position: relative;
    img {
      width: 100%;
      height: auto;
    }
    .pg {
      position: absolute;
      right: 0;
      top: 0.5rem;
      .rulesBtn {
        background-size: 100% 100%;
        width: 1.3rem;
        height: 0.34rem;
        margin-bottom: 0.1rem;
      }
    }
  }
  .prizeBox {
    width: 7.3rem;
    min-height: 4.36rem;
    background: linear-gradient(0deg, #ffebbd 0%, #fffefb 100%);
    margin: 0 auto 0.2rem;
    border-radius: 0.1rem;
    box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);
    padding: 0.3rem 0.3rem 0.1rem 0.3rem;
    .header {
      display: flex;
      text-align: center;
      justify-content: space-around;
      align-items: center;
      margin-bottom: 0.2rem;
      font-weight: 800;
      .item {
        padding: 0.12rem 0.16rem;
        background: linear-gradient(93deg, #ffe59e 0%, #fff6c4 47%, #ffe59e 100%);
        font-size: 0.2rem;
        color: #492201;
        border-radius: 0.3rem;
        height: 0.7rem;
      }
      .redItem {
        color: #cb1401;
        font-size: 0.26rem;
        height: 0.8rem;
      }
    }
    .prize {
      position: relative;
      margin-bottom: 0.3rem;
      img {
        width: 100%;
        height: auto;
      }
      .count {
        position: absolute;
        left: 2.4rem;
        top: 0.1rem;
        font-size: 0.2rem;
        color: #710000;
        img {
          width: 0.6rem;
          height: 0.6rem;
        }
        span {
          position: absolute;
          left: 0;
          top: 0;
          width: 0.6rem;
          height: 0.6rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.24rem;
        }
      }
      .exchangeBtn {
        width: 1.82rem;
        height: 0.45rem;
        position: absolute;
        right: 0.5rem;
        bottom: -0.2rem;
      }
    }
    .toShop {
      width: 4.88rem;
      height: 0.54rem;
      margin: 0.4rem auto 0;
    }
  }
  .footer {
    width: 7.3rem;
    min-height: 2rem;
    background: linear-gradient(0deg, #fffefb 0%, #fffefb 100%);
    margin: 0 auto 0.2rem;
    border-radius: 0.1rem;
    box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);
    padding: 0.3rem 0.3rem 0.1rem 0.3rem;
    color: #492201;
    font-size: 0.18rem;
    img {
      width: 2.24rem;
      height: 0.47rem;
      margin: 0 auto 0.2rem;
    }
    div {
      text-align: left;
      margin-bottom: 0.2rem;
    }
  }
}
img {
  width: 100%;
  height: auto;
}
</style>
