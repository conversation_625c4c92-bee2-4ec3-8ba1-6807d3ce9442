<template>
  <div class="rule-bk" :style="{backgroundImage:`url(${decoData.confirmBg})`}" >
    <div class="close"  @click="closeDialog"></div>
    <div class="main">
       <div>会员专属</div>
      <div class="title">{{prizeInfo?.giftName}}</div>
    </div>
    <img class="img" :src="prizeInfo?.successImage" alt="" />
    <div class="btn" @click="clickGetPrizeFn"></div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, inject, onUnmounted, ref } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { debounce } from 'lodash';

const props = defineProps(['prizeInfo']);
const emits = defineEmits(['close', 'success', 'fail']);
const decoData = inject('decoData') as any;
const baseInfo = inject('baseInfo') as BaseInfo;

const isLoading = ref(false);
const closeDialog = () => {
  emits('close');
};
const getRights = () => {
  if (isLoading.value) return;
  isLoading.value = true;
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    httpRequest.post('/firmus/1899014730653335553/draw', {
      giftId: props.prizeInfo?.giftId,
    }).then(() => {
      emits('success');
    }).catch(() => {
      emits('fail');
    }).finally(() => {
      closeDialog();
      isLoading.value = false;
      closeToast();
    });
  } catch (error: any) {
    emits('fail');
    closeDialog();
    closeToast();
  }
};

const clickGetPrizeFn = debounce(() => {
  getRights();
}, 1000);

onUnmounted(() => {
  clickGetPrizeFn.cancel();
});
</script>

<style scoped lang="scss" >
::-webkit-scrollbar {
  display: none;
}
.rule-bk {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 5.7rem;
  height: 7.88rem;
  position: relative;
  .close {
    width: 1.8rem;
    height: 0.7rem;
  }
  .main {
    text-align: center;
    font-size: 0.4rem;
    color: #710000;
    margin: 0.5rem auto  ;
    .title {
      font-size: 0.4rem;
      text-align: center;
      width: 90%;
      margin: 0 auto;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .img {
    width: 4.7rem;
    height:3.2rem;
    margin: 0 auto;
  }
  .btn {
    width: 100%;
    height: 1rem;
    position: absolute;
    bottom: .3rem;
  }
}
</style>
