<template>
  <active class="block" />
</template>

<script setup lang="ts">
import Active from './blocks/MainAct.vue';

</script>

<style lang="scss">
@font-face {
  font-family: 'Noto Sans CJK SC';
  font-style: normal;
  font-weight: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Domaine Sans Text';
  font-style: normal;
  font-weight: normal;
  font-display: swap;
}

::-webkit-scrollbar {
  display: none;
}

body.lock {
  #app {
    overflow: hidden;
  }
}

body.unlock {
  #app {
    overflow-y: auto !important;
  }
}

#app {
  width: 100vw;
  min-height: 100vh;
  // overflow: hidden;
  scroll-snap-type: y mandatory;
  transition: all 1s ease;
  // 英文，数字使用 Domaine Sans Text,中文使用 Noto Sans CJK SC'
  font-family: 'Domaine Sans Text', 'Noto Sans CJK SC', sans-serif;
}

.vh100 {
  height: 100vh;
}

.block {
  position: relative;
  // overflow: hidden;
  width: 100vw;
  min-height: 100vh;
  scroll-snap-align: start;
  scroll-snap-stop: always;
}
</style>
