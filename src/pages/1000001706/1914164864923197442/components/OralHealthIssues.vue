<template>
    <VanPopup teleport="body" v-model:show="show" position="center" :close-on-click-overlay="true">
        <div>
            <img class="pet-close-icon close-icon" :src="require('../asset/home/<USER>')" @click="closePopup()"/>
            <div class="common-text-popup">
                <!-- <img class="bg-img" :src="require('../asset/口腔健康问题popup.png')"/> -->
                <img class="bg-img" src="//img10.360buyimg.com/imgzone/jfs/t1/272050/17/24552/46954/680f3824F31dfc643/ce0e843d2fc4dc12.png"/>
            </div>
        </div>
    </VanPopup>
</template>
<script lang="ts" setup>
import { FLAGS } from 'html2canvas/dist/types/dom/element-container';
import { emit } from 'process';
import { defineProps, computed, ref } from 'vue';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

const show = computed(() => props.isShow);
const emits = defineEmits(['closePopup']);
const closePopup = () => {
  emits('closePopup');
};

</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
