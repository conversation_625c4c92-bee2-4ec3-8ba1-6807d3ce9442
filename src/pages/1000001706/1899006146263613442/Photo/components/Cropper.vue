<template>
  <div id="cropper">
    <van-popup v-model:show="isShowPopup" safe-area-inset-top safe-area-inset-bottom teleport="body" :lock-scroll="false">
      <van-button class="btn" type="primary" @click="_successCropper"><van-icon name="add" style="margin-right: 0.1rem" />上传</van-button>
      <cropper
        class="cropper"
        :src="imageSrc"
        :auto-zoom="false"
        :stencil-props="{
          aspectRatio: 1 / 1,
          class: 'cropper-stencil',
          previewClass: 'cropper-stencil__preview',
          draggingClass: 'cropper-stencil--dragging',
          handlersClasses: {
            default: 'cropper-handler',
            eastNorth: 'cropper-handler--east-north',
            westNorth: 'cropper-handler--west-north',
            eastSouth: 'cropper-handler--east-south',
            westSouth: 'cropper-handler--west-south',
          },
          handlersWrappersClasses: {
            default: 'cropper-handler-wrapper',
            eastNorth: 'cropper-handler-wrapper--east-north',
            westNorth: 'cropper-handler-wrapper--west-north',
            eastSouth: 'cropper-handler-wrapper--east-south',
            westSouth: 'cropper-handler-wrapper--west-south',
          },
        }"
        @change="handleCropperChange"></cropper>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
import { ref, onMounted, nextTick, toRefs, Ref, computed } from 'vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast } from 'vant';
import { Cropper } from 'vue-advanced-cropper';
import 'vue-advanced-cropper/dist/style.css';
import { delayToast } from '../common';

interface Props {
  imageSrc: string;
  isShow: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  imageSrc: 'https://img10.360buyimg.com/imgzone/jfs/t1/160464/30/47276/23712/66de97c2F3fc49a94/9622487457ec9f29.png',
  isShow: false,
});

const { imageSrc, isShow } = toRefs(props);

const isShowPopup = computed({
  get() {
    return isShow.value;
  },
  set() {
    _closePopup();
  },
});

type DefineEmits = {
  (e: 'close-popup', type: string): void;
  (e: 'success-cropper', src: string): void;
};
const emits = defineEmits<DefineEmits>();
const _closePopup = () => {
  emits('close-popup', 'cropper');
};

/**
 * 裁剪
 */
const fileBlob = ref<Blob>();
const handleCropperChange = (data: any): void => {
  const { canvas } = data;
  handleCompressor(canvas);
  return;
  const quality = 0.6; // 范围是 0 到 1
  canvas.toBlob(
    (blob: Blob) => {
      // 使用FileReader来读取Blob
      const reader = new FileReader();
      reader.onload = () => {
        console.log(blob.size, '裁剪成功');
        fileBlob.value = blob;
      };
      // 读取Blob为DataURL，同时指定图片格式和质量
      reader.readAsDataURL(blob);
    },
    'image/jpeg',
    quality,
  );
};
const compressorQuality: Ref<number> = ref(1);
// 处理裁剪后的压缩
const handleCompressor = (canvas: any) => {
  canvas.toBlob(
    (blob: Blob) => {
      // 使用FileReader来读取Blob
      const reader = new FileReader();
      reader.onload = () => {
        if (blob.size >= 1 * 1024 * 1024) {
          compressorQuality.value = Number((compressorQuality.value - 0.1).toFixed(1));
          handleCompressor(canvas);
        } else {
          console.log(blob.size, '裁剪成功');
          console.log(compressorQuality.value, '此时的质量');
          fileBlob.value = blob;
          compressorQuality.value = 1;
        }
      };
      // 读取Blob为DataURL，同时指定图片格式和质量
      reader.readAsDataURL(blob);
    },
    'image/jpeg',
    compressorQuality.value,
  );
};
const _successCropper = async (): Promise<any> => {
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  try {
    showLoadingToast({});
    // 转formdata
    let formData = new FormData();
    fileBlob.value && formData.append('file', fileBlob.value, 'babyAvatar.png');
    const res = await httpRequest.post('/common/uploadImgOss', formData, config);
    emits('success-cropper', res.data);
    isShowPopup.value = false;
  } catch (error: any) {
    delayToast(error);
  } finally {
    closeToast();
  }
};
</script>

<style lang="scss">
.cropper-stencil {
  &__preview {
    &:after,
    &:before {
      content: '';
      opacity: 0;
      transition: opacity 0.25s;
      position: absolute;
      pointer-events: none;
      z-index: 1;
    }

    &:after {
      border-left: solid 1px white;
      border-right: solid 1px white;
      width: 33%;
      height: 100%;
      transform: translateX(-50%);
      left: 50%;
      top: 0;
    }

    &:before {
      border-top: solid 1px white;
      border-bottom: solid 1px white;
      height: 33%;
      width: 100%;
      transform: translateY(-50%);
      top: 50%;
      left: 0;
    }
  }

  &--dragging {
    .cropper-stencil__preview {
      &:after,
      &:before {
        opacity: 0.7;
      }
    }
  }
}

.cropper-line {
  border-color: rgba(white, 0.8);
}

.cropper-handler-wrapper {
  width: 24px;
  height: 24px;
  &--west-north {
    transform: translate(0, 0);
  }
  &--east-south {
    transform: translate(-100%, -100%);
  }
  &--west-south {
    transform: translate(0, -100%);
  }
  &--east-north {
    transform: translate(-100%, 0);
  }
}

.cropper-handler {
  display: block;
  position: relative;
  flex-shrink: 0;
  transition: opacity 0.5s;
  border: none;
  background: white;
  height: 4px;
  width: 4px;
  opacity: 0;
  top: auto;
  left: auto;

  &--west-north,
  &--east-south,
  &--west-south,
  &--east-north {
    display: block;
    height: 16px;
    width: 16px;
    background: none;
    opacity: 0.7;
  }

  &--west-north {
    border-left: solid 2px white;
    border-top: solid 2px white;
  }

  &--east-south {
    border-right: solid 2px white;
    border-bottom: solid 2px white;
  }

  &--west-south {
    border-left: solid 2px white;
    border-bottom: solid 2px white;
  }

  &--east-north {
    border-right: solid 2px white;
    border-top: solid 2px white;
  }

  &--hover {
    opacity: 1;
  }
}
</style>
<style lang="scss" scoped>
.cropper {
  width: 6.5rem;
  height: 8rem;
  // background: #ddd;
  position: relative;
  z-index: 3000;
}
.btn {
  position: absolute;
  right: 0.3rem;
  bottom: 0.3rem;
  z-index: 3001;
}
</style>
