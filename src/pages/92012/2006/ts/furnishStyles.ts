import { computed, reactive } from 'vue';

export const furnish = reactive({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  receivePrizeBg: '', // 领取奖品按钮背景图
  thresholdBg: '', // 机制背景图
  seriesTabNotAct: '', // 系列tab未选中
  seriesTabAct: '', // 系列tab选中
  sampleBg: '', // 试用装背景图
  sampleGoodsBg: '', // 试用装商品背景图
  formalBg: '', // 正装背景图
  formalGoodsBg: '', // 正装商品背景图
  returnTopBtn: '', // 返回顶部按钮
  orderBtn: '', // 我的订单按钮
  myPrizeBtn: '', // 我的奖品按钮
  ruleBtn: '', // 规则按钮
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundColor: furnish.btnBg ?? '',
  borderColor: furnish.btnBorderColor ?? '',
}));

const thresholdBg = computed(() => ({
  backgroundImage: furnish.thresholdBg ? `url("${furnish.thresholdBg}")` : '',
}));
const seriesTabNotAct = computed(() => ({
  backgroundImage: furnish.seriesTabNotAct ? `url("${furnish.seriesTabNotAct}")` : '',
}));
const seriesTabAct = computed(() => ({
  backgroundImage: furnish.seriesTabAct ? `url("${furnish.seriesTabAct}")` : '',
}));
const sampleBg = computed(() => ({
  backgroundImage: furnish.sampleBg ? `url("${furnish.sampleBg}")` : '',
}));
const sampleGoodsBg = computed(() => ({
  backgroundImage: furnish.sampleGoodsBg ? `url("${furnish.sampleGoodsBg}")` : '',
}));
const formalBg = computed(() => ({
  backgroundImage: furnish.formalBg ? `url("${furnish.formalBg}")` : '',
}));
const formalGoodsBg = computed(() => ({
  backgroundImage: furnish.formalGoodsBg ? `url("${furnish.formalGoodsBg}")` : '',
}));
const returnTopBtn = computed(() => ({
  backgroundImage: furnish.returnTopBtn ? `url("${furnish.returnTopBtn}")` : '',
}));
const myPrizeBtn = computed(() => ({
  backgroundImage: furnish.myPrizeBtn ? `url("${furnish.myPrizeBtn}")` : '',
}));
const orderBtn = computed(() => ({
  backgroundImage: furnish.orderBtn ? `url("${furnish.orderBtn}")` : '',
}));
const ruleBtn = computed(() => ({
  backgroundImage: furnish.ruleBtn ? `url("${furnish.ruleBtn}")` : '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  thresholdBg,
  seriesTabNotAct,
  seriesTabAct,
  sampleBg,
  sampleGoodsBg,
  formalBg,
  formalGoodsBg,
  returnTopBtn,
  myPrizeBtn,
  orderBtn,
  ruleBtn,
};
