<template>
    <div class="box">
      <div class='dialog'>
        <div class="join-text">
          <span>您还不是店铺会员</span><br>
          <span>本活动仅适用于<br>{{baseInfo.shopName}}会员</span><br>
          <span>请先加入会员解锁</span>
        </div>
        <div class="confirm-btn" @click="openCard"/>
        <div class="cancel-btn" @click="closeDialog"/>
      </div>
    </div>
</template>

<script setup lang='ts'>
import { computed, defineEmits, inject } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
});
const isShowPopup = computed(() => props.showPopup);

const openCard = async () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const emits = defineEmits(['closeDialog']);

const closeDialog = () => {
  emits('closeDialog');
};
</script>
<style lang='scss' scoped>
.box {
  .dialog {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/284849/8/21699/129722/6800aafbFffd84f38/e6cf1dd2ade6c0b7.png) no-repeat;
    background-size: 100%;
    width: 7.02rem;
    height: 9.8rem;
    background-repeat: no-repeat;
    position: relative;
    .join-text{
      width: 5.3rem;
      font-size: 0.26rem;
      position: absolute;
      height: 2rem;
      line-height: 0.4rem;
      top: 3rem;
      left: 1.1rem;
      text-align: center;
      color: #865d07;
      font-weight: 600;
    }
    .confirm-btn{
      font-size: 0.26rem;
      position: absolute;
      width: 3rem;
      height: 0.67rem;
      top: 5.7rem;
      left: 2.3rem;
    }
    .cancel-btn{
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/281515/2/20664/1823/67fe160aF8aff0f7c/58b1e21a2ee3ea1d.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%);
      width: .8rem;
      height: .8rem;
      border-radius: 50%;
    }
  }
}

</style>
