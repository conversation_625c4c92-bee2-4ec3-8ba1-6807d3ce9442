import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData2 = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/272917/16/17211/2663319/67f39b88Fd5558aaa/6981bdda3bffa0c2.png', // 页面背景图
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/279437/9/16713/158208/67f39b87F38c69a6c/34da2b5e5226424a.png', // 主KV
  shopNameColor: '#72421f', // 店铺名称颜色
  orderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/277818/34/17518/3979/67f48841F3932f2f6/60a6af6598b75bd1.png', // 订单按钮
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/275711/10/17321/4237/67f48841F432d185c/2127a4cb057cc40a.png', // 活动规则按钮
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/273396/28/16997/3990/67f48840Fcf82b9e3/92e117994e2932ba.png', // 我的奖品按钮
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/283413/4/21009/9796/67ff6ec3Fd4bce6df/bdedf23e5dbddcbe.png', // 机制背景图
  receivePrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/280385/30/16123/13076/67f48eeeF44aa5118/961c019647d82d2d.png', // 领取奖品按钮
  receivePrizeBgGray: '//img10.360buyimg.com/imgzone/jfs/t1/310886/19/6450/4596/683fa8a0F03a094ae/97655ab2d93f4e21.png', // 领取奖品按钮置灰
  seriesTabAct: '//img10.360buyimg.com/imgzone/jfs/t1/274226/6/16774/1465/67f49743F22eb707f/957f60b6a1ca66e8.png', // 系列tab选中
  seriesTabNotAct: '//img10.360buyimg.com/imgzone/jfs/t1/282184/10/16402/1321/67f50a71Fe929d7d4/413dea364707f61e.png', // 系列tab未选中
  sampleBg: '//img10.360buyimg.com/imgzone/jfs/t1/278433/24/16750/10568/67f49744Fbb3aeb89/a55f97bf3eaec9c4.png', // 试用装背景框
  sampleGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/274332/13/17419/27900/67f49743F397054cd/6a6b9b9e16d0159d.png', // 试用装商品背景框
  formalBg: '//img10.360buyimg.com/imgzone/jfs/t1/271749/19/17108/6690/67f49743Fca6e98a8/510f12f954317376.png', // 正装背景框
  formalGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/276432/13/17290/27962/67f49742F0427f551/875a828ec4185694.png', // 正装商品背景框
  returnTopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/274085/12/16974/7437/67f4b716F8e135d0c/d7ef521769219caf.png', // 返回顶部按钮
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/277886/5/17571/29849/67f4bd0cF3d4faa4c/2db6474c2c367f2b.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/280316/37/16415/9157/67f4bd91F32bfbba1/cb64674d9fa1901a.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/274857/1/17606/43963/67f4bde3Fa7f9cfd7/d98f7c96ec28dad7.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '零元试喝';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
