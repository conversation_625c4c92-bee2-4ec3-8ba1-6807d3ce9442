<template>
  <div class="rule-bk">
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="name">{{ item.prizeName }}</div>
        <div class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        <div class="status">
          <div v-if="item.winStatus === 0">发放失败</div>
          <div v-else-if="item.prizeType === 3">
            <div v-if="!item.deliveryStatus">待发货</div>
            <div v-else>已发货</div>
          </div>
          <div v-else>已发放</div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无获奖记录哦~</div>
    </div>
  </div>

  <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import Clipboard from 'clipboard';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
  recordId: number;
  deliverName: string;
  deliverNo: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90007/getUserPrizes');
    closeToast();
    // res.data.forEach((item: any) => {
    //   item.prizeType = Number(item.prizeType);
    // });
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {

  addressId.value = item.addressId;
  activityPrizeId.value = item.activityPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userPrizeId, prizeContent.result.planDesc);
};
</script>

<style scoped lang="scss">
.rule-bk {
  background: url(../assets/myPrize.png) no-repeat;
  background-size: 100%;
  width: 6.8rem;
  height: 8rem;
  padding: 1.8rem 0.65rem 1.45rem;

  .content {
    height: 100%;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #fff;
    white-space: pre-wrap;

    .prize {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.1rem 0.1rem;
      .name {
        flex: 1;
      }
      .time {
        flex: 0.7;
        text-align: center;
      }
      .status {
        flex: 1;
        text-align: right;
      }
    }

    .no-data {
      text-align: center;
      padding-top: 2rem;
      font-size: 0.24rem;
      color: #fff;
    }
  }
}
</style>
