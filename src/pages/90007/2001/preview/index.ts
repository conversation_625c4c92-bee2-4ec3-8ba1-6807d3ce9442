import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const activityData2 = {
  perReceiveLimit: false,
  shopName: '广博长泽专卖店',
  orderStatus: 'FINISH',
  rules: '集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2集罐赢好礼-自动结束2',
  threshold: 1,
  templateCode: 2001,
  receiveTimeType: 0,
  orderFilterToggle: 1,
  activityId: '1775412446864674818',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  receivePointRangeDate: [
    '2024-04-16T09:58:09.000Z',
    '2024-04-16T16:58:09.000Z',
  ],
  startTime: '2024-04-16 17:58:09',
  exposureSkuList: [

  ],
  rangeDate: [
    '2024-04-16T09:58:09.000Z',
    '2024-04-16T16:58:09.000Z',
  ],
  gradeLabel: [

  ],
  diffSeriesReceiveLimit: true,
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  activityName: '集罐赢好礼-自动结束2',
  supportLevels: '1,2,3,4,5',
  orderRangeDate: [
    '2024-04-01T09:58:42.000Z',
    '2024-04-30T09:58:42.000Z',
  ],
  seriesList: [
    {
      seriesPrizeList: [
        {
          potNum: 1,
          quantityTotal: 100,
          endDate: 1715702400000,
          isBinding: 0,
          planName: '测试-红包',
          planStatus: 2,
          prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/214055/33/4729/14449/61946651E1ce8e563/d568024bdefb6ffa.png',
          quantityUsed: 13,
          packetDataTime: [
            1713235320000,
            1715702400000,
          ],
          planId: '01baafb4e2874bbdae123e3b33ac54db',
          hongBaoExpireType: 2,
          unitPrice: '0.10',
          amount: 93,
          sendTotalCount: 1,
          aveAmount: 10,
          prizeType: 6,
          unitCount: 1,
          activityName: '',
          quantityRemain: 87,
          version: 0,
          prizeKey: '01baafb4e2874bbdae123e3b33ac54db',
          itemId: 15,
          dayLimit: 1,
          dayLimitType: 1,
          prizeName: '测试-红包',
          createTime: 1713234738000,
          userDayCount: 100,
          planContent: '12',
          userActivityCount: 100,
          hongBaoExpireDay: 30,
          startDate: 1713235320000,
        },
        {
          potNum: 1,
          quantityTotal: 100,
          endDate: 1715702400000,
          isBinding: 0,
          planName: '测试-红包',
          planStatus: 2,
          prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/214055/33/4729/14449/61946651E1ce8e563/d568024bdefb6ffa.png',
          quantityUsed: 13,
          packetDataTime: [
            1713235320000,
            1715702400000,
          ],
          planId: '01baafb4e2874bbdae123e3b33ac54db',
          hongBaoExpireType: 2,
          unitPrice: '0.10',
          amount: 93,
          sendTotalCount: 1,
          aveAmount: 10,
          prizeType: 6,
          unitCount: 1,
          activityName: '',
          quantityRemain: 87,
          version: 0,
          prizeKey: '01baafb4e2874bbdae123e3b33ac54db',
          itemId: 15,
          dayLimit: 1,
          dayLimitType: 1,
          prizeName: '测试-红包',
          createTime: 1713234738000,
          userDayCount: 100,
          planContent: '12',
          userActivityCount: 100,
          hongBaoExpireDay: 30,
          startDate: 1713235320000,
        },
        {
          potNum: 1,
          quantityTotal: 100,
          endDate: 1715702400000,
          isBinding: 0,
          planName: '测试-红包',
          planStatus: 2,
          prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/214055/33/4729/14449/61946651E1ce8e563/d568024bdefb6ffa.png',
          quantityUsed: 13,
          packetDataTime: [
            1713235320000,
            1715702400000,
          ],
          planId: '01baafb4e2874bbdae123e3b33ac54db',
          hongBaoExpireType: 2,
          unitPrice: '0.10',
          amount: 93,
          sendTotalCount: 1,
          aveAmount: 10,
          prizeType: 6,
          unitCount: 1,
          activityName: '',
          quantityRemain: 87,
          version: 0,
          prizeKey: '01baafb4e2874bbdae123e3b33ac54db',
          itemId: 15,
          dayLimit: 1,
          dayLimitType: 1,
          prizeName: '测试-红包',
          createTime: 1713234738000,
          userDayCount: 100,
          planContent: '12',
          userActivityCount: 100,
          hongBaoExpireDay: 30,
          startDate: 1713235320000,
        },
        {
          potNum: 1,
          quantityTotal: 100,
          endDate: 1715702400000,
          isBinding: 0,
          planName: '测试-红包',
          planStatus: 2,
          prizeImg: '//img10.360buyimg.com/imgzone/jfs/t1/214055/33/4729/14449/61946651E1ce8e563/d568024bdefb6ffa.png',
          quantityUsed: 13,
          packetDataTime: [
            1713235320000,
            1715702400000,
          ],
          planId: '01baafb4e2874bbdae123e3b33ac54db',
          hongBaoExpireType: 2,
          unitPrice: '0.10',
          amount: 93,
          sendTotalCount: 1,
          aveAmount: 10,
          prizeType: 6,
          unitCount: 1,
          activityName: '',
          quantityRemain: 87,
          version: 0,
          prizeKey: '01baafb4e2874bbdae123e3b33ac54db',
          itemId: 15,
          dayLimit: 1,
          dayLimitType: 1,
          prizeName: '测试-红包',
          createTime: 1713234738000,
          userDayCount: 100,
          planContent: '12',
          userActivityCount: 100,
          hongBaoExpireDay: 30,
          startDate: 1713235320000,
        },
      ],
      seriesUrl: '12',
      seriesSkuList: [
        {
          potNum: 12,
          skuId: 12345678,
        },
        {
          potNum: 12,
          skuId: 121212,
        },
        {
          potNum: 12,
          skuId: 121213,
        },
        {
          potNum: 12,
          skuId: 12,
        },
      ],
      seriesName: 'DD',
      seriesPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/172172/35/45086/61120/661e6cccF9a510bdc/de20f61834e6b699.png',
    },
  ],
  perReceiveCount: 0,
  sameSeriesReceiveLimit: true,
  shareTitle: '集罐赢好礼，超多惊喜大奖等你来领！',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  prizeDay: [

  ],
  isExposure: 0,
  endTime: '2024-04-17 00:58:09',
};
const _decoData = {
  pageBg: '',
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/157164/36/41307/226408/661e6ccaF259a19ab/d2ee3050ad7ad619.png', // 主页背景图
  actBgColor: '#f6e1ae', // 主页背景色
  shopNameColor: '#72421f', // 店铺名称颜色
  btnColor: '#72421f', // 按钮字体颜色
  btnBg: '#ffefc5', // 按钮背景颜色
  btnBorderColor: '#dc9d57', // 按钮边框颜色
  acIntroductionBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/105489/37/41355/336649/661e6ccfFbcaf2c81/83125d6b0de569af.png', // 活动介绍背景图
  seriesGoodsBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/149666/32/42717/46182/661c8223F74f6ee23/e0c2158174fdc23a.png', // 系列图
  buyBtnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/232333/30/14704/8303/661e6ccfFa6c4a895/4535655b2f5a5721.png', // 继续集罐按钮
  exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/157504/16/41149/5738/661e6cd0Fa3687e43/80c1d6d0a0052668.png', // 兑换按钮
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/198727/40/18336/5096/619b80b9Eafb4de86/6e8012f498dd80d1.png', // 曝光商品头部
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '集罐有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
