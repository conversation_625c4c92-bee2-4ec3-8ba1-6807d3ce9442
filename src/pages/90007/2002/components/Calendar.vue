<template>
  <div class="calendar">
    <div v-for="(item, index) in calendarData" class="calendar-item" :key="index" :class="{ ash: item.color === 'ash', date: index > 6 && item.color !== 'ash' }">
      <div :class="getSigned(item)">
        <div :class="index <= 6 ? 'calendar-title' : 'calendar-date'">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { ref, watch } from 'vue';

const props = withDefaults(defineProps<{ signList: number[] }>(), {
  signList: () => [],
});

interface CalendarProps {
  label: string;
  full?: string;
  color?: string;
  show?: boolean;
}
const calendarData = ref<Array<CalendarProps>>([{ label: '日' }, { label: '一' }, { label: '二' }, { label: '三' }, { label: '四' }, { label: '五' }, { label: '六' }]);
const monthMap = {
  1: 'January',
  2: 'February',
  3: 'March',
  4: 'April',
  5: 'May',
  6: 'June',
  7: 'July',
  8: 'August',
  9: 'September',
  10: 'October',
  11: 'November',
  12: 'December',
};
const year = ref(0); // 当前日期年
const month = ref(0); // 当前日期月数
const date = ref(0); // 当前日期号数
const day = ref(0); // 当前星期几
const today = dayjs().format('YYYYMMDD');
const signList = ref(props.signList);

const getSigned = (item: CalendarProps) => {
  const result = signList.value.find((e) => e === Number(item.full));
  if (item.full === today && result && !item.color) {
    return 'calendar-border calendar-bg';
  }
  if (item.full === today && !result && !item.color) {
    return 'calendar-border';
  }
  if (result && !item.color) {
    return 'calendar-bg';
  }
  return '';
};
const getNow = () => {
  const now = new Date();
  year.value = +now.getFullYear();
  month.value = +now.getMonth() + 1;
  date.value = +now.getDate();
  day.value = +now.getDay();
};
// 获取每个月的天数
const monthDay = (currentMonth: number): number => {
  if ([1, 3, 5, 7, 8, 10, 12].includes(currentMonth)) {
    return 31;
  }
  if ([4, 6, 9, 11].includes(currentMonth)) {
    return 30;
  }
  if (currentMonth === 2) {
    // 判断当年是否为闰年
    if ((year.value % 4 === 0 && year.value % 100 !== 0) || year.value % 400 === 0) {
      return 29;
    }
    return 28;
  }
  return 0;
};
const setZero = (val: number) => {
  if (val < 10) {
    return `0${val}`;
  }
  if (val >= 10) {
    return val;
  }
  return '';
};

const getCalendarDate = () => {
  // 获取当前月份一号星期几
  const firstDay = new Date(`${year.value}/${month.value}/01`).getDay();
  calendarData.value = [{ label: '日' }, { label: '一' }, { label: '二' }, { label: '三' }, { label: '四' }, { label: '五' }, { label: '六' }];
  const num = firstDay;
  const nowDays = monthDay(month.value);
  const lastMonth = month.value - 1 > 0 ? month.value - 1 : 12;
  const lastDays = monthDay(lastMonth);
  // 循环添加上一个月数据
  for (let i = 0; i < num; i += 1) {
    calendarData.value.push({
      label: String(lastDays - num + i + 1),
      full: `${month.value === 1 ? year.value - 1 : year.value}${setZero(month.value === 1 ? 12 : month.value - 1)}${setZero(lastDays - num + i + 1)}`,
      color: 'ash',
    });
  }
  // 循环添加当月数据
  for (let i = 0; i < nowDays; i += 1) {
    calendarData.value.push({
      label: String(i + 1),
      full: `${year.value}${setZero(month.value)}${setZero(i + 1)}`,
    });
  }
  // 循环添加下一个月数据
  if (calendarData.value.length % 7 !== 0) {
    const surplusDay = 7 - (calendarData.value.length % 7);
    for (let i = 0; i < surplusDay; i += 1) {
      calendarData.value.push({
        label: String(i + 1),
        full: `${month.value === 12 ? year.value + 1 : year.value}${setZero(month.value === 12 ? 1 : month.value + 1)}${setZero(i + 1)}`,
        color: 'ash',
      });
    }
  }
};

watch(props.signList, () => {
  signList.value = props.signList;
  getCalendarDate();
});

getNow();
getCalendarDate();
</script>

<style scoped lang="scss">
.calendar {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  box-sizing: border-box;
  width: 6.6rem;
  height: 5.15rem;
  .calendar-item {
    box-sizing: border-box;
    width: 0.9rem;
    height: 0.6rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .calendar-border {
      position: relative;
      height: 0.6rem;
      width: 0.6rem;
      text-align: center;
      line-height: 0.6rem;
      .calendar-date {
        color: #8b85ff;
      }
      &::after {
        content: '今天';
        display: block;
        width: 0.6rem;
        color: #fff;
        font-size: 0.16rem;
        line-height: 0.26rem;
        background-color: #8b85ff;
        border-radius: 0.13rem;
        position: absolute;
        left: 0;
        bottom: -0.15rem;
      }
    }
    .calendar-bg {
      height: 0.6rem;
      width: 0.6rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/138694/25/21669/5016/619f3741E16169627/490a0e75626bcaeb.png) no-repeat;
      background-size: 100%;

      .calendar-date {
        color: white;
      }
    }
    .calendar-title {
      font-size: 0.24rem;
      color: rgba(0, 0, 28, 0.5);
    }
    .calendar-date {
      font-size: 0.26rem;
      color: rgba(0, 0, 28, 0.7);
    }
  }

  .ash {
    div {
      color: transparent !important;
    }
  }
}
</style>
