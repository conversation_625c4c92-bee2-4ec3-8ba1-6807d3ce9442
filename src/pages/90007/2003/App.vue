<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="kv">
      <div class="ac-time" :style="furnishStyles.shopNameColor.value">活动时间:{{ dayjs(baseInfo.startTime).format('YYYY/MM/DD') }} -{{ dayjs(baseInfo.endTime).subtract(15, 'day').format('YYYY/MM/DD') }}</div>
      <div class="kv-btn" :style="furnishStyles.headerBtn.value" @click="isShowRuleDialog = true">活动规则</div>
      <div class="kv-btn prize" :style="furnishStyles.headerBtn.value" @click="isShowPrizeDialog = true">我的奖品</div>
      <div class="kv-btn history" v-if="furnish.lastActivityLink" :style="furnishStyles.headerBtn.value" @click="gotoHistoryPage">上期回顾</div>
    </div>
    <div class="content" v-for="series in seriesList" :key="series.seriesId">
      <div class="progress" :style="{ backgroundImage: `url(${furnish.seriesBoxBkHead})` }">
        <div class="progress-bar" :style="{ backgroundImage: `url(${furnish.seriesBoxBkBody})`, width: progressWidth(series) + '%' }"></div>
      </div>
      <div class="gift" :style="{ '--drawTextColor': furnish.drawTextColor, '--drawBtnColor': furnish.drawBtnColor, '--drawBtnBg': furnish.drawBtnBg }">
        <div class="gift-item" v-for="item in series?.prizes" :key="item.prizeKey">
          <div class="gift-step">累计满{{ item.standardNum }}罐</div>
          <img class="gift-icon" :src="furnish.seriesBoxBkFooter ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/196280/1/44570/3998/662f9d67F38a9f591/a809b7b3cdbb4a9a.png'" alt="" />
          <div class="gift-img">
            <img :src="item.prizeImg" alt="奖品图" />
          </div>
          <div class="gift-name">{{ item?.prizeName }}</div>
          <div class="gift-remainder">(奖品剩余{{ item.stockNum }}份)</div>
          <div class="gift-btn" :class="{ 'item-btn-disabled': !item.canClick }" @click="handleExchange(item, series.seriesId)">立即领取</div>
        </div>
      </div>
      <div class="user-info" :style="{ color: furnish.drawTextColor }">您已购买{{ series?.userTotalPotNum }}罐，已使用{{ series?.userUsedPotNum }}罐，剩余{{ series?.userPotNum }}罐</div>
    </div>
    <div class="act-info" :style="{ color: furnish.actTipsColor }">{{ furnish.actTips }}</div>
    <div class="content sku" v-if="exposureSkuList.length">
      <div class="title" :style="{ color: furnish.skuTextColor }">活动商品</div>
      <div class="til">
        <icon name="play" />
        购买以下商品即可参加活动
      </div>
      <div class="sku-list">
        <div class="sku-item" v-for="item in exposureSkuList" :key="item.skuId">
          <img class="sku-img" :src="item.skuMainPicture" alt="" />
          <img class="sku-btn" @click="gotoSkuPage(item.skuId)" :src="furnish.buyBtnBg ?? '//img10.360buyimg.com/imgzone/jfs/t1/240414/32/8098/6624/662fa5caFb406adba/582c4c9a431dd1ad.png'" alt="" />
        </div>
      </div>
      <div class="sku-tips">上下滑动查看更多》</div>
    </div>
    <img class="go-shop" @click="gotoShopPage(baseInfo.shopId)" :src="furnish.exchangeBtn || '//img10.360buyimg.com/imgzone/jfs/t1/167918/30/43384/10450/662fa766F407f9fd8/8fc1732f65cd2830.png'" alt="" />
  </div>
  <my-prize :isShowPopup="isShowPrizeDialog" @closeDialog="isShowPrizeDialog = false" @changeAddress="changeAddress"></my-prize>
  <open-card :isShowPopup="isShowOpenCard" @closeDialog="isShowOpenCard = false"></open-card>
  <act-rule :isShowPopup="isShowRuleDialog" @closeDialog="isShowRuleDialog = false"></act-rule>
  <confirm-redemption @confirmFun="handleConfirmExchange" :isShowPopup="isShowConfirmDialog" @closeDialog="isShowConfirmDialog = false"></confirm-redemption>
  <get-result :isShowPopup="isShowGetResult" @saveAddress="(isShowAddress = true), (isShowGetResult = false)" :giftInfo="giftInfo" :isShowSuccess="isGetSuccess" @closeDialog="isShowGetResult = false"></get-result>
  <save-address :isShowPopup="isShowAddress" :giftInfo="giftInfo" @closeDialog="isShowAddress = false"></save-address>
</template>

<script lang="ts" setup>
import './assets/style.scss';
import { computed, inject, reactive, ref } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import { gotoSkuPage, gotoShopPage } from '@/utils/platforms/jump';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { closeToast, showLoadingToast, showToast, Icon } from 'vant';
import { httpRequest } from '@/utils/service';
import type { Sku, Series } from './ts/type';
import dayjs from 'dayjs';
import OpenCard from './components/OpenCard.vue';
import ActRule from './components/ActRule.vue';
import ConfirmRedemption from './components/ConfirmRedemption.vue';
import GetResult from './components/GetResult.vue';
import MyPrize from './components/MyPrize.vue';
import SaveAddress from './components/SaveAddress.vue';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;
const successPopup = ref(false);
const isShowPrizeDialog = ref(false);
const isShowAddress = ref(false);
//  兑换奖品信息
const giftInfo = ref<any>({});
const progressWidth = computed(() => (series: Series) => {
  let progress = 0;
  if (series.prizes.length === 1) {
    progress = Math.min((series.userPotNum / series.prizes[0].standardNum) * 100, 99);
  }
  if (series.prizes.length === 2) {
    progress = Math.min((series.userPotNum / series.prizes[0].standardNum) * 100, 99);
    if (series.userPotNum < series.prizes[0]?.standardNum) {
      progress = Math.min((series.userPotNum / series.prizes[0]?.standardNum) * (1 / series.prizes.length / 2) * 100, 99);
    }
    if (series.userPotNum >= series.prizes[series.prizes.length - 1]?.standardNum) {
      progress = 99;
    }
    const index = series?.prizes.findIndex((item) => item.standardNum >= series.userPotNum);
    if (index === 0) {
      progress = Math.min((series.userPotNum / series.prizes[0].standardNum) * (1 / series.prizes.length / 2) * 100, 99);
    } else if (index === 1) {
      progress = Math.min((series.userPotNum / series.prizes[series.prizes.length - 1].standardNum) * 100, 99);
    }
  }
  if (series.prizes.length === 3) {
    if (series.userPotNum < series.prizes[0]?.standardNum) {
      progress = Math.min((series.userPotNum / series.prizes[0]?.standardNum) * (1 / series.prizes.length / 2) * 100, 99);
    }
    if (series.userPotNum >= series.prizes[series.prizes.length - 1]?.standardNum) {
      progress = 99;
    }
    const index = series?.prizes.findIndex((item) => item.standardNum >= series.userPotNum);
    if (index === 0) {
      progress = Math.min((series.userPotNum / series.prizes[0].standardNum) * (1 / series.prizes.length / 2) * 100, 99);
    } else if (index === 1) {
      progress = Math.min((series.userPotNum / series.prizes[series.prizes.length - 1].standardNum - 1 / series.prizes.length / 2) * 100, 99);
    } else if (index === 2) {
      progress = Math.min((series.userPotNum / series.prizes[series.prizes.length - 1].standardNum) * 100, 99);
    }
  }
  return progress;
});
const isShowRuleDialog = ref(false);
const isShowConfirmDialog = ref(false);
const isShowGetResult = ref(false);
const isGetSuccess = ref(true);
const shopName = ref(baseInfo.shopName);
const isShowOpenCard = ref(false);
const gotoHistoryPage = () => {
  window.location.href = furnish.lastActivityLink;
};
const isLoadingFinish = ref(false);
// 系列列表
const seriesList = ref<any[]>([]);

let newPrizeObj = {
  prizeName: '',
  prizeImg: '',
  prizeType: 0,
};
interface Prize {
  writeAddress: boolean;
  prizeType: string;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  winStatus: number;
  recordId: string;
}
// 从我的奖品修改地址信息
const changeAddress = (prize: Prize) => {
  console.log('🚀 ~ changeAddress ~ prize:', prize);
  giftInfo.value = prize;
  isShowAddress.value = true;
  isShowPrizeDialog.value = false;
};
// 保存实物地址相关
const showSaveAddress = ref(false);
const addressForm = ref({});
// 计算用户罐数
const calcUserPotNum = () => {
  httpRequest
    .post('/90007/computePinSeriesPotNum')
    .then((res) => {
      if (res.code === 200) {
        httpRequest.post('/90007/getSeriesPrizes').then((res) => {
          if (res.code === 200) {
            seriesList.value = res.data;
          }
        });
      }
    })
    .catch((err) => {
      httpRequest.post('/90007/getSeriesPrizes').then((res) => {
        if (res.code === 200) {
          seriesList.value = res.data;
        }
      });
    });
};
const exposureSkuList = ref<Sku[]>([]);

// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/90007/getSkuListPage', {
      type: 0,
      pageNum: 1,
      pageSize: 20,
    });
    console.log('sku', data);

    exposureSkuList.value.push(...data.records);
  } catch (error: any) {
    console.error(error);
  }
};

const init = async () => {
  console.log(decoData);

  if (baseInfo.thresholdResponseList.some((item) => item.type === 1)) {
    isShowOpenCard.value = true;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([calcUserPotNum(), getSkuList()]);
    isLoadingFinish.value = true;

    closeToast();
  } catch (error: any) {
    console.error(error);

    closeToast();
  }
};
// 确认兑换
const handleConfirmExchange = () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  httpRequest
    .post('/90007/cashPrize', {
      prizeKey: giftInfo.value.prizeKey,
      seriesId: giftInfo.value.seriesId,
    })
    .then((data) => {
      closeToast();
      if (data.code === 200) {
        calcUserPotNum();
        // showToast('兑换成功，请等待发放');
        init();
        isShowConfirmDialog.value = false;
        isGetSuccess.value = true;
        isShowGetResult.value = true;
        giftInfo.value.addressId = data.data;
      }
    })
    .catch((err) => {
      closeToast();
      init();
      isShowConfirmDialog.value = false;
      isGetSuccess.value = false;
      isShowGetResult.value = true;
      console.log(err);
      //   showToast(err.message);
    });
};

// 继续集罐
const goToShop = (url: string) => {
  if (baseInfo.status === 1) {
    showToast({
      message: '活动暂未开始',
      duration: 2000,
    });
    return;
  }
  if (baseInfo.status === 3) {
    showToast({
      message: '抱歉，活动已结束',
      duration: 2000,
    });
    return;
  }
  window.location.href = url;
};

// 立即兑换
const handleExchange = (prizeObj: any, series: number) => {
  console.log(prizeObj);
  if (baseInfo.status === 1) {
    showToast({
      message: '活动未开始',
      duration: 2000,
    });
    return;
  }
  if (baseInfo.status === 3) {
    showToast({
      message: '抱歉，活动已结束',
      duration: 2000,
    });
    return;
  }
  if (prizeObj.stockNum <= 0) {
    showToast({
      message: '奖品已兑完',
      duration: 2000,
    });
    return;
  }
  if (prizeObj.canClick) {
    giftInfo.value = prizeObj;
    giftInfo.value.prizeKey = prizeObj.prizeKey;
    giftInfo.value.seriesId = series;
    newPrizeObj = prizeObj;
    isShowConfirmDialog.value = true;
  } else {
    showToast({
      message: `${prizeObj.reason}`,
      duration: 2000,
    });
  }
};

init();
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}
</style>
<style lang="scss">
@font-face {
  font-family: 'FZZXHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZZhengHei/FZZXHJW.TTF');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
body {
  font-family: 'FZZXHJW', sans-serif;
}
</style>
