<template>
  <!--不满足参与条件（有前置订单等）-->
  <div class="rule-bk" :style="furnishStyles.canNotPop.value">
    <div class="btn" @click="jump"></div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const jump = () => {
  window.location.href = furnish.canNotPopLink;
};
</script>

<style scoped lang="scss">
.close {
  width: 0.45rem;
  height: 0.45rem;
  margin: 0.2rem auto;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/226770/28/11313/1145/65969c51Fb9dda6aa/31b3866cf538306e.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.rule-bk {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.5rem;
  height: 8rem;
  padding:1rem 0 0 0;
  position:relative;
  .btn {
    width: 2.45rem;
    height: 0.43rem;
    border-radius: 0.21rem;
    position:absolute;
    top: 5.4rem;
    left: 2rem;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none;
}
</style>
