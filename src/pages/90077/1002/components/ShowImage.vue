<template>
    <VanPopup teleport='body' v-model:show='isShowPopup' @click-overlay="closeDialog">
      <div class="box">
        <div class="close" @click="closeDialog">X</div>
        <img style="width: 100%" :src="picUrl" alt="">
      </div>
    </VanPopup>
</template>

<script setup lang='ts'>
import { computed, defineEmits, defineProps, inject, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  picUrl: {
    type: String,
    required: true,
    default: '',
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  emits('closeDialog');
};
</script>
<style lang='scss' scoped>
.close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.2rem;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  cursor: pointer;
}
.box {
  max-height: 100vh;
  overflow-y: scroll;
}
</style>
