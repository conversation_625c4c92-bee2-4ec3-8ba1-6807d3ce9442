import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/237377/22/33288/138591/67b43bf3Ff8e77ce9/b05555347e43d2ea.png',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/226128/40/32720/901779/67b449c8Ff407b768/ddcf8cea09fc5413.png',
  actBgColor: '#8cc4fd',
  shopNameColor: '#000000',
  btnColor: '#ffffff',
  btnBg: '#ed284c',
  btnBorderColor: '#ffffff',
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/228409/2/26049/2796/66f2579fF9c441cf7/fd9b2919286458b8.png',
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/109613/37/54418/2816/66f2579bF9ff70d45/0ade54b05c382e0f.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/264744/36/21215/723134/67b449c3F7ab9e523/f9d2cc508ca34dbe.png',
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/235032/2/25528/19123/66da604fF29785a03/7708ce8046e2250f.png',
  getPrizeGrayBtn: '//img10.360buyimg.com/imgzone/jfs/t1/149518/23/42554/12205/66da5e92Fa31db838/b6a7ffd840679bbe.png',
  prizeNameColor: '#ffffff',
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/259777/22/21329/985538/67b449c1F92962c0d/42b1a440d322fa51.png',
  priceColor: '#ffffff',
  btnToShop: '//img10.360buyimg.com/imgzone/jfs/t1/238149/5/25860/77052/66f2579fFb11f61ed/0dcb3d4dd5e2d255.png',
  canNotCloseJoinPopup: '1',
  jumpUrl: '',
  isShowJump: true,
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/95574/17/49540/74784/66f26ed7F48a96f57/505a29ee0f7ec6b6.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/179591/6/48959/254680/66f26e5fF8fced1db/fba20831c711519e.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/248029/24/19638/33997/66f26dfaF3eb526c2/ea5a80bebbbb9147.png',
  hotZoneList: [],
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '新单有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('decoData', a);
  app.provide('isPreview', true);
  app.mount('#app');
});
