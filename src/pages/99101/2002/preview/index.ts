import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/279596/1/9938/215285/67e3d107Ff12437a2/5e82b63afa81bd2a.png',
  actBg: '',
  actBgColor: '#ffdfa6',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/282103/1/9780/10715/67e3d10aF13e07809/c3813b9e406b4e21.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/275678/38/10383/11111/67e3d108F3b56c0ce/59ebd5c726835da4.png',
  stepBtnBg: '//img10.360buyimg.com/imgzone/jfs/t1/276392/22/10604/8903/67e3d107F2d1a4e51/e72263c5f1f3037a.png',
  stepBtnSelectBg: '//img10.360buyimg.com/imgzone/jfs/t1/276486/4/10618/8765/67e3d108F3fdf8dcd/af75d33ec91ffaed.png',
  exchangeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/282483/4/8854/11851/67e24321F1f5b3c09/920897062e6cd10e.png',
  skuListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/270626/15/10894/44256/67e3d10eFc3d8e1ef/1a4aca1ff81201e1.png',
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/283917/12/9603/8388/67e3d10fF0e1e483f/ff1c30fb126c18e8.png',
  skuTitleBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/282220/2/9807/7042/67e3d108F73d8d579/09eb61ce69a9fade.png',
  skuTitleColor: '#f8f1e0',
  goSkuBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/274522/17/10635/12578/67e3d10fF8c0b5e55/3ab1409e58ee0b13.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/270380/16/11374/57179/67e4b3daF28728f09/a349caffcc5d8781.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/284784/1/9548/11008/67e4b3dbFbc179659/3f0df3da18f5eff8.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/270483/23/10750/73707/67e4b3dbFc18bc588/f206296004a8be44.png',
  ruleImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274889/23/10597/1072746/67e3d10dFfe25b5fb/196b0328da7b6609.png',
};
const actData2 = {
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/8014/27/27673/8015/66e3f17eFd57d2e61/7aa4b43696214531.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/5404/18/24418/39038/66e3f17fFda46171c/a5d709264c89365f.png',
  rules: '测试',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/166070/15/46885/27256/66e3f17fF139817f3/ed6b59e0774f1a11.png',
  endTime: '2025-03-22 23:59:59',
  crowdBag: '',
  prizeDay: [

  ],
  shopName: '伊利母婴京东自营旗舰店',
  rangeDate: [
    '2025-02-20 00:00:00',
    '2025-03-22 23:59:59',
  ],
  startTime: '2025-02-20 00:00:00',
  threshold: 1,
  activityId: '1892419731947462658',
  gradeLabel: [

  ],
  limitOrder: 1,
  seriesPrizeList: [
    {
      seriesName: '1转2',
      seriesPic: 'https://img10.360buyimg.com/imgzone/jfs/t1/273077/25/6616/94048/67dbca97Ff2025ba0/f45e102c8d6960a9.jpg',
      beforeOptions: [
        1,
      ],
      afterOptions: 2,
      prizeList: [
        {
          prizeKey: 's250320162233349723',
          prizeType: 3,
          dayLimitType: 1,
          dayLimit: 1,
          prizeImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/262756/13/28314/7825/67c80318F618f9044/ae3ef908a85ce0eb.png',
          activityIds: [],
          createTime: 1742458953000,
          quantityAvailable: 3,
          quantityFreeze: 0,
          quantityPreDeliver: 0,
          quantityRemain: 3,
          quantityTotal: null,
          shopId: null,
          skuCode: 's250320162233349723',
          skuDetails: null,
          skuMainPicture: 'https://img10.360buyimg.com/imgzone/jfs/t1/262756/13/28314/7825/67c80318F618f9044/ae3ef908a85ce0eb.png',
          skuName: 'ceshisce',
          version: 1,
          wmsCode: null,
          prizeName: 'ceshisce',
          unitPrice: 1,
          unitCount: 1,
          sendTotalCount: 1,
          step: '1',
        },
      ],
      beforeSkuList: [
        {
          period: '一段',
          periodSort: 1,
          potNum: 1,
          skuId: '1234567',
          skuIdSort: 1,
        },
      ],
      afterSkuList: [
        {
          period: '二段',
          periodSort: 2,
          potNum: 2,
          skuId: '1',
          skuIdSort: 1,
        },
      ],
      stepSetting: [
        {
          step: 1,
          minPotNum: 1,
          maxPotNum: 5,
          stepImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/283460/16/6766/241821/67dd2967Ffa654bbf/cf37b8175d825990.png',
        },
      ],
    },
  ],
  shareTitle: '满额赢好礼，超多惊喜大奖等你来领！',
  joinEndTime: '',
  shareStatus: 1,
  activityName: '满额阶梯礼-2025-02-20',
  orderEndTime: '2025-03-22 23:59:59',
  templateCode: 2001,
  joinStartTime: '',
  joinTimeRange: [

  ],
  supportLevels: '1,2,3,4,5',
  orderStartTime: '2025-02-20 00:00:00',
  limitJoinTimeType: 0,
  actTotalReceiveCount: '',
  actLimitActTotalReceiveCount: 0,
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员转段赠好礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
