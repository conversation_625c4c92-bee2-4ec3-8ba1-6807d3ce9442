<template>
  <div class="rule-bk" :style="furnishStyles.saveAddressBk.value">
    <div class="content">
      <div class="form">
        <VanField label-align="right" v-model="form.realName" label="收货人：" maxlength="20"></VanField>
        <VanField label-align="right" v-model="form.mobile" label="手机号：" maxlength="11" type="number"></VanField>
        <VanField label-align="right" v-model="addressCode" label="省市区：" readonly @click="addressSelects = true"></VanField>
        <VanField label-align="right" v-model="form.address" label="详细地址：" maxlength="100"></VanField>
      </div>
      <div class="submit" @click="checkForm"></div>
    </div>

  </div>
  <div class="close" @click="close" />
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { reactive, ref, computed, PropType, onMounted } from 'vue';
import { areaList } from '@vant/area-data';
import { FormType } from '../ts/type';
import { httpRequest } from '@/utils/service';
import furnishStyles from '../ts/furnishStyles';

const props = defineProps(['userPrizeId', 'stepId', 'delay']);
const emits = defineEmits(['close', 'success', 'upLoadData']);

const close = () => {
  emits('close');
};

const addressSelects = ref(false);

const form: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

const addressCode = computed(() => {
  if (form.province && form.city && form.county) {
    return `${form.province}/${form.city}/${form.county}`;
  }
  return '';
});

const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  addressSelects.value = false;
};

const submit = async () => {
  try {
    if (props.stepId) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const { data } = await httpRequest.post('/99101/prize/draw', {
        ...form,
        stepId: props.stepId,
      });
      if (props.delay) {
        emits('success');
      } else {
        showToast('领取成功');
      }
      emits('upLoadData');
      emits('close', true);
    } else {
      showLoadingToast({
        message: '提交中...',
        forbidClick: true,
        duration: 0,
      });
      const { data } = await httpRequest.post('/99101/address/modify', {
        ...form,
        addressId: props.userPrizeId,
      });
      showToast('保存成功');
    }
    closeToast();
    emits('close');
  } catch (error: any) {
    showToast(error.message);
    setTimeout(() => {
      emits('upLoadData');
      emits('close');
    }, 1000);
  }
};

// 检查表单
const checkForm = () => {
  const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!form.realName) {
    showToast('请输入姓名');
  } else if (!form.mobile) {
    showToast('请输入电话');
  } else if (!phone.test(form.mobile)) {
    showToast('请输入正确的电话');
  } else if (!form.province) {
    showToast('请选择省市区');
  } else if (!form.address) {
    showToast('请输入详细地址');
  } else {
    submit();
  }
};
const getUserAddress = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/99101/query/address', {
      addressId: props.userPrizeId,
    });
    closeToast();
    Object.keys(form).forEach((key: string) => {
      form[key] = res.data[key];
    });
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
onMounted(() => {
  if (props.userPrizeId) {
    getUserAddress().then();
  }

});
</script>

<style scoped lang="scss">
.rule-bk {
  background-size: 100%;
  width: 6.54rem;
  height: 7.35rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.5rem;
  .content {
    padding: 0 0.5rem;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    width: 100%;
    .form {
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.25rem;
        background: transparent;
        padding: 0;
        color: #6a30d2;

        &::after {
          display: none;
        }
      }
    }

    .tip {
      font-size: 0.18rem;
      color: #b3b3b3;
    }

    .submit {
      text-align: center;
      font-size: 0.35rem;
      line-height: 0.8rem;
      color: #fff;
      width: 3.5rem;
      height: 0.8rem;
      margin: 1.2rem auto 0;
    }
  }
}
.close {
  width: 0.6rem;
  height: 0.6rem;
  background: url('../assets/closeBtn.png') no-repeat;
  background-size: 100%;
  margin: 0.2rem auto;
}
</style>
<style>
.van-field__control {
  height: 0.65rem;
  background-color: #fff;
  border-radius: 0.15rem;
  border: 0.01rem solid #6a30d2 !important;
  padding-left: .2rem;
  width: 3.8rem;
}
.van-field__label {
  color: #6a30d2 !important;
  font-size: 0.26rem;
  font-weight: bold;
  line-height: 0.65rem;
  width: 1.4rem;
  margin-right: 0;
}
</style>
