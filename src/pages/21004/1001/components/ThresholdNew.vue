<template>
  <div class="bg">
    <div class="content">
      {{text}}
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script setup lang="ts">
import { defineProps, ref } from 'vue';
import dayjs from 'dayjs';

const props = defineProps(['orderStartTime', 'actStartTime', 'actEndTime', 'fullGiftThreshold', 'maxParticipateNum']);
const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

const text = ref(`
      非常抱歉！
      您未满足活动条件，
      感谢您对CPB肌肤之钥的喜爱与支持。
      您不满足活动条件的原因如下：
      1. 参与人群
      首次加入CPB肌肤之钥京东自营官方旗舰店会员
      且该会员${dayjs(props.orderStartTime).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(props.actStartTime).format('YYYY-MM-DD HH:mm:ss')}
      未成功购买过正装商品
      2. 活动时间
      领取时间：${dayjs(props.actStartTime).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(props.actEndTime).format('YYYY-MM-DD HH:mm:ss')}
      限量${props.maxParticipateNum}份，每个ID限领1份，数量有限，先到先得
`);

</script>

<style scoped lang="scss">
.bg{
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/157325/30/37512/5761/65a7697cF9ff7894c/8488024fe71b2e31.png") no-repeat;
  background-size: 100%;
  width: 6.5rem;
  height: 6.5rem;
  margin: 0 auto;
  padding: 0.1rem 0.2rem 0.3rem 0.2rem;
  .content{
    text-align: center;
    white-space: pre-line;
    background: linear-gradient(to right, #513c28, #8d6a35, #513c28);
    -webkit-background-clip: text;
    color: transparent; /* 隐藏文字本身的颜色 */
    font-size: 0.23rem;
    line-height: 0.43rem;
  }
}
.close {
  width: 0.45rem;
  height: 0.45rem;
  margin: 0.3rem auto 0 auto;
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/226770/28/11313/1145/65969c51Fb9dda6aa/31b3866cf538306e.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
</style>
