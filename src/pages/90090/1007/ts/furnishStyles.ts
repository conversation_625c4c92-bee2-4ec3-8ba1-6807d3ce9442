import { computed, reactive } from 'vue';

export const furnish = reactive({
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  // 活动规则
  ruleBg: '',
  equitySelection: '',
  // 前置订单背景图
  preOrderBg: '',
  // 后置订单背景图
  postOrderBg: '',
  // 订单时间颜色
  orderTimeColor: '',
  // 前后订单中间Icon
  orderIcon: '',
  // 已完成订单Icon
  orderFinishIcon: '',
  // 未达标Icon
  orderUnfinishedIcon: '',
  // 达标Icon
  orderFinishedIcon: '',
  // 我的奖品
  myPrizeBg: '',
  // 奖励区域背景图
  prizeBg: '',
  // 资格提示/选购提示框
  qualificationBg: '',
  // 资格提示字体颜色
  qualificationTextColor: '',
  // 订单限制提示字体颜色
  orderLimitTextColor: '',
  // 进度条背景框
  progressBarBg: '',
  // 进度条上方字体颜色
  topTextColor: '',
  // 进度条下方字体颜色
  bottomTextColor: '',
  // 限时惊喜福利背景
  timeLimitedPrizeBg: '',
  // 限时惊喜福利字体颜色
  timeLimitedTextColor: '',
  // 限时惊喜福利区按钮
  timeLimitedBtnBg: '',
  timeLimitedGrayBtn: '',
  timeLimitedBtnTextColor: '',
  choosePrizeTitleColor: '',
  // N选M区域单个礼品背景
  prizeItemBg: '',
  // N选M区域单个礼品标题背景
  prizeItemTitleBg: '',
  // N选M区域单个礼品标题颜色
  prizeItemTitleColor: '',
  // N选M区域领取奖品按钮
  getPrizeBtn: '',
  // N选M区域领取奖品兑完按钮
  noMorePrizeBtn: '',
  // 闯关区域背景
  barrierBg: '',
  // 闯关区域领取按钮背景
  barrierGetBtn: '',
  // 闯关区域领取按钮字体颜色
  barrierBtnTextColor: '',
  // 参与活动商品背景
  showSkuBg: '',
  // 价格颜色
  priceColor: '',
  // 返回顶部
  btnToTop: '',
  jumpUrl: '',
  isShowJump: true,
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  canNotCloseJoinPopup: '',
  hotZoneList: [],
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));
const headerBtnRules = computed(() => ({
  backgroundImage: furnish.ruleBg ? `url("${furnish.ruleBg}")` : '',
}));
const headerBtnMyPrizes = computed(() => ({
  backgroundImage: furnish.myPrizeBg ? `url("${furnish.myPrizeBg}")` : '',
}));
const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));
const equitySelection = computed(() => ({
  backgroundImage: furnish.equitySelection ? `url("${furnish.equitySelection}")` : '',
}));
const prizeBg = computed(() => ({
  backgroundImage: furnish.prizeBg ? `url("${furnish.prizeBg}")` : '',
}));
const qualificationBg = computed(() => ({
  backgroundImage: furnish.qualificationBg ? `url("${furnish.qualificationBg}")` : '',
}));
const qualificationTextColor = computed(() => ({
  color: furnish.qualificationTextColor ?? '',
}));
const orderLimitTextColor = computed(() => ({
  color: furnish.orderLimitTextColor ?? '',
}));
const progressBarBg = computed(() => ({
  backgroundImage: furnish.progressBarBg ? `url("${furnish.progressBarBg}")` : '',
}));
const topTextColor = computed(() => ({
  color: furnish.topTextColor ?? '',
}));
const bottomTextColor = computed(() => ({
  color: furnish.bottomTextColor ?? '',
}));
const timeLimitedPrizeBg = computed(() => ({
  backgroundImage: furnish.timeLimitedPrizeBg ? `url("${furnish.timeLimitedPrizeBg}")` : '',
}));
const timeLimitedTextColor = computed(() => ({
  color: furnish.timeLimitedTextColor ?? '',
}));
const timeLimitedBtnBg = computed(() => ({
  backgroundImage: furnish.timeLimitedBtnBg ? `url("${furnish.timeLimitedBtnBg}")` : '',
  color: furnish.timeLimitedBtnTextColor ?? '',
}));
const timeLimitedGrayBtn = computed(() => ({
  backgroundImage: furnish.timeLimitedGrayBtn ? `url("${furnish.timeLimitedGrayBtn}")` : '',
}));
const choosePrizeTitleColor = computed(() => ({
  color: furnish.choosePrizeTitleColor ?? '',
}));
const prizeItemBg = computed(() => ({
  backgroundImage: furnish.prizeItemBg ? `url("${furnish.prizeItemBg}")` : '',
}));
const prizeItemTitleBg = computed(() => ({
  backgroundImage: furnish.prizeItemTitleBg ? `url("${furnish.prizeItemTitleBg}")` : '',
  color: furnish.prizeItemTitleColor ?? '',
}));
// const prizeItemTitleColor = computed(() => ({
//   color: furnish.prizeItemTitleColor ?? '',
// }));
const getPrizeBtn = computed(() => ({
  backgroundImage: furnish.getPrizeBtn ? `url("${furnish.getPrizeBtn}")` : '',
}));
const noMorePrizeBtn = computed(() => ({
  backgroundImage: furnish.noMorePrizeBtn ? `url("${furnish.noMorePrizeBtn}")` : '',
}));
const barrierBg = computed(() => ({
  backgroundImage: furnish.barrierBg ? `url("${furnish.barrierBg}")` : '',
}));
const barrierGetBtn = computed(() => ({
  backgroundImage: furnish.barrierGetBtn ? `url("${furnish.barrierGetBtn}")` : '',
  color: furnish.barrierBtnTextColor ?? '',
}));
const showSkuBg = computed(() => ({
  backgroundImage: furnish.showSkuBg ? `url("${furnish.showSkuBg}")` : '',
}));
const priceColor = computed(() => ({
  color: furnish.priceColor ?? '',
}));
const btnToTop = computed(() => ({
  backgroundImage: furnish.btnToTop ? `url("${furnish.btnToTop}")` : '',
}));
const preOrderBg = computed(() => ({
  backgroundImage: furnish.preOrderBg ? `url("${furnish.preOrderBg}")` : '',
  color: furnish.orderTimeColor ?? '',
}));
const postOrderBg = computed(() => ({
  backgroundImage: furnish.postOrderBg ? `url("${furnish.postOrderBg}")` : '',
  color: furnish.orderTimeColor ?? '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtnRules,
  headerBtnMyPrizes,
  equitySelection,
  prizeBg,
  qualificationBg,
  qualificationTextColor,
  orderLimitTextColor,
  progressBarBg,
  topTextColor,
  bottomTextColor,
  timeLimitedPrizeBg,
  timeLimitedTextColor,
  timeLimitedBtnBg,
  timeLimitedGrayBtn,
  choosePrizeTitleColor,
  prizeItemBg,
  prizeItemTitleBg,
  // prizeItemTitleColor,
  getPrizeBtn,
  noMorePrizeBtn,
  barrierBg,
  barrierGetBtn,
  showSkuBg,
  priceColor,
  btnToTop,
  preOrderBg,
  postOrderBg,
};
