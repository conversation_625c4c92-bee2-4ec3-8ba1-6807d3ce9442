<template>
  <div class="box">
    <div class='dialog'>
      <div class="title">- 领取权益 -</div>
      <div class="dialog_rule">
        <div>
          <div class="conten">恭喜您成功领取{{ detail.prizeName }}权益，您的卡密是：
              <div class="item" v-show="detail.cardPassword">
                <div class="text">{{ detail.cardPassword }}</div>
                <div class="copy-btn" :copy-text="detail.cardPassword">复制</div>
              </div>
            请前往兑换
          </div>
          <div class="tips">注：卡密充值请仔细核实充值账号，确认无误，本产品属于虚拟产品，官方明确充值成功后无法退换和转移。兑换手机号请确认已注册平台账号，否则激活失败不负责赔偿。</div>
          <div class="confirm process-btn" @click="isShowImageDialog=true"></div>
        </div>
<!--          <img :src="detail.prizeImg" alt="" class="card-img" />-->
<!--          <div class="prize-name">{{ detail.prizeName }}</div>-->
<!--          <div class="item" v-show="detail.cardNumber">-->
<!--            <div class="label">卡号：</div>-->
<!--            <div class="text">{{ detail.cardNumber }}</div>-->
<!--            <div class="copy-btn" :copy-text="detail.cardNumber">复制</div>-->
<!--          </div>-->
<!--          <div class="item" v-show="detail.cardPassword">-->
<!--            <div class="label">卡密：</div>-->
<!--            <div class="text">{{ detail.cardPassword }}</div>-->
<!--            <div class="copy-btn" :copy-text="detail.cardPassword">复制</div>-->
<!--          </div>-->
<!--        <div>-->
<!--          <div class="tip">礼品卡说明：</div>-->
<!--          <div class="tip" :class="{ 'tip-small': detail.cardNumber && detail.cardPassword }">{{ detail.cardDesc }}</div>-->
<!--        </div>-->
      </div>
    </div>
  </div>
  <ShowImage :showPopup="isShowImageDialog" :picUrl="detail.exchangeImg" @closeDialog="isShowImageDialog=false"></ShowImage>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';
import { CardType } from '../ts/type';
import Clipboard from 'clipboard';
import { showToast } from 'vant';
import ShowImage from './ShowImage.vue';

const isShowImageDialog = ref(false);

const props = defineProps({
  detail: {
    type: Object as PropType<CardType>,
    required: true,
  },
});

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};

console.log(';', props.detail);

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.box {

  .dialog {
    width: 6.9rem;
    margin: 0 auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/86305/9/53436/97905/66ea46e1F43562da9/eb5a79a1d662d187.png) no-repeat;
    height: 8rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.05rem 0.9rem;
    .title {
      font-size: 0.42rem;
      text-align: center;
      font-weight: bold;
      letter-spacing: 0.08rem;
      color: #375519;
      margin: 0 0 0.1rem;
    }
    .dialog_rule {
      max-height: 3.73rem;
      font-size: 0.21rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #375519;
      margin-top: 0.1rem;
      text-align: left;
      .conten {
        font-size: 0.26rem;
        font-weight: bold;
        word-wrap: break-word;
      }
      .tips {
        font-size: 0.22rem;
        padding: 0 0.1rem;
        text-align: left;
        box-sizing: border-box;
      }
      .confirm {
        width: 8rem;
        height: 2rem;
        margin-top: 0.2rem;
      }
        .process-btn {
        width: 1.8rem;
        margin: 0.1rem auto;
        background: url(//img10.360buyimg.com/imgzone/jfs/t1/173716/17/49846/8724/6721f720F7b8a47d9/ec326ccbc160e14b.png) no-repeat;
        background-size: 100%;
      }
    }
      .card-img {
        margin: 0 auto;
        width: 1.1rem;
      }
      .prize-name {
        color: #e2231a;
        width: 100%;
        text-align: center;
        font-weight: bolder;
        margin-top: 0.1rem;
        margin-bottom: 0.1rem;
        font-size: 0.24rem;
      }
      .item {
        display: flex;
        align-items: center;
        font-size: 0.24rem;
        margin-bottom: 0.1rem;
        .label {
          color: #ffce5c;
          width: 0.8rem;
        }
        .text {
          flex: 1;
          color: #ff5f00;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          border: 0.03rem solid #ffce5c;
          padding: 0.05rem 0.3125rem;
          line-height: 0.35rem;
          border-radius: 0.15rem 0 0 0.15rem;
        }
        .copy-btn {
          width: 1rem;
          line-height: 0.35rem;
          padding: 0.05rem 0;
          text-align: center;
          color: #fff;
          font-size: 0.24rem;
          border-radius: 0 0.15rem 0.15rem 0;
          border: 0.03rem solid #ffce5c;
          background-color: #ffce5c;
        }
      }
      .tip {
        width:5.1rem;
        font-size: 0.2rem;
        color: #a3a3a3;
        white-space: pre-line;
        max-height: 1.2rem;
        overflow-y: scroll;
      }
      .tip-small {
        width:5.1rem;
        max-height: 0.6rem;
      }
    .close {
      position: absolute;
      width: 0.6rem; // 0.6rem / 0.32
      height: 0.6rem; // 0.6rem / 0.32
      top: 0.65rem; // 0.65rem / 0.32
      right: 0.1rem; // 0.1rem / 0.32
    }
    .confirm {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/232777/14/24981/9192/66de5f6cFd88ab96e/404146b7df73b396.png) no-repeat;
      background-size: 100% 100%;
      width: 2.16rem;
      height: 0.66rem;
      margin: 0.1rem auto;
    }
  }
}
</style>
