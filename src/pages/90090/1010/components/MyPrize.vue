<template>
  <div class="box">
    <div class='dialog'>
      <div class="title">- 我的奖品 -</div>
      <div class="prize_list" v-if="prizes?.length > 0">
        <div class="prize_item prize_til">
          <div class="item">日期</div>
          <div class="item">奖品</div>
          <div class="item">状态</div>
        </div>
        <div class="prize_over">
          <div class="prize_item" v-for="item in prizes" :key="item.giftId">
            <div class="item">{{dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
            <div class="item">{{ item.prizeName }}</div>
            <div class="item" v-if="item.prizeType !== 3 && item.prizeType !== 7">
              <div v-if="item.status === 0">发放失败</div>
              <div v-else>已发放</div>
            </div>
            <div class="item" v-else-if="item.prizeType === 7">
              <div v-if="item.status === 0">发放失败</div>
              <div class="cardBtn" @click="showCardNum(item)" v-if="item.status === 1">兑换码</div>
            </div>
            <div class="item" v-else>
              <div v-if="!item.deliveryStatus && item.city && item.status !== 0">待发货</div>
              <div v-if="item.status === 0">发放失败</div>
              <div v-if="item.deliveryStatus && item.status !== 0">已发货</div>
              <div v-if="!item.city && item.status !== 0" @click="changAddress(item)">填写地址</div>
            </div>
          </div>
        </div>

      </div>
      <div class="no_data" v-else>暂无数据</div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :userReceiveRecordId="userReceiveRecordId" :activityPrizeId="activityPrizeId" :echoData="echoData" @close="closeSaveAddress" :prize-id="activityPrizeId"></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import Clipboard from 'clipboard';

const isPreview = inject('isPreview') as boolean;

const emits = defineEmits(['close', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  userReceiveRecordId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  // 状态 0允许报名  1 报名成功 2 取消报名 3 已发奖
  status: number;
  showOrder: boolean;
  orderList: {
    orderId: string;
    orderStatus: string;
    orderPrice: string;
  }[];
  deliverName: string;
  deliverNo: string;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/90090/getReceive');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
    prizes.forEach((item) => {
      item.showOrder = false;
    });
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

const showSaveAddress = ref(false);
const userReceiveRecordId = ref('');
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 修改地址
const changAddress = (item: any) => {

  userReceiveRecordId.value = item.userReceiveRecordId;
  activityPrizeId.value = item.userPrizeId;
  addressId.value = item.addressId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg, exchangeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, prizeImg, exchangeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  emits('savePhone', item.userReceiveRecordId, prizeContent.result.planDesc);
};

</script>

<style scoped lang="scss">
.box {

  .dialog {
    width: 6.67rem;
    background: url("https://img10.360buyimg.com/imgzone/jfs/t1/227345/13/27509/102377/66da7777F85c25ba6/d10d7399c5fa1b61.png") no-repeat;
    height:8rem;
    background-size: contain;
    box-sizing: border-box;
    padding: 1.2rem .57rem;
    color: #1b3f7d;

    .title {
      font-size: 0.37rem;
      text-align: center;
    }

    .prize_list {
      font-size: 0.27rem;
      margin-top: .13rem;

      .prize_til {
        background-color: #f0aa4a;
        color: #fff;
      }

      .prize_item {
        width: 100%;
        display: flex;
        line-height: 0.31rem;
        margin-bottom: 0.07rem;
        text-align: center;
        align-items: center;

        div.item {
          flex: 1;
          text-align: center;
          padding-bottom: 0.07rem;
          padding-top: 0.07rem;
          //.confirm {
          //  padding:0.07rem;
          //  width: 70%;
          //  margin-left: 8.5%;
          //}
        }
        .cardBtn {
          font-weight: 400;
          font-stretch: normal;
          line-height: 0.25rem;
          letter-spacing: 0.01rem;
          color: #d32e26;
          background-image: linear-gradient(180deg, #eaaf57, #fce2b4 50%, #ffedc7 61%, #dd9c43), linear-gradient(#fffaf2, #fffaf2);
          background-blend-mode: normal, normal;
          border-radius: 0.31rem;
          font-size: .3rem;
          padding: .1rem;
          width: 70%;
          margin-left: 18%;
        }
      }
      .prize_over {
        height: 3.1rem;
        overflow-y: scroll;
      }
}

.no_data {
  font-size: 0.33rem;
  text-align: center;
  margin-top: 0.33rem;
}
}

}

</style>
