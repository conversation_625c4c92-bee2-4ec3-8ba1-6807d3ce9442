<template>
  <div class="box">
    <div class="dialog">
      <div class="title">- 权益仅可领取{{ multiplePrizeCanReceiveNum }}次 -</div>
      <!-- <div class="subtitle">{{ multiplePrizeNum }}大权益任选其{{ multiplePrizeCanReceiveNum }}领取哦</div> -->
      <div class="subtitle">您确认领取{{ giftInfo.prizeName }}吗？</div>
      <div class="dialog_rule">
        <img class="prizeImg" :src="giftInfo.prizeImg" alt="" />
      </div>
      <div class="confirm" @click="confirmSub" />
    </div>
  </div>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress :addressId="addressId" @close="closeAddress" />
  </VanPopup>
  <!-- 展示卡密 -->
  <VanPopup teleport="body" v-model:show="copyCardPopup">
    <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { PropType, reactive, ref } from 'vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import CopyCard from './CopyCard.vue';

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const userReceiveRecordId = ref('');

interface PrizeType {
  prizeType: number;
  prizeName: string;
  prizeImg: string;
  result: any;
  prizeId: string;
  status: number;
  remainCount: number;
  sendTotalCount: number;
  area: number;
}
const props = defineProps({
  giftInfo: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
  multiplePrizeNum: {
    type: Number,
    default: 0,
  },
  multiplePrizeCanReceiveNum: {
    type: Number,
    default: 0,
  },
});
console.log('giftInfo', props.giftInfo);

const emits = defineEmits(['close', 'drawSuccess', 'upData']);

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  id: 1,
  prizeName: '',
  prizeImg: '',
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  exchangeImg: '',
});

// 展示卡密
const showCardNum = (distribute: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = distribute[item];
  });
  copyCardPopup.value = true;
};

const confirmSub = async () => {
  console.log(props.giftInfo.prizeId);
  try {
    const { data } = await httpRequest.post('/90090/receivePrize', { prizeId: props.giftInfo.prizeId });
    if (data.distribute.status) {
      closeToast();
      showToast('领取成功~');
      emits('upData');
      if (props.giftInfo.prizeType === 3) {
        addressId.value = data.addressId;
        setTimeout(() => {
          showSaveAddress.value = true;
        }, 2000);
      }
      if (props.giftInfo.prizeType === 7) {
        const card = {
          id: data.distribute.id,
          prizeName: data.distribute.prizeName,
          prizeImg: data.distribute.prizeImg,
          cardDesc: data.distribute.result.cardDesc,
          cardNumber: data.distribute.result.cardNumber,
          cardPassword: data.distribute.result.cardPassword,
          exchangeImg: data.distribute.exchangeImg,
        };
        setTimeout(() => {
          showCardNum(card);
        }, 2000);
        return;
      }
    }
    setTimeout(() => {
      emits('drawSuccess', data);
    }, 2000);
  } catch (e) {
    showToast(e);
  }
};

const closeAddress = () => {
  showSaveAddress.value = false;
  emits('close');
};
</script>

<style scoped lang="scss">
.box {
  .dialog {
    width: 6.9rem;
    margin: 0 auto;
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/227345/13/27509/102377/66da7777F85c25ba6/d10d7399c5fa1b61.png) no-repeat;
    height: 8rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.1rem 0.85rem;
    .title {
      font-size: 0.42rem;
      text-align: center;
      font-weight: bold;
      letter-spacing: 0.08rem;
      color: #1b3f7d;
      margin: 0.3rem 0 0.1rem;
    }
    .subtitle {
      font-size: 0.28rem;
      font-weight: bold;
      letter-spacing: 0.07rem;
      text-align: center;
      color: #1b3f7d;
    }
    .dialog_rule {
      max-height: 3.73rem;
      overflow-y: auto;
      font-size: 0.21rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #1b3f7d;
      margin-top: 0.1rem;
      text-align: left;
    }
    .prizeImg {
      //width: 2rem;
      height: 1.5rem;
      margin: 0.1rem auto;
    }
    .confirm {
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/232777/14/24981/9192/66de5f6cFd88ab96e/404146b7df73b396.png) no-repeat;
      background-size: 100% 100%;
      width: 2.16rem;
      height: 0.66rem;
      margin: 0 auto;
    }
  }
}
</style>
