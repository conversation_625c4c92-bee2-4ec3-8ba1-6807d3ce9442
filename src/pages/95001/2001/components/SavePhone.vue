<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>报名参与活动</div>
      <div class="rightLineDiv"></div>
      <img alt=""
           data-v-705393a4=""
           src="//img10.360buyimg.com/imgzone/jfs/t1/133343/17/19838/1158/5fd5c798E72ccdde6/51194de08ba1e3dc.png"
           class="close"
           @click="close" />
    </div>
    <div class="content">
      <div class="form">
        <VanField v-model="phone" required label="电话：" maxlength="11" type="number"></VanField>
      </div>
      <div class="submit" @click="checkForm">提交</div>
    </div>
  </div>

</template>

<script lang="ts" setup>
import { showToast, closeToast, showLoadingToast } from 'vant';
import { ref } from 'vue';
import { httpRequest } from '@/utils/service';

const emits = defineEmits(['close']);
const phone = ref('');
const close = () => {
  emits('close', true);
};

const submit = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/95001/application', {
      phone: phone.value,
    });
    closeToast();
    if (res.code === 200) {
      showToast('保存成功');
      emits('close', true);
    }
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

// 检查表单
const checkForm = () => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phone.value) {
    showToast('请输入电话');
  } else if (!checkPhone.test(phone.value)) {
    showToast('请输入正确的电话');
  } else {
    submit();
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 7rem;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
    border: 0.3rem solid transparent;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;

    .form {
      .van-cell {
        border-radius: 0.08rem;
        margin-bottom: 0.1rem;

        &::after {
          display: none;
        }
      }
    }

    .tip {
      font-size: 0.18rem;
      color: #b3b3b3;
    }

    .submit {
      margin-top: 0.6rem;
      text-align: center;
      font-size: 0.24rem;
      line-height: 0.6rem;
      color: #fff;
      background-color: #e2231a;
      height: 0.6rem;
    }
  }
}
</style>
