<template>
  <div class="">
    <div class="text">
      <div class="text-box">
        <img src="../assets/logo.png" alt="" class="logo" />
        <div>大奖擦身而过，</div>
        <div>伊利祝您新年健康喜乐。</div>
        <div>继续购买伊利商品，</div>
        <div>还可获得抽奖机会</div>
      </div>
    </div>
    <div class="btn-co">
      <img src="../assets/popup/noNumbtn.png" alt="" />
      <div class="btn" @click="toOrder"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { scrollToAnchor } from '../hooks/index';

const emit = defineEmits(['close']);

const toOrder = () => {
  emit('close');
  scrollToAnchor('buyGoods');
};
</script>

<style scoped lang="scss">
.text {
  height: 3.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  .text-box {
    .logo {
      width: 1rem;
      margin: 0 auto 0.2rem;
    }
    font-family: 'AlibabaPuHuiTi-Bold';
    text-align: center;
    font-size: 0.35rem;
    line-height: 0.5rem;
    letter-spacing: -0.02rem;
    color: #edca84;
    white-space: nowrap;
    display: block;
    background-size: 100% 100%;
  }
}
.btn-co {
  position: relative;
  width: 3.6rem;
  margin: 0 auto;
  img {
    width: 100%;
  }
  .btn {
    position: absolute;
    top: 0rem;
    right: 0.47rem;
    width: 2.74rem;
    height: 0.63rem;
  }
}
</style>
