/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024/4/8 14:55
 * Description:
 */
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

export default function useHttpRequest() {

  interface Options {
    duration?: number;
    isLoading?: boolean;
  }
  async function request(url: string, data?: any, options: Options = {
    isLoading: true,
  }): Promise<any> {
    try {
      if (options.isLoading) {
        showLoadingToast({
          forbidClick: true,
          duration: options.duration || 0,
        });
      }
      const response = await httpRequest.post(url, data);
      closeToast();
      return response;
    } catch (error: any) {
      showToast(error?.message||'请求失败');
      return null;
    }
  }
  async function getRequest(url: string): Promise<any> {
    try {
      showLoadingToast({
        forbidClick: true,
        duration: 0,
      });
      const response = await httpRequest.get(url);
      closeToast();
      return response;
    } catch (error: any) {
      return null;
    }
  }

  return {
    request,
    getRequest,
  };
}
