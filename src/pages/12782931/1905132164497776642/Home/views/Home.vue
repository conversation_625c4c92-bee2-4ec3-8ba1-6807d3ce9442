<template>
    <div class="main-view">
        <!-- 隐藏的 SVG 滤镜定义 -->
        <svg style="display: none;">
            <defs>
                <filter id="blackwhite-mask">
                    <feColorMatrix type="matrix" values="0.33 0.33 0.33 0 0.2
                                                         0.33 0.33 0.33 0 0.2
                                                         0.33 0.33 0.33 0 0.2
                                                         0 0 0 1 0"/>
                    <feComponentTransfer>
                        <feFuncR type="table" tableValues="0 1"/>
                        <feFuncG type="table" tableValues="0 1"/>
                        <feFuncB type="table" tableValues="0 1"/>
                    </feComponentTransfer>
                </filter>
            </defs>
        </svg>
        <img class="home-dog" :src="require(`../asset/homeDog.png`)"/>
        <img class="home-right-icon" :src="require(`../asset/免责声明.png`)" style="top: 0.5rem;" @click="isShowDisclaimers = true"/>
        <img class="home-right-icon" :src="require(`../asset/适用宠物.png`)" style="top: 1.8rem;" @click="isShowSuitableForPets = true"/>
        <div class="home-bottom-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="joinNow">立即参与</div>
        <div class="home-check-box">
            <van-checkbox v-model="isRead" :checked-color="PAGE_CONFIG.mainBgColor">我已阅读、知晓并同意本页面右上角免责声明</van-checkbox>
        </div>
        <div class="home-content-view" :style="`background-color: ${PAGE_CONFIG.mainBgColor};color: ${PAGE_CONFIG.mainTextColor}`">
            <div class="text-item" v-for="(item, index) in PAGE_CONFIG.homeTextList" :key="index">
                <text>{{ item.text }}</text>
                <text class="link-text" @click="isShowOralHealthIssues = true">{{ item.linkText }}</text>
            </div>
            <div class="bottom-text-box" :style="{ backgroundColor: PAGE_CONFIG.homeBottonLinkBg, color: PAGE_CONFIG.homeBottonLinkTextColor}" @click="openHomeLink">
                <div>点击此处了解更多关于汪汪爱牙宝背后的科学</div>
            </div>
        </div>
        <!-- <LoadingSpinner spinnerColor="#ff6347" spinnerSize="1"/> -->
        <VanPopup teleport="body" v-model:show="petList" position="center" :close-on-click-overlay="true">
            <div class="pet-popup">
                <img class="pet-close-icon close-icon" :src="require('../asset/closeIcon.png')" @click="petList = false"/>
                <div class="pet-content">
                    <div class="pet-title">选择宠物</div>
                    <template v-for="(it, index) in petListArr" :key="index">
                        <div class="pet-item" v-if="it === selectPet && it.petType == 1" :style="`background-color: ${PAGE_CONFIG.mainBgColor};color: ${PAGE_CONFIG.mainTextColor}`">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../asset/defaultDogImg.jpg`)"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../asset/sex-m.png`)" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../asset/sex-f.png`)"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petBreed}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../asset/zhuazi.png`)"/>
                        </div>
                        <div class="pet-item" v-else-if="it !== selectPet && it.petType == 1" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`" @click="selectPet = it">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../asset/defaultDogImg.jpg`)"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../asset/sex-m-red.png`)" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../asset/sex-f-red.png`)"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petBreed}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../asset/zhuazi-red.png`)"/>
                        </div>
                        <div class="pet-item" v-else style="filter: url(#blackwhite-mask);" :style="`border-color: ${PAGE_CONFIG.mainBgColor};`">
                            <div class="pet-img">
                                <img v-if="it.petAvatar" :src="it.petAvatar"/>
                                <img v-else :src="require(`../asset/defaultDogImg.jpg`)"/>
                            </div>
                            <div class="pet-info-wrapper">
                                <div class="row-1-wrap">
                                    <text>{{it.petNick}}</text>
                                    <img v-if="it.petGender === 1" :src="require(`../asset/sex-m.png`)" />
                                    <img v-else-if="it.petGender === 2" :src="require(`../asset/sex-f.png`)"/>
                                </div>
                                <div class="row-2-wrap">
                                    <text>{{it.petBreed}}</text>
                                    <!-- <div>{{it.ageStr}}</div> -->
                                </div>
                                <!-- <text class="row-3-wrap">{{it.personAge}}</text> -->
                                </div>
                            <img class="pet-icon" :src="require(`../asset/zhuazi-red.png`)"/>
                        </div>
                    </template>
                    <img class="pet-add-btn" v-if="petListArr.length < 5" :src="require(`../asset/addPet.png`)" @click="emits('toggleComponent', 'CreatePage');"/>
                    <div class="pet-confirm-btn" v-if="selectPet" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor}`" @click="confirmPet">确认宠物</div>
                </div>
            </div>
        </VanPopup>
        <Disclaimers :isShow="isShowDisclaimers" @closePopup="closeDisclaimers"/>
        <OralHealthIssues :isShow="isShowOralHealthIssues" @closePopup="isShowOralHealthIssues = false"/>
        <SuitableForPets :isShow="isShowSuitableForPets" @closePopup="isShowSuitableForPets = false"/>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import LoadingSpinner from '../components/LoadingSpinner.vue';
import Disclaimers from '../components/Disclaimers.vue';
import OralHealthIssues from '../components/OralHealthIssues.vue';
import SuitableForPets from '../components/SuitableForPets.vue';
import { emit } from 'process';
import { getPetList } from '../config/api';
import { showFailToast, Checkbox, CheckboxGroup } from 'vant';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

const store = useStore<RootState>();
interface PetItem {
    id: number;
    petAvatar: string;
    petNick: string;
    petType: number; // 1'dog' | 2'cat'
    petBreed: string;
    petGender: number; // 0:未知，1：公，2：母
}

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG: any = inject('PAGE_CONFIG');

const petList = ref(false);
const isRead = ref(false);

const isShowDisclaimers = ref(false);
const isShowOralHealthIssues = ref(false);
const isShowSuitableForPets = ref(false);
const emits = defineEmits(['toggleComponent']);
const closeDisclaimers = () => {
  isShowDisclaimers.value = false;
};
const joinNow = () => {
  lzReportClick('startCheck');
  if (baseUserInfo.level === 0) {
    window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
    return;
  }
  if (!isRead.value) {
    showFailToast('请阅读、知晓并同意本页面右上角免责声明');
    return;
  }
  if (baseInfo.shopId === '**********') {
    petList.value = true;
  } else {
    const getHost = window.location.href.split('/custom')[0];
    window.location.href = `${getHost}/custom/12782931/1905132164497776642/Photo/`;
  }
};

const petListArr = ref<PetItem[]>([]);
const selectPet = ref();
const confirmPet = () => {
  if (!selectPet.value) {
    showFailToast('请选择宠物');
    return;
  }
  console.log('=>(App.vue:59) selectPet', selectPet.value);
  petList.value = false;
  store.commit('setPetInfo', selectPet.value);
  const getHost = window.location.href.split('/custom')[0];
  window.location.href = `${getHost}/custom/12782931/1905132164497776642/Photo/?petNick=${selectPet.value.petNick}&petId=${selectPet.value.id}`;
};
const openHomeLink = () => {
  window.open(PAGE_CONFIG.homeLink);
};
const init = () => {
  console.log(store);
  getPetList().then((res) => {
    // console.log(res);
    if (res.data && res.data.length > 0) {
      // 使用数组解构赋值，并确保数组不为空
      [selectPet.value] = res.data; // 解构赋值
      petListArr.value = res.data;
    } else {
      // 如果数据为空，初始化为空值或默认值
      selectPet.value = undefined;
      petListArr.value = [];
    }
  });
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
<style lang="scss" scoped>
</style>
